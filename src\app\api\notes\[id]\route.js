import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

// Update a note
export async function PATCH(request, { params }) {
  try {
    const body = await request.json();
    const { title, content } = body;

    const { id } = await params;

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Get the existing note for audit log
    const existingNote = await prisma.note.findUnique({
      where: { id }
    });

    if (!existingNote) {
      return NextResponse.json(
        { error: 'Note not found' },
        { status: 404 }
      );
    }

    const updatedNote = await prisma.note.update({
      where: { id },
      data: {
        ...(title !== undefined && { title }),
        ...(content !== undefined && { content }),
        updatedAt: new Date()
      }
    });

    // Log the note update
    await auditLogger.logNoteUpdate({
      userId: user?.id,
      noteId: id,
      oldValues: existingNote,
      newValues: updatedNote,
      request
    });

    return NextResponse.json(updatedNote);
  } catch (error) {
    console.error('Failed to update note:', error);
    return NextResponse.json(
      { error: 'Failed to update note: ' + error.message },
      { status: 500 }
    );
  }
}

// Delete a note
export async function DELETE(request, { params }) {
  try {
    const { id } = await params;

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Get the existing note for audit log
    const existingNote = await prisma.note.findUnique({
      where: { id }
    });

    if (!existingNote) {
      return NextResponse.json(
        { error: 'Note not found' },
        { status: 404 }
      );
    }

    // Log the note deletion
    await auditLogger.logNoteDelete({
      userId: user?.id,
      noteId: id,
      oldValues: existingNote,
      request
    });

    await prisma.note.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete note:', error);
    return NextResponse.json(
      { error: 'Failed to delete note: ' + error.message },
      { status: 500 }
    );
  }
}
