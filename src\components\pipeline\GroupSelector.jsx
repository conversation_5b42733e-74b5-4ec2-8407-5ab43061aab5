"use client";

import { useState, useEffect, useRef } from "react";
import { Users, X } from "lucide-react";

export default function GroupSelector({ onGroupSelect, onClearSelection }) {
  const [groups, setGroups] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const selectRef = useRef(null);

  useEffect(() => {
    const fetchGroups = async () => {
      setIsLoading(true);
      try {
        // Fetch groups with member counts
        const response = await fetch("/api/groups?includeMemberCounts=true");
        if (response.ok) {
          const data = await response.json();
          console.log("Groups with member counts:", data);
          setGroups(data);
        }
      } catch (error) {
        console.error("Failed to fetch groups:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGroups();
  }, []);

  const handleGroupChange = (e) => {
    const groupId = e.target.value;
    if (groupId && groupId !== "default") {
      const selectedGroupData = groups.find((g) => g.id === groupId);
      setSelectedGroup(selectedGroupData);
      onGroupSelect(groupId);
    }
  };

  const handleClearSelection = () => {
    setSelectedGroup(null);
    if (selectRef.current) {
      selectRef.current.value = "default";
    }
    if (onClearSelection) {
      onClearSelection();
    }
  };

  return (
    <div className="flex items-center">
      <Users className="h-4 w-4 text-gray-500 mr-2" />
      {selectedGroup ? (
        <div className="flex items-center bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light px-2 py-1 rounded-md">
          <span className="text-sm">
            {selectedGroup.name} ({selectedGroup.memberCount || 0} contacts)
          </span>
          <button
            onClick={handleClearSelection}
            className="ml-2 text-primary/70 hover:text-primary dark:text-primary-light/70 dark:hover:text-primary-light focus:outline-none"
            title="Clear selection"
          >
            <X className="h-3.5 w-3.5" />
          </button>
        </div>
      ) : (
        <select
          ref={selectRef}
          onChange={handleGroupChange}
          className="text-sm rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 py-1"
          defaultValue="default"
          disabled={isLoading}
        >
          <option value="default" disabled>
            Select a group
          </option>
          {groups.map((group) => (
            <option key={group.id} value={group.id}>
              {group.name} ({group.memberCount || 0} contacts)
            </option>
          ))}
          {groups.length === 0 && !isLoading && (
            <option disabled>No groups found</option>
          )}
        </select>
      )}
      {isLoading && (
        <div className="ml-2 animate-spin h-4 w-4 border-2 border-gray-300 dark:border-gray-600 border-t-primary rounded-full"></div>
      )}
    </div>
  );
}
