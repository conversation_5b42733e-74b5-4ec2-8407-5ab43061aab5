'use client';

import { useState, useEffect, useRef } from 'react';
import { Info, AlertCircle } from 'lucide-react';
import { dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import ContactCard from './ContactCard';
import Tooltip from '@/components/ui/Tooltip';
import './pipeline-styles.css';
import { getActionTypeById } from '@/lib/constants/stageActionTypes';

export default function PipelineColumn({
  stage,
  contacts,
  onLoadMore,
  isMultiSelectMode = false,
  selectedContacts = [],
  onContactSelect
}) {
  const [stageActions, setStageActions] = useState([]);
  const [isLoadingActions, setIsLoadingActions] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const scrollRef = useRef(null);

  // Set up drop target
  useEffect(() => {
    if (!scrollRef.current || isMultiSelectMode) return;

    const cleanup = dropTargetForElements({
      element: scrollRef.current,
      getData: () => ({ stageId: stage.id }),
      onDragEnter: () => {
        if (scrollRef.current) {
          scrollRef.current.classList.add('bg-blue-50/50', 'dark:bg-blue-900/20');
        }
      },
      onDragLeave: () => {
        if (scrollRef.current) {
          scrollRef.current.classList.remove('bg-blue-50/50', 'dark:bg-blue-900/20');
        }
      },
      onDrop: () => {
        if (scrollRef.current) {
          scrollRef.current.classList.remove('bg-blue-50/50', 'dark:bg-blue-900/20');
        }
      }
    });

    return cleanup;
  }, [stage.id, isMultiSelectMode]);

  // Fetch actions for this stage
  useEffect(() => {
    const fetchStageActions = async () => {
      if (!stage.id) return;

      setIsLoadingActions(true);
      try {
        const response = await fetch(`/api/pipeline/stages/${stage.id}/actions`);
        if (response.ok) {
          const actions = await response.json();
          setStageActions(actions);
        }
      } catch (error) {
        console.error('Failed to fetch stage actions:', error);
      } finally {
        setIsLoadingActions(false);
      }
    };

    fetchStageActions();
  }, [stage.id]);

  const handleScroll = (e) => {
    if (isLoadingMore || !hasMore) return;

    const { scrollTop, scrollHeight, clientHeight } = e.target;
    // Load more when user scrolls to 80% of the way down
    if (scrollHeight - scrollTop <= clientHeight * 1.2) {
      loadMoreContacts();
    }
  };

  const loadMoreContacts = async () => {
    if (isLoadingMore || !hasMore || !onLoadMore) return;

    setIsLoadingMore(true);
    const nextPage = currentPage + 1;

    try {
      const result = await onLoadMore(nextPage);

      if (!result.contacts || result.contacts.length === 0) {
        setHasMore(false);
      } else {
        setCurrentPage(nextPage);
      }
    } catch (error) {
      console.error('Failed to load more contacts:', error);
      setHasMore(false);
    } finally {
      setIsLoadingMore(false);
    }
  };

  return (
    <div className="pipeline-column flex-none w-64 md:w-72 h-full flex flex-col bg-gray-200 dark:bg-gray-800 rounded-lg shadow-md border border-gray-300 dark:border-gray-700 transition-shadow duration-200">
      {/* Column Header */}
      <div className="p-4 border-b border-gray-300 dark:border-gray-700 bg-gray-300 dark:bg-gray-700 rounded-t-lg">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold text-gray-800 dark:text-white">{stage.name}</h3>
            <span className="px-2 py-0.5 text-xs font-medium bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full">
              {stage.contactCount || contacts.length}
            </span>
            {stageActions.length > 0 ? (
              <Tooltip content={
                <div className="w-48">
                  <p className="font-medium mb-1">Automated actions:</p>
                  <ul className="space-y-1">
                    {stageActions.map(action => {
                      const actionType = getActionTypeById(action.actionType);
                      return (
                        <li key={action.id} className="flex items-center">
                          <span className="mr-1">{actionType.icon}</span>
                          <span>{actionType.label}</span>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              }>
                <div className="text-blue-500 dark:text-blue-400 cursor-help">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </Tooltip>
            ) : (
              <Tooltip content="This stage has no automated actions configured. You can add actions in the Manage Stages dialog.">
                <div className="text-gray-500 dark:text-gray-400 cursor-help">
                  <Info className="h-4 w-4" />
                </div>
              </Tooltip>
            )}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-300">
            {stage.order && `Stage ${stage.order}`}
          </div>
        </div>
      </div>

      {/* Column Content - Scrollable */}
      <div
        ref={scrollRef}
        data-stage-id={stage.id}
        className="pipeline-scroll flex-1 overflow-y-auto p-3 space-y-3 transition-colors bg-white dark:bg-gray-800/80"
        style={{ minHeight: '100px' }}
        onScroll={handleScroll}
      >
            {contacts.length === 0 && (
              <div className="h-full flex items-center justify-center text-center p-4">
                <div className="flex flex-col items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-gray-300 dark:text-gray-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <p className="text-sm text-gray-500 dark:text-gray-400">No contacts in this stage</p>
                  <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">Drag contacts here</p>
                </div>
              </div>
            )}

            {contacts && contacts.length > 0 ? (
              contacts.map((contact, index) => (
                <div key={contact.id} className="contact-card-wrapper">
                  <ContactCard
                    contact={contact}
                    isDragging={false}
                    isMultiSelectMode={isMultiSelectMode}
                    isSelected={selectedContacts.includes(contact.id)}
                    onSelect={onContactSelect}
                  />
                </div>
              ))
            ) : (
              <div className="text-center p-4 text-gray-500 dark:text-gray-400">
                <p>No contacts in this stage</p>
              </div>
            )}
            {isLoadingMore && (
              <div className="py-2 text-center text-gray-500 dark:text-gray-400">
                <div className="inline-block animate-spin h-4 w-4 border-2 border-gray-300 dark:border-gray-600 border-t-primary rounded-full mr-2"></div>
                Loading more...
              </div>
            )}
      </div>
    </div>
  );
}