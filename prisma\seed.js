const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Helper function to generate random dates within a range
function randomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// Helper function to pick a random item from an array
function randomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Helper function to generate a random phone number
function randomPhone() {
  return `(${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`;
}

async function main() {
  console.log('Starting database seed...');

  // Get or create the admin user (current user)
  let adminUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  if (!adminUser) {
    adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: '<PERSON><PERSON> Admin',
        azureId: 'gwitech-azure-id-12345',
        role: 'admin',
        permissions: [
          'read:contacts',
          'write:contacts',
          'delete:contacts',
          'read:groups',
          'write:groups',
          'delete:groups',
          'read:tasks',
          'write:tasks',
          'delete:tasks',
          'read:notes',
          'write:notes',
          'delete:notes',
          'read:documents',
          'write:documents',
          'delete:documents',
          'read:pipeline',
          'write:pipeline',
          'read:metrics',
          'read:email_campaigns',
          'write:email_campaigns',
          'send:emails'
        ]
      }
    });
    console.log('Created admin user with admin role:', adminUser.name);
  } else {
    // Update the existing user with roles and permissions if they don't have them
    if (!adminUser.role || !adminUser.permissions) {
      adminUser = await prisma.user.update({
        where: { id: adminUser.id },
        data: {
          role: 'admin',
          permissions: [
            'read:contacts',
            'write:contacts',
            'delete:contacts',
            'read:groups',
            'write:groups',
            'delete:groups',
            'read:tasks',
            'write:tasks',
            'delete:tasks',
            'read:notes',
            'write:notes',
            'delete:notes',
            'read:documents',
            'write:documents',
            'delete:documents',
            'read:pipeline',
            'write:pipeline',
            'read:metrics',
            'read:email_campaigns',
            'write:email_campaigns',
            'send:emails'
          ]
        }
      });
      console.log('Updated admin user with admin role and permissions:', adminUser.name);
    } else {
      console.log('Admin user already exists with role:', adminUser.role);
    }
  }

  // Create a regular user with limited permissions if it doesn't exist
  let regularUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  if (!regularUser) {
    regularUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Regular User',
        azureId: 'regular-user-azure-id-67890',
        role: 'user',
        permissions: [
          'read:contacts',
          'write:contacts',
          'read:groups',
          'read:tasks',
          'write:tasks',
          'read:notes',
          'write:notes',
          'read:documents',
          'read:pipeline',
          'read:metrics'
        ]
      }
    });
    console.log('Created regular user with limited permissions:', regularUser.name);
  } else {
    // Update the existing user with roles and permissions if they don't have them
    if (!regularUser.role || !regularUser.permissions) {
      regularUser = await prisma.user.update({
        where: { id: regularUser.id },
        data: {
          role: 'user',
          permissions: [
            'read:contacts',
            'write:contacts',
            'read:groups',
            'read:tasks',
            'write:tasks',
            'read:notes',
            'write:notes',
            'read:documents',
            'read:pipeline',
            'read:metrics'
          ]
        }
      });
      console.log('Updated regular user with limited permissions:', regularUser.name);
    } else {
      console.log('Regular user already exists with role:', regularUser.role);
    }
  }

  // Create pipeline stages
  const stages = [
    { name: 'Lead', order: 1 },
    { name: 'Meeting Scheduled', order: 2 },
    { name: 'Proposal Sent', order: 3 },
    { name: 'Negotiation', order: 4 },
    { name: 'Closed Won', order: 5 },
    { name: 'Closed Lost', order: 6 }
  ];

  console.log('Seeding pipeline stages...');

  const createdStages = [];
  for (const stage of stages) {
    const existingStage = await prisma.pipelineStage.findFirst({
      where: { name: stage.name }
    });

    if (!existingStage) {
      const newStage = await prisma.pipelineStage.create({
        data: stage
      });
      createdStages.push(newStage);
      console.log(`Created stage: ${stage.name}`);
    } else {
      createdStages.push(existingStage);
      console.log(`Stage already exists: ${stage.name}`);
    }
  }

  // Create sample stage actions
  console.log('Creating sample stage actions...');

  // For the "Meeting Scheduled" stage
  const meetingStage = createdStages.find(s => s.name === 'Meeting Scheduled');
  if (meetingStage) {
    const existingAction = await prisma.stageAction.findFirst({
      where: { stageId: meetingStage.id }
    });

    if (!existingAction) {
      await prisma.stageAction.create({
        data: {
          stageId: meetingStage.id,
          actionType: 'task',
          actionDetails: {
            title: 'Schedule initial meeting',
            description: 'Reach out to the contact and schedule an initial meeting',
            priority: 'high',
            dueDate: null // Will be set relative to when contact enters this stage
          }
        }
      });
      console.log('Created task action for Meeting Scheduled stage');
    }
  }

  // For the "Proposal Sent" stage
  const proposalStage = createdStages.find(s => s.name === 'Proposal Sent');
  if (proposalStage) {
    const existingAction = await prisma.stageAction.findFirst({
      where: { stageId: proposalStage.id }
    });

    if (!existingAction) {
      await prisma.stageAction.create({
        data: {
          stageId: proposalStage.id,
          actionType: 'activity',
          actionDetails: {
            type: 'email',
            description: 'Proposal sent to client'
          }
        }
      });
      console.log('Created activity action for Proposal Sent stage');
    }
  }

  // Create contact groups
  console.log('Creating contact groups...');

  const groupData = [
    { name: 'VIP Clients', description: 'High-value clients', color: '#FF5733' },
    { name: 'Prospects', description: 'Potential new clients', color: '#33FF57' },
    { name: 'Referral Partners', description: 'Partners who refer clients', color: '#3357FF' }
  ];

  const createdGroups = [];
  for (const group of groupData) {
    const existingGroup = await prisma.contactGroup.findFirst({
      where: {
        name: group.name,
        userId: adminUser.id
      }
    });

    if (!existingGroup) {
      const newGroup = await prisma.contactGroup.create({
        data: {
          ...group,
          userId: adminUser.id
        }
      });
      createdGroups.push(newGroup);
      console.log(`Created group: ${group.name}`);
    } else {
      createdGroups.push(existingGroup);
      console.log(`Group already exists: ${group.name}`);
    }
  }

  // Create distribution lists
  console.log('Creating distribution lists...');

  const listData = [
    { name: 'Newsletter', description: 'Monthly newsletter recipients' },
    { name: 'Event Invitations', description: 'Contacts to invite to events' },
    { name: 'VIP Clients', description: 'High-value clients for special communications' },
    { name: 'Prospects', description: 'Potential clients in the sales pipeline' },
    { name: 'Partners', description: 'Business partners and referral sources' },
    { name: 'Product Updates', description: 'Contacts interested in product announcements' },
    { name: 'Webinar Attendees', description: 'Contacts who attend webinars' },
    { name: 'Trial Users', description: 'Users currently on trial' },
    { name: 'Customer Success', description: 'Existing customers for success communications' },
    { name: 'Marketing Qualified Leads', description: 'Leads qualified by marketing team' },
    { name: 'Sales Qualified Leads', description: 'Leads qualified by sales team' },
    { name: 'Industry - Technology', description: 'Contacts in technology industry' },
    { name: 'Industry - Healthcare', description: 'Contacts in healthcare industry' },
    { name: 'Industry - Finance', description: 'Contacts in finance industry' },
    { name: 'Cold Outreach', description: 'Contacts for cold email campaigns' }
  ];

  const createdLists = [];
  for (const list of listData) {
    const existingList = await prisma.distributionList.findFirst({
      where: {
        name: list.name,
        userId: adminUser.id
      }
    });

    if (!existingList) {
      const newList = await prisma.distributionList.create({
        data: {
          ...list,
          userId: adminUser.id,
          contactIds: [],
          groupIds: []
        }
      });
      createdLists.push(newList);
      console.log(`Created distribution list: ${list.name}`);
    } else {
      createdLists.push(existingList);
      console.log(`Distribution list already exists: ${list.name}`);
    }
  }

  // Create sample contacts
  console.log('Creating sample contacts...');

  const contactData = [
    {
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
      company: 'Acme Corp',
      type: 'customer',
      pipelineStage: createdStages.find(s => s.name === 'Closed Won')?.id
    },
    {
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      company: 'XYZ Industries',
      type: 'lead',
      pipelineStage: createdStages.find(s => s.name === 'Lead')?.id
    },
    {
      firstName: 'Michael',
      lastName: 'Brown',
      email: '<EMAIL>',
      company: 'Brown Consulting',
      type: 'partner',
      pipelineStage: createdStages.find(s => s.name === 'Negotiation')?.id
    },
    {
      firstName: 'Emily',
      lastName: 'Davis',
      email: '<EMAIL>',
      company: 'Davis & Associates',
      type: 'lead',
      pipelineStage: createdStages.find(s => s.name === 'Meeting Scheduled')?.id
    },
    {
      firstName: 'Robert',
      lastName: 'Wilson',
      email: '<EMAIL>',
      company: 'Wilson Technologies',
      type: 'customer',
      pipelineStage: createdStages.find(s => s.name === 'Closed Won')?.id
    },
    {
      firstName: 'Jennifer',
      lastName: 'Lee',
      email: '<EMAIL>',
      company: 'Lee Enterprises',
      type: 'lead',
      pipelineStage: createdStages.find(s => s.name === 'Proposal Sent')?.id
    },
    {
      firstName: 'David',
      lastName: 'Martinez',
      email: '<EMAIL>',
      company: 'Martinez Group',
      type: 'customer',
      pipelineStage: createdStages.find(s => s.name === 'Closed Won')?.id
    },
    {
      firstName: 'Lisa',
      lastName: 'Taylor',
      email: '<EMAIL>',
      company: 'Taylor Financial',
      type: 'lead',
      pipelineStage: createdStages.find(s => s.name === 'Lead')?.id
    },
    {
      firstName: 'James',
      lastName: 'Anderson',
      email: '<EMAIL>',
      company: 'Anderson & Co',
      type: 'partner',
      pipelineStage: createdStages.find(s => s.name === 'Negotiation')?.id
    },
    {
      firstName: 'Patricia',
      lastName: 'Thomas',
      email: '<EMAIL>',
      company: 'Thomas Solutions',
      type: 'lead',
      pipelineStage: createdStages.find(s => s.name === 'Meeting Scheduled')?.id
    }
  ];

  const createdContacts = [];
  for (const contact of contactData) {
    const existingContact = await prisma.contact.findFirst({
      where: {
        email: contact.email,
        userId: adminUser.id
      }
    });

    if (!existingContact) {
      const now = new Date();
      const pastDate = new Date(now);
      pastDate.setMonth(pastDate.getMonth() - 3);

      const newContact = await prisma.contact.create({
        data: {
          ...contact,
          phone: randomPhone(),
          userId: adminUser.id,
          lastContactDate: randomDate(pastDate, now),
          distributionListIds: []
        }
      });
      createdContacts.push(newContact);
      console.log(`Created contact: ${contact.firstName} ${contact.lastName}`);
    } else {
      createdContacts.push(existingContact);
      console.log(`Contact already exists: ${contact.firstName} ${contact.lastName}`);
    }
  }

  // Create additional random contacts
  console.log('Creating additional random contacts...');

  const contactTypes = ['Lead', 'Prospect', 'Client', 'Partner'];
  const sources = ['Website', 'Referral', 'Cold Call', 'Social Media', 'Event', 'Email Campaign', 'LinkedIn', 'Trade Show', 'Partner Referral', 'Google Ads'];
  const industries = ['Technology', 'Healthcare', 'Finance', 'Real Estate', 'Manufacturing', 'Retail', 'Education', 'Consulting', 'Legal', 'Marketing', 'Construction', 'Hospitality', 'Transportation', 'Energy', 'Media', 'Non-Profit'];

  const companies = [
    'Tech Solutions Inc', 'Healthcare Partners', 'Financial Advisors LLC', 'Real Estate Group',
    'Manufacturing Corp', 'Retail Dynamics', 'Education First', 'Consulting Experts',
    'Innovation Labs', 'Digital Marketing Co', 'Investment Partners', 'Property Management',
    'Software Development', 'Medical Center', 'Banking Solutions', 'Construction Co',
    'Global Logistics', 'Creative Agency', 'Data Analytics Inc', 'Cloud Services Ltd',
    'Mobile Apps Co', 'E-commerce Solutions', 'Cybersecurity Firm', 'AI Research Lab',
    'Green Energy Corp', 'Biotech Innovations', 'Legal Associates', 'Marketing Pros',
    'Design Studio', 'Event Planning Co', 'Travel Services', 'Food & Beverage Inc',
    'Fitness Solutions', 'Pet Care Services', 'Home Improvement', 'Auto Dealership',
    'Insurance Brokers', 'Accounting Firm', 'HR Solutions', 'Training Institute',
    'Publishing House', 'Music Production', 'Film Studio', 'Gaming Company',
    'Fashion Brand', 'Beauty Products', 'Sports Equipment', 'Outdoor Gear',
    'Electronics Store', 'Furniture Design', 'Interior Decorating', 'Landscaping Co'
  ];

  const firstNames = [
    'Alexander', 'Amanda', 'Benjamin', 'Stephanie', 'Gregory', 'Rebecca', 'Patrick', 'Rachel',
    'Jack', 'Carolyn', 'Dennis', 'Janet', 'Jerry', 'Virginia', 'Tyler', 'Maria',
    'Aaron', 'Heather', 'Jose', 'Diane', 'Henry', 'Julie', 'Adam', 'Joyce',
    'Douglas', 'Victoria', 'Nathan', 'Kelly', 'Peter', 'Christina', 'Zachary', 'Joan',
    'Kyle', 'Frances', 'Noah', 'Evelyn', 'William', 'Jean', 'Mason', 'Cheryl',
    'Ethan', 'Megan', 'Lucas', 'Kathryn', 'Jacob', 'Janice', 'Logan', 'Catherine',
    'Caleb', 'Frances', 'Oliver', 'Samantha', 'Eli', 'Debra', 'Owen', 'Rachel',
    'Luke', 'Carolyn', 'Carter', 'Janet', 'Wyatt', 'Virginia', 'John', 'Maria'
  ];

  const lastNames = [
    'Cruz', 'Edwards', 'Collins', 'Reyes', 'Stewart', 'Morris', 'Morales', 'Murphy',
    'Cook', 'Rogers', 'Gutierrez', 'Ortiz', 'Morgan', 'Cooper', 'Peterson', 'Bailey',
    'Reed', 'Kelly', 'Howard', 'Ramos', 'Kim', 'Cox', 'Ward', 'Richardson',
    'Watson', 'Brooks', 'Chavez', 'Wood', 'James', 'Bennett', 'Gray', 'Mendoza',
    'Ruiz', 'Hughes', 'Price', 'Alvarez', 'Castillo', 'Sanders', 'Patel', 'Myers',
    'Long', 'Ross', 'Foster', 'Jimenez', 'Powell', 'Jenkins', 'Perry', 'Russell',
    'Sullivan', 'Bell', 'Coleman', 'Butler', 'Henderson', 'Barnes', 'Gonzales', 'Fisher'
  ];

  const jobTitles = [
    'CEO', 'CTO', 'CFO', 'COO', 'VP Sales', 'VP Marketing', 'VP Operations', 'VP Engineering',
    'Director of Sales', 'Director of Marketing', 'Director of Operations', 'Director of HR',
    'Sales Manager', 'Marketing Manager', 'Operations Manager', 'Project Manager',
    'Account Executive', 'Account Manager', 'Business Development Manager', 'Product Manager',
    'Senior Developer', 'Lead Developer', 'Software Engineer', 'Data Analyst',
    'Business Analyst', 'Financial Analyst', 'Marketing Specialist', 'Sales Representative',
    'Customer Success Manager', 'Office Manager', 'Administrative Assistant', 'Consultant',
    'Senior Consultant', 'Principal', 'Partner', 'Founder', 'Co-Founder', 'President'
  ];

  const cities = [
    'Austin', 'Jacksonville', 'Fort Worth', 'Columbus', 'Charlotte', 'San Francisco',
    'Indianapolis', 'Seattle', 'Denver', 'Washington', 'Boston', 'El Paso', 'Nashville', 'Detroit',
    'Oklahoma City', 'Portland', 'Las Vegas', 'Memphis', 'Louisville', 'Baltimore', 'Milwaukee', 'Albuquerque',
    'Tucson', 'Fresno', 'Sacramento', 'Kansas City', 'Mesa', 'Atlanta', 'Omaha', 'Colorado Springs',
    'Raleigh', 'Miami', 'Virginia Beach', 'Oakland', 'Minneapolis', 'Tulsa', 'Arlington', 'Tampa'
  ];

  const states = [
    'TX', 'FL', 'TX', 'OH', 'NC', 'CA', 'IN', 'WA', 'CO', 'DC', 'MA', 'TX', 'TN', 'MI',
    'OK', 'OR', 'NV', 'TN', 'KY', 'MD', 'WI', 'NM', 'AZ', 'CA', 'CA', 'MO', 'AZ', 'GA',
    'NE', 'CO', 'NC', 'FL', 'VA', 'CA', 'MN', 'OK', 'TX', 'FL'
  ];

  // Create 100 additional contacts
  for (let i = 0; i < 100; i++) {
    const firstName = randomItem(firstNames);
    const lastName = randomItem(lastNames);
    const company = randomItem(companies);
    const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${company.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '')}.com`;
    const cityIndex = Math.floor(Math.random() * cities.length);

    const existingContact = await prisma.contact.findFirst({
      where: {
        email: email,
        userId: adminUser.id
      }
    });

    if (!existingContact) {
      const now = new Date();
      const pastDate = new Date(now);
      pastDate.setMonth(pastDate.getMonth() - 6);

      const contact = await prisma.contact.create({
        data: {
          firstName,
          lastName,
          email,
          phone: randomPhone(),
          company,
          jobTitle: randomItem(jobTitles),
          type: randomItem(contactTypes),
          leadSource: randomItem(sources),
          addressLine1: `${Math.floor(Math.random() * 9999) + 1} ${randomItem(['Main', 'Oak', 'Pine', 'Maple', 'Cedar', 'Elm', 'First', 'Second', 'Park', 'Broadway'])} ${randomItem(['St', 'Ave', 'Blvd', 'Dr', 'Ln', 'Way', 'Ct', 'Pl'])}`,
          city: cities[cityIndex],
          state: states[cityIndex],
          postalCode: `${Math.floor(Math.random() * 90000) + 10000}`,
          country: 'United States',
          website: `https://www.${company.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '')}.com`,
          notesInternal: `Contact created during seed process. ${randomItem(['Very interested in our services', 'Potential high-value client', 'Referred by existing client', 'Met at industry conference', 'Responded to marketing campaign', 'Cold outreach prospect', 'Warm lead from website', 'Trade show connection', 'LinkedIn connection', 'Partner referral'])}`,
          tags: [randomItem(['VIP', 'Hot Lead', 'Follow Up', 'Qualified', 'Interested', 'Cold Lead', 'Warm Lead', 'Decision Maker', 'Influencer', 'Champion'])],
          pipelineStage: randomItem(createdStages).id,
          lastContactDate: randomDate(pastDate, now),
          userId: adminUser.id,
          distributionListIds: [],
          createdAt: randomDate(pastDate, now),
          updatedAt: randomDate(pastDate, now)
        }
      });

      createdContacts.push(contact);
      if (i % 20 === 0) {
        console.log(`Created ${i + 1} additional contacts...`);
      }
    }
  }

  console.log(`Created ${createdContacts.length} total contacts`);

  // Add contacts to groups
  console.log('Adding contacts to groups...');

  // VIP Clients group
  const vipGroup = createdGroups.find(g => g.name === 'VIP Clients');
  if (vipGroup) {
    const vipContacts = [
      createdContacts.find(c => c.firstName === 'John' && c.lastName === 'Smith'),
      createdContacts.find(c => c.firstName === 'Robert' && c.lastName === 'Wilson'),
      createdContacts.find(c => c.firstName === 'David' && c.lastName === 'Martinez')
    ].filter(Boolean);

    for (const contact of vipContacts) {
      const existingMembership = await prisma.contactGroupMembership.findFirst({
        where: {
          groupId: vipGroup.id,
          contactId: contact.id
        }
      });

      if (!existingMembership) {
        await prisma.contactGroupMembership.create({
          data: {
            groupId: vipGroup.id,
            contactId: contact.id
          }
        });
        console.log(`Added ${contact.firstName} ${contact.lastName} to VIP Clients group`);
      }
    }
  }

  // Prospects group
  const prospectsGroup = createdGroups.find(g => g.name === 'Prospects');
  if (prospectsGroup) {
    const prospectContacts = [
      createdContacts.find(c => c.firstName === 'Sarah' && c.lastName === 'Johnson'),
      createdContacts.find(c => c.firstName === 'Emily' && c.lastName === 'Davis'),
      createdContacts.find(c => c.firstName === 'Jennifer' && c.lastName === 'Lee'),
      createdContacts.find(c => c.firstName === 'Lisa' && c.lastName === 'Taylor'),
      createdContacts.find(c => c.firstName === 'Patricia' && c.lastName === 'Thomas')
    ].filter(Boolean);

    for (const contact of prospectContacts) {
      const existingMembership = await prisma.contactGroupMembership.findFirst({
        where: {
          groupId: prospectsGroup.id,
          contactId: contact.id
        }
      });

      if (!existingMembership) {
        await prisma.contactGroupMembership.create({
          data: {
            groupId: prospectsGroup.id,
            contactId: contact.id
          }
        });
        console.log(`Added ${contact.firstName} ${contact.lastName} to Prospects group`);
      }
    }
  }

  // Referral Partners group
  const partnersGroup = createdGroups.find(g => g.name === 'Referral Partners');
  if (partnersGroup) {
    const partnerContacts = [
      createdContacts.find(c => c.firstName === 'Michael' && c.lastName === 'Brown'),
      createdContacts.find(c => c.firstName === 'James' && c.lastName === 'Anderson')
    ].filter(Boolean);

    for (const contact of partnerContacts) {
      const existingMembership = await prisma.contactGroupMembership.findFirst({
        where: {
          groupId: partnersGroup.id,
          contactId: contact.id
        }
      });

      if (!existingMembership) {
        await prisma.contactGroupMembership.create({
          data: {
            groupId: partnersGroup.id,
            contactId: contact.id
          }
        });
        console.log(`Added ${contact.firstName} ${contact.lastName} to Referral Partners group`);
      }
    }
  }

  // Create activity types
  console.log('Creating activity types...');

  const activityTypeData = [
    {
      name: 'Email',
      icon: 'Mail',
      color: '#3b82f6', // blue
      description: 'Email communication with contact',
      isSystem: true,
    },
    {
      name: 'Call',
      icon: 'Phone',
      color: '#10b981', // green
      description: 'Phone call with contact',
      isSystem: true,
    },
    {
      name: 'Meeting',
      icon: 'Users',
      color: '#6366f1', // indigo
      description: 'In-person or virtual meeting',
      isSystem: true,
    },
    // Removed legacy Task activity type. Tasklists/TasklistItems are now separate models.
    {
      name: 'Note',
      icon: 'FileText',
      color: '#f59e0b', // amber
      description: 'General note or comment',
      isSystem: true,
    },
    {
      name: 'Follow-up',
      icon: 'Redo',
      color: '#ef4444', // red
      description: 'Follow-up activity',
      isSystem: false,
    },
    {
      name: 'Demo',
      icon: 'Presentation',
      color: '#ec4899', // pink
      description: 'Product or service demonstration',
      isSystem: false,
    },
    {
      name: 'Proposal',
      icon: 'FileCheck',
      color: '#f97316', // orange
      description: 'Proposal or quote sent to contact',
      isSystem: false,
    }
  ];

  const createdActivityTypes = {};
  for (const type of activityTypeData) {
    const existingType = await prisma.activityType.findFirst({
      where: { name: type.name }
    });

    if (!existingType) {
      const newType = await prisma.activityType.create({
        data: type
      });
      createdActivityTypes[type.name.toLowerCase()] = newType;
      console.log(`Created activity type: ${type.name}`);
    } else {
      createdActivityTypes[type.name.toLowerCase()] = existingType;
      console.log(`Activity type already exists: ${type.name}`);
    }
  }

  // Create activities for contacts
  console.log('Creating activities for contacts...');

  const activityTypeKeys = Object.keys(createdActivityTypes);
  const now = new Date();
  const threeMonthsAgo = new Date(now);
  threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

  for (const contact of createdContacts) {
    // Create 2-5 activities per contact
    const numActivities = Math.floor(Math.random() * 4) + 2;

    for (let i = 0; i < numActivities; i++) {
      const activityDate = randomDate(threeMonthsAgo, now);
      const activityTypeKey = randomItem(activityTypeKeys);
      const activityType = createdActivityTypes[activityTypeKey];

      await prisma.activity.create({
        data: {
          contactId: contact.id,
          type: activityType.id,
          description: `${activityType.name} with ${contact.firstName} ${contact.lastName}`,
          date: activityDate
        }
      });
    }
    console.log(`Created ${numActivities} activities for ${contact.firstName} ${contact.lastName}`);
  }

  // Create tasklists for contacts
  console.log('Creating tasklists for contacts...');

  const taskPriorities = ['low', 'medium', 'high'];
  const taskTitles = [
    'Follow up call',
    'Send proposal',
    'Schedule meeting',
    'Review contract',
    'Send welcome package',
    'Quarterly review',
    'Update contact information',
    'Discuss new opportunities'
  ];

  for (const contact of createdContacts) {
    // Create 1-2 tasklists per contact
    const numTasklists = Math.floor(Math.random() * 2) + 1;

    for (let i = 0; i < numTasklists; i++) {
      const tasklist = await prisma.tasklist.create({
        data: {
          contactId: contact.id,
          title: `Tasks for ${contact.firstName} ${contact.lastName} - ${i + 1}`,
          description: `Task list for ${contact.firstName} ${contact.lastName}`,
          status: Math.random() > 0.7 ? 'completed' : 'in_progress'
        }
      });

      // Create 2-4 task items per tasklist
      const numItems = Math.floor(Math.random() * 3) + 2;

      for (let j = 0; j < numItems; j++) {
        const completed = Math.random() > 0.6;
        const dueDate = new Date();

        // Set due date to be either in the past or future
        if (Math.random() > 0.5) {
          dueDate.setDate(dueDate.getDate() + Math.floor(Math.random() * 14) + 1); // 1-14 days in future
        } else {
          dueDate.setDate(dueDate.getDate() - Math.floor(Math.random() * 7) - 1); // 1-7 days in past
        }

        const completedAt = completed ? randomDate(threeMonthsAgo, now) : null;

        await prisma.tasklistItem.create({
          data: {
            tasklistId: tasklist.id,
            title: randomItem(taskTitles),
            description: `Task item for ${contact.firstName} ${contact.lastName}`,
            dueDate: dueDate,
            priority: randomItem(taskPriorities),
            completed: completed,
            completedAt: completedAt,
            order: j + 1
          }
        });
      }
    }
    console.log(`Created ${numTasklists} tasklists for ${contact.firstName} ${contact.lastName}`);
  }

  // Create notes for contacts
  console.log('Creating notes for contacts...');

  const noteContents = [
    'Client expressed interest in our premium service package.',
    'Discussed potential partnership opportunities.',
    'Client has concerns about pricing, need to follow up with more options.',
    'Very positive meeting, client ready to move forward.',
    'Client referred by existing customer.',
    'Needs more information about our services.',
    'Client has a tight deadline, need to expedite process.',
    'Discussed long-term strategy and goals.'
  ];

  for (const contact of createdContacts) {
    // Create 1-2 notes per contact
    const numNotes = Math.floor(Math.random() * 2) + 1;

    for (let i = 0; i < numNotes; i++) {
      const noteDate = randomDate(threeMonthsAgo, now);

      await prisma.note.create({
        data: {
          contactId: contact.id,
          title: `Note from ${noteDate.toLocaleDateString()}`,
          content: randomItem(noteContents),
          createdAt: noteDate,
          updatedAt: noteDate
        }
      });
    }
    console.log(`Created ${numNotes} notes for ${contact.firstName} ${contact.lastName}`);
  }

  // Create checklists for some contacts (commented out - model doesn't exist)
  // console.log('Creating checklists for contacts...');

  const checklistTitles = [
    'Onboarding Process',
    'Account Setup',
    'Document Collection',
    'Project Kickoff',
    'Quarterly Review'
  ];

  const checklistItems = {
    'Onboarding Process': [
      'Initial consultation',
      'Collect client information',
      'Sign agreement',
      'Setup client in system',
      'Send welcome package'
    ],
    'Account Setup': [
      'Create user account',
      'Configure permissions',
      'Setup billing information',
      'Provide access credentials',
      'Schedule training session'
    ],
    'Document Collection': [
      'ID verification',
      'Proof of address',
      'Financial statements',
      'Tax documents',
      'Signed contracts'
    ],
    'Project Kickoff': [
      'Define project scope',
      'Assign team members',
      'Create timeline',
      'Set milestones',
      'Schedule kickoff meeting'
    ],
    'Quarterly Review': [
      'Prepare performance report',
      'Review goals and achievements',
      'Identify improvement areas',
      'Update strategy',
      'Schedule follow-up'
    ]
  };

  // Create checklists for half of the contacts
  const contactsForChecklists = createdContacts.filter(() => Math.random() > 0.5);

  /*
  for (const contact of contactsForChecklists) {
    const checklistTitle = randomItem(checklistTitles);
    const items = checklistItems[checklistTitle];

    const checklist = await prisma.checklist.create({
      data: {
        contactId: contact.id,
        title: checklistTitle,
        description: `Checklist for ${contact.firstName} ${contact.lastName}`,
        status: 'in_progress',
        category: 'client'
      }
    });

    // Create checklist items
    for (let i = 0; i < items.length; i++) {
      const completed = Math.random() > 0.6;

      await prisma.checklistItem.create({
        data: {
          checklistId: checklist.id,
          text: items[i],
          description: `Item ${i+1} for ${checklistTitle}`,
          completed: completed,
          completedAt: completed ? randomDate(threeMonthsAgo, now) : null,
          order: i + 1
        }
      });
    }

    console.log(`Created checklist "${checklistTitle}" for ${contact.firstName} ${contact.lastName}`);
  }
  */

  // Create email templates
  console.log('Creating email templates...');

  const emailTemplates = [
    {
      name: 'Welcome Email',
      description: 'Initial welcome email for new clients',
      subject: 'Welcome to SlimCRM!',
      content: `
        <h1>Welcome to SlimCRM!</h1>
        <p>Dear {{firstName}},</p>
        <p>We're excited to have you as a client and look forward to working with you.</p>
        <p>Here are some resources to help you get started:</p>
        <ul>
          <li>Your account manager: {{accountManager}}</li>
          <li>Support email: <EMAIL></li>
          <li>Phone: (*************</li>
        </ul>
        <p>Best regards,<br>The SlimCRM Team</p>
      `
    },
    {
      name: 'Follow-up Email',
      description: 'Follow-up after initial meeting',
      subject: 'Following up on our conversation',
      content: `
        <h1>Thank You for Your Time</h1>
        <p>Dear {{firstName}},</p>
        <p>Thank you for taking the time to meet with me today. I enjoyed our conversation about {{topic}} and am excited about the possibility of working together.</p>
        <p>As promised, I've attached the information we discussed. Please let me know if you have any questions.</p>
        <p>I'll follow up with you next week to discuss next steps.</p>
        <p>Best regards,<br>{{senderName}}</p>
      `
    },
    {
      name: 'Monthly Newsletter',
      description: 'Monthly newsletter template',
      subject: 'SlimCRM Monthly Update - {{month}}',
      content: `
        <h1>SlimCRM Monthly Update</h1>
        <p>Dear {{firstName}},</p>
        <p>Here's what's new at SlimCRM this month:</p>
        <h2>Feature Updates</h2>
        <p>We've added several new features to enhance your experience:</p>
        <ul>
          <li>Improved pipeline management</li>
          <li>Enhanced reporting capabilities</li>
          <li>New integration options</li>
        </ul>
        <h2>Upcoming Events</h2>
        <p>Join us for our upcoming webinar on {{webinarDate}}.</p>
        <p>Best regards,<br>The SlimCRM Team</p>
      `
    },
    {
      name: 'Proposal Follow-up',
      description: 'Follow-up after sending a proposal',
      subject: 'Your Proposal from {{companyName}}',
      content: `
        <h1>Proposal Follow-up</h1>
        <p>Dear {{firstName}},</p>
        <p>I wanted to follow up on the proposal I sent you last week for {{projectName}}.</p>
        <p>Do you have any questions about our approach or pricing? I'm happy to schedule a call to discuss any concerns you might have.</p>
        <p>The proposal is valid until {{expirationDate}}, so please let me know if you'd like to move forward.</p>
        <p>Best regards,<br>{{senderName}}</p>
      `
    },
    {
      name: 'Meeting Reminder',
      description: 'Reminder for upcoming meetings',
      subject: 'Reminder: Meeting Tomorrow at {{time}}',
      content: `
        <h1>Meeting Reminder</h1>
        <p>Dear {{firstName}},</p>
        <p>This is a friendly reminder about our meeting scheduled for tomorrow, {{date}} at {{time}}.</p>
        <p><strong>Meeting Details:</strong></p>
        <ul>
          <li>Date: {{date}}</li>
          <li>Time: {{time}}</li>
          <li>Location: {{location}}</li>
          <li>Topic: {{topic}}</li>
        </ul>
        <p>Please let me know if you need to reschedule.</p>
        <p>Looking forward to speaking with you!</p>
        <p>Best regards,<br>{{senderName}}</p>
      `
    },
    {
      name: 'Thank You',
      description: 'Thank you email after purchase or meeting',
      subject: 'Thank you for choosing {{companyName}}',
      content: `
        <h1>Thank You!</h1>
        <p>Dear {{firstName}},</p>
        <p>Thank you for choosing {{companyName}} for your {{serviceType}} needs. We're excited to work with you!</p>
        <p>Your account manager {{accountManager}} will be in touch within 24 hours to get started.</p>
        <p>In the meantime, here are some helpful resources:</p>
        <ul>
          <li>Getting Started Guide: {{guideLink}}</li>
          <li>Support Portal: {{supportLink}}</li>
          <li>Contact Support: {{supportEmail}}</li>
        </ul>
        <p>Welcome to the {{companyName}} family!</p>
        <p>Best regards,<br>The {{companyName}} Team</p>
      `
    },
    {
      name: 'Cold Outreach',
      description: 'Initial cold outreach template',
      subject: 'Quick question about {{companyName}}',
      content: `
        <p>Hi {{firstName}},</p>
        <p>I hope this email finds you well. I came across {{companyName}} and was impressed by {{specificDetail}}.</p>
        <p>I work with companies like yours to help them {{valueProposition}}. We've helped similar companies achieve:</p>
        <ul>
          <li>{{benefit1}}</li>
          <li>{{benefit2}}</li>
          <li>{{benefit3}}</li>
        </ul>
        <p>Would you be open to a brief 15-minute call to discuss how we might be able to help {{companyName}}?</p>
        <p>Best regards,<br>{{senderName}}<br>{{senderTitle}}<br>{{senderCompany}}</p>
      `
    },
    {
      name: 'Event Invitation',
      description: 'Invitation to company events',
      subject: 'You\'re Invited: {{eventName}}',
      content: `
        <h1>You're Invited!</h1>
        <p>Dear {{firstName}},</p>
        <p>We're excited to invite you to our upcoming event: <strong>{{eventName}}</strong></p>
        <p><strong>Event Details:</strong></p>
        <ul>
          <li>Date: {{eventDate}}</li>
          <li>Time: {{eventTime}}</li>
          <li>Location: {{eventLocation}}</li>
          <li>Dress Code: {{dressCode}}</li>
        </ul>
        <p>{{eventDescription}}</p>
        <p>This event is perfect for {{targetAudience}} and will feature:</p>
        <ul>
          <li>{{feature1}}</li>
          <li>{{feature2}}</li>
          <li>{{feature3}}</li>
        </ul>
        <p>Please RSVP by {{rsvpDate}} by clicking the link below:</p>
        <p><a href="{{rsvpLink}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">RSVP Now</a></p>
        <p>We look forward to seeing you there!</p>
        <p>Best regards,<br>The {{companyName}} Team</p>
      `
    },
    {
      name: 'Contract Renewal',
      description: 'Contract renewal reminder',
      subject: 'Contract Renewal - {{contractName}}',
      content: `
        <h1>Contract Renewal Notice</h1>
        <p>Dear {{firstName}},</p>
        <p>Your contract for {{contractName}} is set to expire on {{expirationDate}}.</p>
        <p>We've been pleased to work with {{companyName}} over the past {{contractDuration}} and would love to continue our partnership.</p>
        <p><strong>Renewal Benefits:</strong></p>
        <ul>
          <li>{{renewalBenefit1}}</li>
          <li>{{renewalBenefit2}}</li>
          <li>{{renewalBenefit3}}</li>
        </ul>
        <p>To ensure uninterrupted service, please let us know your renewal decision by {{renewalDeadline}}.</p>
        <p>I'll be in touch to discuss renewal terms and answer any questions you may have.</p>
        <p>Best regards,<br>{{accountManager}}<br>Account Manager</p>
      `
    },
    {
      name: 'Holiday Greetings',
      description: 'Holiday wishes for clients',
      subject: 'Happy Holidays from {{companyName}}',
      content: `
        <h1>Happy Holidays!</h1>
        <p>Dear {{firstName}},</p>
        <p>As we approach the end of another year, we wanted to take a moment to express our gratitude for your continued partnership with {{companyName}}.</p>
        <p>This year has been filled with achievements, and we're grateful to have shared this journey with valued clients like you.</p>
        <p>We wish you and your family a wonderful holiday season and a prosperous New Year!</p>
        <p>Thank you for your trust and partnership. We look forward to continuing to serve you in the coming year.</p>
        <p>Warmest regards,<br>The {{companyName}} Team</p>
      `
    }
  ];

  for (const template of emailTemplates) {
    const existingTemplate = await prisma.emailTemplate.findFirst({
      where: {
        name: template.name,
        userId: adminUser.id
      }
    });

    if (!existingTemplate) {
      await prisma.emailTemplate.create({
        data: {
          ...template,
          userId: adminUser.id
        }
      });
      console.log(`Created email template: ${template.name}`);
    } else {
      console.log(`Email template already exists: ${template.name}`);
    }
  }

  // Create email campaigns
  console.log('Creating email campaigns...');

  const campaignData = [
    {
      name: 'Welcome Series',
      description: 'Automated welcome email series for new clients',
      campaignType: 'drip',
      status: 'active',
      sendType: 'immediate',
      tags: ['onboarding', 'welcome'],
      templateId: null, // Will be set to Welcome Email template
      distributionListId: null // Will be set to Customer Success list
    },
    {
      name: 'Monthly Newsletter - January',
      description: 'January 2024 newsletter campaign',
      campaignType: 'one-time',
      status: 'completed',
      sendType: 'scheduled',
      scheduledDate: new Date('2024-01-15'),
      scheduledTime: '09:00',
      tags: ['newsletter', 'monthly'],
      templateId: null, // Will be set to Monthly Newsletter template
      distributionListId: null // Will be set to Newsletter list
    },
    {
      name: 'Product Update Announcement',
      description: 'Announcing new product features',
      campaignType: 'one-time',
      status: 'draft',
      sendType: 'scheduled',
      scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      scheduledTime: '10:00',
      tags: ['product', 'announcement'],
      templateId: null, // Will be set to Product Updates template
      distributionListId: null // Will be set to Product Updates list
    },
    {
      name: 'Event Invitation - Q1 Webinar',
      description: 'Invitation to Q1 strategy webinar',
      campaignType: 'one-time',
      status: 'scheduled',
      sendType: 'scheduled',
      scheduledDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
      scheduledTime: '14:00',
      tags: ['event', 'webinar', 'Q1'],
      templateId: null, // Will be set to Event Invitation template
      distributionListId: null // Will be set to Event Invitations list
    },
    {
      name: 'Cold Outreach - Tech Companies',
      description: 'Cold outreach campaign targeting technology companies',
      campaignType: 'one-time',
      status: 'active',
      sendType: 'immediate',
      tags: ['cold-outreach', 'technology'],
      templateId: null, // Will be set to Cold Outreach template
      distributionListId: null // Will be set to Cold Outreach list
    },
    {
      name: 'Holiday Greetings 2024',
      description: 'End of year holiday greetings to all clients',
      campaignType: 'one-time',
      status: 'draft',
      sendType: 'scheduled',
      scheduledDate: new Date('2024-12-20'),
      scheduledTime: '09:00',
      tags: ['holiday', 'greetings', 'annual'],
      templateId: null, // Will be set to Holiday Greetings template
      distributionListId: null // Will be set to VIP Clients list
    }
  ];

  // Get created templates and lists for campaigns
  const createdTemplates = await prisma.emailTemplate.findMany({
    where: { userId: adminUser.id }
  });

  for (const campaign of campaignData) {
    // Find matching template
    let templateId = null;
    if (campaign.name.includes('Welcome')) {
      templateId = createdTemplates.find(t => t.name === 'Welcome Email')?.id;
    } else if (campaign.name.includes('Newsletter')) {
      templateId = createdTemplates.find(t => t.name === 'Monthly Newsletter')?.id;
    } else if (campaign.name.includes('Product')) {
      templateId = createdTemplates.find(t => t.name === 'Thank You')?.id; // Use Thank You as placeholder
    } else if (campaign.name.includes('Event')) {
      templateId = createdTemplates.find(t => t.name === 'Event Invitation')?.id;
    } else if (campaign.name.includes('Cold')) {
      templateId = createdTemplates.find(t => t.name === 'Cold Outreach')?.id;
    } else if (campaign.name.includes('Holiday')) {
      templateId = createdTemplates.find(t => t.name === 'Holiday Greetings')?.id;
    }

    // Find matching distribution list
    let distributionListId = null;
    if (campaign.name.includes('Welcome')) {
      distributionListId = createdLists.find(l => l.name === 'Customer Success')?.id;
    } else if (campaign.name.includes('Newsletter')) {
      distributionListId = createdLists.find(l => l.name === 'Newsletter')?.id;
    } else if (campaign.name.includes('Product')) {
      distributionListId = createdLists.find(l => l.name === 'Product Updates')?.id;
    } else if (campaign.name.includes('Event')) {
      distributionListId = createdLists.find(l => l.name === 'Event Invitations')?.id;
    } else if (campaign.name.includes('Cold')) {
      distributionListId = createdLists.find(l => l.name === 'Cold Outreach')?.id;
    } else if (campaign.name.includes('Holiday')) {
      distributionListId = createdLists.find(l => l.name === 'VIP Clients')?.id;
    }

    // Only create campaign if we have both template and distribution list
    if (templateId && distributionListId) {
      const existingCampaign = await prisma.emailCampaign.findFirst({
        where: {
          name: campaign.name,
          userId: adminUser.id
        }
      });

      if (!existingCampaign) {
        await prisma.emailCampaign.create({
          data: {
            ...campaign,
            templateId,
            distributionListId,
            userId: adminUser.id,
            createdAt: randomDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()),
            updatedAt: new Date()
          }
        });
        console.log(`Created email campaign: ${campaign.name}`);
      } else {
        console.log(`Email campaign already exists: ${campaign.name}`);
      }
    } else {
      console.log(`Skipping campaign ${campaign.name} - missing template or distribution list`);
    }
  }

  // Create workflow examples
  console.log('Creating workflow examples...');

  const workflowExamples = [
    {
      name: 'New Client Onboarding',
      description: 'Automatically send welcome email and create tasks when a new client is added',
      isActive: true,
      trigger: {
        triggerType: 'contact_created',
        config: {}
      },
      conditions: [
        {
          field: 'type',
          operator: 'equals',
          value: 'customer',
          order: 1
        }
      ],
      actions: [
        {
          actionType: 'send_email',
          config: {
            templateId: '1', // This would be replaced with actual template ID
            templateName: 'Welcome Email',
            delay: 0
          },
          order: 1
        },
        {
          actionType: 'create_task',
          config: {
            title: 'Schedule welcome call',
            description: 'Call the new client to welcome them and discuss next steps',
            dueDate: '3', // days from trigger
            priority: 'high'
          },
          order: 2
        },
        {
          actionType: 'add_to_group',
          config: {
            groupId: createdGroups.find(g => g.name === 'Clients')?.id || ''
          },
          order: 3
        }
      ]
    },
    {
      name: 'Follow-up Reminder',
      description: 'Create a follow-up task when a contact moves to the "Meeting Scheduled" pipeline stage',
      isActive: true,
      trigger: {
        triggerType: 'stage_changed',
        config: {
          toStage: createdStages.find(s => s.name === 'Meeting Scheduled')?.id || 'any'
        }
      },
      conditions: [],
      actions: [
        {
          actionType: 'create_task',
          config: {
            title: 'Follow up after meeting',
            description: 'Send a follow-up email and document meeting outcomes',
            dueDate: '1', // days from trigger
            priority: 'medium'
          },
          order: 1
        },
        {
          actionType: 'create_note',
          config: {
            title: 'Meeting scheduled',
            content: 'Contact has moved to the Meeting Scheduled stage. Prepare necessary materials for the meeting.'
          },
          order: 2
        }
      ]
    },
    {
      name: 'Lead Nurturing',
      description: 'Send nurturing emails to leads that haven\'t been contacted in 14 days',
      isActive: false, // Inactive by default
      trigger: {
        triggerType: 'contact_updated',
        config: {}
      },
      conditions: [
        {
          field: 'type',
          operator: 'equals',
          value: 'lead',
          order: 1
        },
        {
          field: 'pipelineStage',
          operator: 'equals',
          value: createdStages.find(s => s.name === 'Lead')?.id || '',
          order: 2
        }
      ],
      actions: [
        {
          actionType: 'send_email',
          config: {
            templateId: '3', // This would be replaced with actual template ID
            templateName: 'Lead Nurturing Email',
            delay: 0
          },
          order: 1
        },
        {
          actionType: 'create_task',
          config: {
            title: 'Follow up with lead',
            description: 'Call or email the lead to check if they received the nurturing email and if they have any questions',
            dueDate: '3', // days from trigger
            priority: 'medium'
          },
          order: 2
        }
      ]
    },
    {
      name: 'Deal Won Celebration',
      description: 'Send congratulatory email and create follow-up tasks when a deal is won',
      isActive: true,
      trigger: {
        triggerType: 'stage_changed',
        config: {
          toStage: createdStages.find(s => s.name === 'Closed Won')?.id || 'any'
        }
      },
      conditions: [],
      actions: [
        {
          actionType: 'send_email',
          config: {
            templateId: '2', // This would be replaced with actual template ID
            templateName: 'Deal Celebration Email',
            delay: 0
          },
          order: 1
        },
        {
          actionType: 'create_task',
          config: {
            title: 'Schedule kickoff meeting',
            description: 'Arrange a kickoff meeting to start the project/service',
            dueDate: '5', // days from trigger
            priority: 'high'
          },
          order: 2
        },
        {
          actionType: 'add_to_group',
          config: {
            groupId: createdGroups.find(g => g.name === 'VIP')?.id || ''
          },
          order: 3
        },
        {
          actionType: 'create_note',
          config: {
            title: 'Deal closed successfully',
            content: 'The deal has been successfully closed. Make sure to follow up with the client to ensure a smooth onboarding process.'
          },
          order: 4
        }
      ]
    },
    {
      name: 'Lead Follow-up Sequence',
      description: 'Automated follow-up sequence for new leads',
      isActive: true,
      trigger: {
        triggerType: 'contact_created',
        config: {}
      },
      conditions: [
        {
          field: 'type',
          operator: 'equals',
          value: 'lead',
          order: 1
        }
      ],
      actions: [
        {
          actionType: 'send_email',
          config: {
            templateId: '2',
            templateName: 'Follow-up Email',
            delay: 1 // 1 day after trigger
          },
          order: 1
        },
        {
          actionType: 'create_task',
          config: {
            title: 'Qualify lead',
            description: 'Call the lead to qualify their interest and budget',
            dueDate: '2',
            priority: 'medium'
          },
          order: 2
        },
        {
          actionType: 'send_email',
          config: {
            templateId: '3',
            templateName: 'Proposal Follow-up',
            delay: 7 // 7 days after trigger
          },
          order: 3
        }
      ]
    },
    {
      name: 'Contract Renewal Reminder',
      description: 'Remind account managers about upcoming contract renewals',
      isActive: true,
      trigger: {
        triggerType: 'date_based',
        config: {
          field: 'contract_expiration',
          days_before: 30
        }
      },
      conditions: [
        {
          field: 'type',
          operator: 'equals',
          value: 'customer',
          order: 1
        }
      ],
      actions: [
        {
          actionType: 'create_task',
          config: {
            title: 'Contract renewal discussion',
            description: 'Reach out to client to discuss contract renewal options',
            dueDate: '0',
            priority: 'high'
          },
          order: 1
        },
        {
          actionType: 'send_email',
          config: {
            templateId: '7',
            templateName: 'Contract Renewal',
            delay: 0
          },
          order: 2
        }
      ]
    },
    {
      name: 'Inactive Contact Re-engagement',
      description: 'Re-engage contacts who haven\'t been contacted in 90 days',
      isActive: true,
      trigger: {
        triggerType: 'date_based',
        config: {
          field: 'last_contact_date',
          days_since: 90
        }
      },
      conditions: [
        {
          field: 'type',
          operator: 'not_equals',
          value: 'customer',
          order: 1
        }
      ],
      actions: [
        {
          actionType: 'send_email',
          config: {
            templateId: '5',
            templateName: 'Cold Outreach',
            delay: 0
          },
          order: 1
        },
        {
          actionType: 'create_task',
          config: {
            title: 'Re-engagement follow-up',
            description: 'Follow up on re-engagement email and assess continued interest',
            dueDate: '3',
            priority: 'low'
          },
          order: 2
        }
      ]
    },
    {
      name: 'VIP Client Special Treatment',
      description: 'Special handling for VIP clients',
      isActive: true,
      trigger: {
        triggerType: 'contact_updated',
        config: {}
      },
      conditions: [
        {
          field: 'tags',
          operator: 'contains',
          value: 'VIP',
          order: 1
        }
      ],
      actions: [
        {
          actionType: 'create_task',
          config: {
            title: 'VIP client check-in',
            description: 'Schedule a personal check-in call with this VIP client',
            dueDate: '1',
            priority: 'high'
          },
          order: 1
        },
        {
          actionType: 'add_to_group',
          config: {
            groupId: createdGroups.find(g => g.name === 'VIP Clients')?.id || ''
          },
          order: 2
        }
      ]
    },
    {
      name: 'Meeting Follow-up',
      description: 'Automatically create follow-up tasks after meetings',
      isActive: true,
      trigger: {
        triggerType: 'activity_created',
        config: {
          activity_type: 'meeting'
        }
      },
      conditions: [],
      actions: [
        {
          actionType: 'create_task',
          config: {
            title: 'Send meeting recap',
            description: 'Send a recap email with meeting notes and next steps',
            dueDate: '1',
            priority: 'medium'
          },
          order: 1
        },
        {
          actionType: 'create_task',
          config: {
            title: 'Schedule follow-up meeting',
            description: 'Schedule the next meeting if discussed during the call',
            dueDate: '3',
            priority: 'medium'
          },
          order: 2
        }
      ]
    }
  ];

  // Use a fixed mock user ID for workflows to match the API
  const mockUserId = "000000000000000000000000";

  for (const workflowExample of workflowExamples) {
    // Check if workflow with this name already exists for the mock user
    const existingWorkflow = await prisma.workflow.findFirst({
      where: {
        name: workflowExample.name,
        userId: mockUserId
      }
    });

    if (!existingWorkflow) {
      // Create the workflow with its trigger, conditions, and actions
      const workflow = await prisma.workflow.create({
        data: {
          name: workflowExample.name,
          description: workflowExample.description,
          isActive: workflowExample.isActive,
          userId: mockUserId
        }
      });

      // Create the trigger
      await prisma.workflowTrigger.create({
        data: {
          workflowId: workflow.id,
          triggerType: workflowExample.trigger.triggerType,
          config: workflowExample.trigger.config
        }
      });

      // Create conditions if any
      if (workflowExample.conditions && workflowExample.conditions.length > 0) {
        await prisma.workflowCondition.createMany({
          data: workflowExample.conditions.map(condition => ({
            workflowId: workflow.id,
            field: condition.field,
            operator: condition.operator,
            value: condition.value,
            order: condition.order
          }))
        });
      }

      // Create actions
      await prisma.workflowAction.createMany({
        data: workflowExample.actions.map(action => ({
          workflowId: workflow.id,
          actionType: action.actionType,
          config: action.config,
          order: action.order
        }))
      });

      console.log(`Created workflow: ${workflowExample.name}`);
    } else {
      console.log(`Workflow already exists: ${workflowExample.name}`);
    }
  }

  // Create sample documents for contacts
  console.log('Creating sample documents...');

  const documentTypes = ['Contract', 'Invoice', 'Proposal', 'Report', 'Legal', 'Financial'];
  const documentSources = ['local', 'onedrive'];

  for (const contact of createdContacts.slice(0, 5)) { // Add documents to first 5 contacts
    const numDocuments = Math.floor(Math.random() * 3) + 1; // 1-3 documents per contact

    for (let i = 0; i < numDocuments; i++) {
      const docType = randomItem(documentTypes);
      const source = randomItem(documentSources);

      await prisma.document.create({
        data: {
          name: `${docType}_${contact.firstName}_${contact.lastName}_${i + 1}.pdf`,
          fileType: 'pdf',
          fileSize: `${Math.floor(Math.random() * 500) + 100} KB`,
          tags: [docType, contact.type],
          contactId: contact.id,
          source: source,
          folder: '/',
          uploadedAt: randomDate(threeMonthsAgo, now),
          uploadedBy: adminUser.name,
          url: source === 'onedrive' ? `https://onedrive.com/documents/${contact.id}/${i}` : null,
          externalId: source === 'onedrive' ? `ext_${contact.id}_${i}` : null,
          driveId: source === 'onedrive' ? 'drive_123' : null
        }
      });
    }
    console.log(`Created ${numDocuments} documents for ${contact.firstName} ${contact.lastName}`);
  }

  // Create sample audit logs to demonstrate the system
  console.log('Creating sample audit logs...');

  // Get actual created templates and workflows for audit logs
  const sampleTemplate = await prisma.emailTemplate.findFirst({
    where: { userId: adminUser.id, name: 'Welcome Email' }
  });

  const sampleWorkflow = await prisma.workflow.findFirst({
    where: { name: 'New Client Onboarding' }
  });

  const auditEvents = [
    {
      eventType: 'USER_LOGIN',
      userId: adminUser.id,
      entityType: 'User',
      entityId: adminUser.id,
      metadata: { loginMethod: 'azure_ad' }
    },
    {
      eventType: 'CONTACT_CREATE',
      userId: adminUser.id,
      entityType: 'Contact',
      entityId: createdContacts[0].id,
      newValues: createdContacts[0]
    },
    {
      eventType: 'CONTACT_UPDATE',
      userId: adminUser.id,
      entityType: 'Contact',
      entityId: createdContacts[1].id,
      oldValues: { phone: '(*************' },
      newValues: { phone: createdContacts[1].phone }
    }
  ];

  // Add template audit log if template exists
  if (sampleTemplate) {
    auditEvents.push({
      eventType: 'EMAIL_TEMPLATE_CREATE',
      userId: adminUser.id,
      entityType: 'EmailTemplate',
      entityId: sampleTemplate.id,
      newValues: { name: 'Welcome Email', subject: 'Welcome!' }
    });
  }

  // Add workflow audit log if workflow exists
  if (sampleWorkflow) {
    auditEvents.push({
      eventType: 'WORKFLOW_CREATE',
      userId: adminUser.id,
      entityType: 'Workflow',
      entityId: sampleWorkflow.id,
      newValues: { name: 'New Client Onboarding', isActive: true }
    });
  }

  for (const auditEvent of auditEvents) {
    await prisma.auditLog.create({
      data: {
        ...auditEvent,
        ip: '127.0.0.1',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        createdAt: randomDate(threeMonthsAgo, now)
      }
    });
  }

  console.log('Created sample audit logs');

  console.log('Seeding finished successfully!');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });