"use client";

import { useCurrentEditor } from "@tiptap/react";
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Quote,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Link2,
  Image as ImageIcon,
  Undo,
  Redo,
  UserCircle,
  ChevronDown,
} from "lucide-react";
import { CONTACT_FIELDS } from "@/lib/constants/contactFields";

export default function Toolbar() {
  const { editor } = useCurrentEditor();

  if (!editor) return null;

  const insertContactField = (fieldId) => {
    if (!fieldId) return;

    const field = CONTACT_FIELDS.find((f) => f.id === fieldId);
    if (!field) return;

    // Insert the merge field with double curly braces
    const mergeField = `{{${field.id}}}`;
    editor.chain().focus().insertContent(mergeField).run();
  };

  const addLink = () => {
    const previousUrl = editor.getAttributes("link").href;
    const url = window.prompt("Enter URL:", previousUrl);

    if (url === null) return;
    if (url === "") {
      editor.chain().focus().unsetLink().run();
      return;
    }
    editor.chain().focus().setLink({ href: url }).run();
  };

  const addImage = () => {
    // Create a hidden file input
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";

    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (file) {
        try {
          // Convert image to base64
          const reader = new FileReader();
          reader.onload = (e) => {
            const base64Image = e.target.result;
            // Insert the base64 image into the editor
            editor
              .chain()
              .focus()
              .setImage({
                src: base64Image,
                "data-type": "embedded-image",
                "data-filename": file.name,
              })
              .run();
          };
          reader.readAsDataURL(file);
        } catch (error) {
          console.error("Error processing image:", error);
          alert("Failed to process image. Please try again.");
        }
      }
    };

    input.click();
  };

  return (
    <div className="flex flex-wrap items-center gap-1 p-2 border-b dark:border-gray-700 bg-white dark:bg-gray-800">
      <div className="flex items-center gap-1 pr-2 border-r border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={`p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
            editor.isActive("bold")
              ? "bg-primary text-white hover:bg-primary-hover"
              : "text-gray-600 dark:text-gray-300"
          }`}
          title="Bold"
        >
          <Bold size={18} />
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={`p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
            editor.isActive("italic")
              ? "bg-primary text-white hover:bg-primary-hover"
              : "text-gray-600 dark:text-gray-300"
          }`}
          title="Italic"
        >
          <Italic size={18} />
        </button>
      </div>

      <div className="flex items-center pr-2 border-r border-gray-200 dark:border-gray-700">
        <select
          onChange={(e) => {
            e.preventDefault(); // Add this line to prevent form submission
            const level = parseInt(e.target.value);
            level === 0
              ? editor.chain().focus().setParagraph().run()
              : editor.chain().focus().toggleHeading({ level }).run();
          }}
          value={
            editor.isActive("heading", { level: 1 })
              ? "1"
              : editor.isActive("heading", { level: 2 })
              ? "2"
              : editor.isActive("heading", { level: 3 })
              ? "3"
              : "0"
          }
          className="h-10 px-2 pr-8 rounded border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent"
        >
          <option value="0">Normal</option>
          <option value="1">Heading 1</option>
          <option value="2">Heading 2</option>
          <option value="3">Heading 3</option>
        </select>
      </div>

      <div className="flex items-center gap-1 pr-2 border-r border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={`p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
            editor.isActive("bulletList")
              ? "bg-primary text-white hover:bg-primary-hover"
              : "text-gray-600 dark:text-gray-300"
          }`}
          title="Bullet List"
        >
          <List size={18} />
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={`p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
            editor.isActive("orderedList")
              ? "bg-primary text-white hover:bg-primary-hover"
              : "text-gray-600 dark:text-gray-300"
          }`}
          title="Numbered List"
        >
          <ListOrdered size={18} />
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={`p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
            editor.isActive("blockquote")
              ? "bg-primary text-white hover:bg-primary-hover"
              : "text-gray-600 dark:text-gray-300"
          }`}
          title="Quote"
        >
          <Quote size={18} />
        </button>
      </div>

      <div className="flex items-center gap-1 pr-2 border-r border-gray-200 dark:border-gray-700">
        {["left", "center", "right", "justify"].map((align) => (
          <button
            type="button"
            key={align}
            onClick={() => editor.chain().focus().setTextAlign(align).run()}
            className={`p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
              editor.isActive({ textAlign: align })
                ? "bg-primary text-white hover:bg-primary-hover"
                : "text-gray-600 dark:text-gray-300"
            }`}
            title={`Align ${align.charAt(0).toUpperCase() + align.slice(1)}`}
          >
            {align === "left" && <AlignLeft size={18} />}
            {align === "center" && <AlignCenter size={18} />}
            {align === "right" && <AlignRight size={18} />}
            {align === "justify" && <AlignJustify size={18} />}
          </button>
        ))}
      </div>

      <div className="flex items-center gap-1 pr-2 border-r border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={addLink}
          className={`p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
            editor.isActive("link")
              ? "bg-primary text-white hover:bg-primary-hover"
              : "text-gray-600 dark:text-gray-300"
          }`}
          title="Add Link"
        >
          <Link2 size={18} />
        </button>
        <button
          type="button"
          onClick={addImage}
          className="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
          title="Add Image"
        >
          <ImageIcon size={18} />
        </button>
      </div>

      <div className="flex items-center gap-1 pr-2 border-r border-gray-200 dark:border-gray-700">
        <div className="relative flex items-center">
          <select
            onChange={(e) => insertContactField(e.target.value)}
            value=""
            className="h-10 pl-2 pr-10 rounded border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">Insert Contact Field</option>
            {CONTACT_FIELDS.map((field) => (
              <option key={field.id} value={field.id}>
                {field.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="flex items-center gap-1">
        <button
          type="button"
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
          className="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          title="Undo"
        >
          <Undo size={18} />
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
          className="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          title="Redo"
        >
          <Redo size={18} />
        </button>
      </div>
    </div>
  );
}
