import { encrypt, decrypt } from './crypto';

class BrokerageAccountOpener {
  constructor() {
    this.schwabBaseUrl = process.env.SCHWAB_API_URL;
    this.ibBaseUrl = process.env.IB_API_URL;
    this.stateCache = new Map();
  }

  async initiateSchwabAuth() {
    try {
      const state = this.generateAndStoreState('schwab');
      const params = new URLSearchParams({
        client_id: process.env.SCHWAB_CLIENT_ID,
        response_type: 'code',
        redirect_uri: `${process.env.NEXTAUTH_URL}/api/auth/callback/schwab`,
        scope: 'openid trade account',
        state
      });
      
      return `${this.schwabBaseUrl}/oauth/authorize?${params.toString()}`;
    } catch (error) {
      console.error('Failed to initiate Schwab authentication:', error);
      throw new Error('Failed to initiate Schwab account opening process');
    }
  }

  async initiateIBAuth() {
    try {
      const state = this.generateAndStoreState('ib');
      const params = new URLSearchParams({
        client_id: process.env.IB_CLIENT_ID,
        response_type: 'code',
        redirect_uri: `${process.env.NEXTAUTH_URL}/api/auth/callback/ib`,
        scope: 'openid trade account',
        state
      });
      
      return `${this.ibBaseUrl}/oauth/authorize?${params.toString()}`;
    } catch (error) {
      console.error('Failed to initiate IB authentication:', error);
      throw new Error('Failed to initiate Interactive Brokers account opening process');
    }
  }

  generateAndStoreState(provider) {
    const state = this.generateStateParam();
    const timestamp = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes

    // Clean up expired states
    this.cleanExpiredStates();

    // Store the state with timestamp
    this.stateCache.set(state, {
      provider,
      timestamp,
      maxAge
    });

    return state;
  }

  generateStateParam() {
    // Generate a cryptographically secure random string
    const buffer = new Uint8Array(32);
    crypto.getRandomValues(buffer);
    return Array.from(buffer)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }

  validateCallback(savedState, receivedState) {
    if (!savedState || !receivedState) {
      throw new Error('State parameter is missing');
    }

    const stateData = this.stateCache.get(receivedState);
    
    if (!stateData) {
      throw new Error('Invalid state parameter');
    }

    // Check if state has expired
    const now = Date.now();
    if (now - stateData.timestamp > stateData.maxAge) {
      this.stateCache.delete(receivedState);
      throw new Error('State parameter has expired');
    }

    // Clean up used state
    this.stateCache.delete(receivedState);

    return true;
  }

  cleanExpiredStates() {
    const now = Date.now();
    for (const [state, data] of this.stateCache.entries()) {
      if (now - data.timestamp > data.maxAge) {
        this.stateCache.delete(state);
      }
    }
  }
}

export const brokerageAccountOpener = new BrokerageAccountOpener();