"use client";

import {
  Users,
  Activity,
  Target,
  TrendingUp,
  TrendingDown,
  Minus,
  HelpCircle
} from "lucide-react";
import Tooltip from "@/components/ui/Tooltip";

export default function ExecutiveSummaryCards({ data }) {
  if (!data) return null;

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num?.toLocaleString() || '0';
  };

  const formatPercentage = (num) => {
    if (num === null || num === undefined) return '0%';
    return `${num >= 0 ? '+' : ''}${num.toFixed(1)}%`;
  };

  const getTrendIcon = (value) => {
    if (value > 0) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (value < 0) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  const getTrendColor = (value) => {
    if (value > 0) return 'text-green-600 dark:text-green-400';
    if (value < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-500 dark:text-gray-400';
  };

  const cards = [
    {
      title: 'Total Contacts',
      value: data.totalContacts,
      subtitle: `${formatNumber(data.newContacts)} new`,
      trend: data.contactGrowthRate,
      icon: Users,
      color: 'bg-blue-500',
      description: 'Total contacts in your CRM',
      tooltip: 'All contacts in your database. Growth rate compares new contacts in selected timeframe vs previous period.',
      affectedByTimeFilter: false
    },
    {
      title: 'Active Pipeline',
      value: data.activePipelineContacts,
      subtitle: `${formatPercentage((data.activePipelineContacts / data.totalContacts) * 100)} of total`,
      trend: null,
      icon: Target,
      color: 'bg-purple-500',
      description: 'Contacts currently in pipeline stages',
      tooltip: 'Contacts that have been assigned to a pipeline stage and are actively being worked.',
      affectedByTimeFilter: false
    },
    {
      title: 'Activities',
      value: data.totalActivities,
      subtitle: `${data.averageActivitiesPerContact.toFixed(1)} per contact`,
      trend: data.activityGrowthRate,
      icon: Activity,
      color: 'bg-green-500',
      description: 'Total activities in selected timeframe',
      tooltip: 'All recorded activities (calls, emails, meetings, notes) within the selected time period. Growth rate compares to previous period.',
      affectedByTimeFilter: true
    },
    {
      title: 'Conversion Rate',
      value: `${data.conversionRate.toFixed(1)}%`,
      subtitle: 'Pipeline to client',
      trend: null,
      icon: TrendingUp,
      color: 'bg-orange-500',
      description: 'Formula: (Contacts reaching final stage ÷ Total pipeline contacts) × 100',
      tooltip: 'Percentage of pipeline contacts that reached a final stage (Closed Won, Client, Converted) within the selected timeframe.',
      affectedByTimeFilter: true
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {cards.map((card, index) => {
        const IconComponent = card.icon;
        
        return (
          <Tooltip key={index} content={card.tooltip} position="top">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className={`p-2 rounded-lg ${card.color}`}>
                    <IconComponent className="h-5 w-5 text-white" />
                  </div>
                  {card.affectedByTimeFilter && (
                    <Tooltip content="This metric is affected by the time filter" position="top">
                      <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                    </Tooltip>
                  )}
                </div>
                {card.trend !== null && (
                  <div className={`flex items-center space-x-1 ${getTrendColor(card.trend)}`}>
                    {getTrendIcon(card.trend)}
                    <span className="text-sm font-medium">
                      {formatPercentage(card.trend)}
                    </span>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="space-y-2">
                <div className="flex items-center space-x-1">
                  <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {card.title}
                  </h3>
                  <HelpCircle className="h-3 w-3 text-gray-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {typeof card.value === 'number' ? formatNumber(card.value) : card.value}
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {card.subtitle}
                </p>
              </div>

              {/* Description */}
              <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                <p className="text-xs text-gray-400 dark:text-gray-500">
                  {card.description}
                </p>
              </div>
            </div>
          </Tooltip>
        );
      })}
    </div>
  );
}
