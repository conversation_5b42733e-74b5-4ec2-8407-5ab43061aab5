'use client';

import { useState, useEffect } from 'react';
import ChecklistForm from './ChecklistForm';
import ChecklistFilter from './ChecklistFilter';
import SearchInput from '@/components/common/SearchInput';
import ChecklistCard from './ChecklistCard';

export default function ChecklistsTab({ contactId, onChecklistChange }) {
  const [checklists, setChecklists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [currentChecklist, setCurrentChecklist] = useState(null);
  const [expandedChecklistId, setExpandedChecklistId] = useState(null);
  const [filters, setFilters] = useState({
    category: 'all',
    status: 'all',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    searchTerm: ''
  });

  const fetchChecklists = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch(`/api/contacts/${contactId}/checklists`);
      if (!response.ok) {
        throw new Error('Failed to fetch checklists');
      }
      const data = await response.json();
      setChecklists(data);
    } catch (error) {
      console.error('Failed to fetch checklists:', error);
      setError('Failed to load checklists. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchChecklists();
  }, [contactId]);

  const handleAddChecklist = () => {
    setCurrentChecklist(null);
    setIsFormOpen(true);
  };

  const handleEditChecklist = (checklist) => {
    setCurrentChecklist(checklist);
    setIsFormOpen(true);
  };

  const handleDeleteChecklist = async (checklistId) => {
    if (!window.confirm('Are you sure you want to delete this checklist?')) {
      return;
    }

    try {
      const response = await fetch(`/api/checklists/${checklistId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete checklist');
      }

      // Remove the deleted checklist from the state
      setChecklists(checklists.filter(checklist => checklist.id !== checklistId));

      // Notify parent component that a checklist has changed
      if (onChecklistChange) onChecklistChange();
    } catch (error) {
      console.error('Failed to delete checklist:', error);
      setError('Failed to delete checklist. Please try again.');
    }
  };

  const handleChecklistSaved = (savedChecklist) => {
    if (currentChecklist) {
      // Update existing checklist
      setChecklists(checklists.map(checklist =>
        checklist.id === savedChecklist.id ? savedChecklist : checklist
      ));
    } else {
      // Add new checklist
      setChecklists([savedChecklist, ...checklists]);
    }

    // Clear any errors
    setError('');

    // Log success for debugging
    console.log('Checklist saved successfully:', savedChecklist);

    // Notify parent component that a checklist has changed
    if (onChecklistChange) onChecklistChange();
  };

  const toggleExpandChecklist = (checklistId) => {
    setExpandedChecklistId(expandedChecklistId === checklistId ? null : checklistId);
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleSearch = (searchTerm) => {
    setFilters(prev => ({
      ...prev,
      searchTerm
    }));
  };

  const handleItemToggle = async (checklistId, itemId, completed) => {
    try {
      // Optimistically update the UI first
      setChecklists(prevChecklists => prevChecklists.map(checklist => {
        if (checklist.id === checklistId) {
          return {
            ...checklist,
            items: checklist.items.map(item =>
              item.id === itemId ? { ...item, completed, completedAt: completed ? new Date() : null } : item
            )
          };
        }
        return checklist;
      }));

      // Then send the update to the server
      const response = await fetch(`/api/checklist-items/${itemId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ completed })
      });

      if (!response.ok) {
        throw new Error('Failed to update checklist item');
      }

      const updatedItem = await response.json();
      console.log('Successfully updated item:', updatedItem);

      // Fetch the updated checklist to get the new status
      const checklistResponse = await fetch(`/api/checklists/${checklistId}`);
      if (checklistResponse.ok) {
        const updatedChecklist = await checklistResponse.json();

        // Update the checklist status in state
        setChecklists(prevChecklists => prevChecklists.map(checklist =>
          checklist.id === checklistId ? {
            ...checklist,
            status: updatedChecklist.status,
            // Ensure we have the latest items data
            items: updatedChecklist.items
          } : checklist
        ));

        // Notify parent component that a checklist has changed
        if (onChecklistChange) onChecklistChange();
      }
    } catch (error) {
      console.error('Failed to toggle checklist item:', error);
      setError('Failed to update checklist item. Please try again.');

      // Revert the optimistic update on error by re-fetching all checklists
      fetchChecklists();
    }
  };

  // Filter checklists
  const filteredChecklists = checklists.filter(checklist => {
    // Filter by category
    if (filters.category !== 'all' && checklist.category !== filters.category) {
      return false;
    }

    // Filter by status
    if (filters.status !== 'all' && checklist.status !== filters.status) {
      return false;
    }

    // Filter by search term
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const titleMatch = checklist.title.toLowerCase().includes(searchLower);
      const descriptionMatch = checklist.description?.toLowerCase().includes(searchLower) || false;

      // Also search in checklist items
      const itemMatch = checklist.items.some(item =>
        item.text.toLowerCase().includes(searchLower) ||
        (item.description?.toLowerCase().includes(searchLower) || false)
      );

      return titleMatch || descriptionMatch || itemMatch;
    }

    return true;
  });

  // Sort checklists
  const sortedChecklists = [...filteredChecklists].sort((a, b) => {
    const sortOrder = filters.sortOrder === 'asc' ? 1 : -1;

    if (filters.sortBy === 'createdAt' || filters.sortBy === 'updatedAt') {
      return (new Date(a[filters.sortBy]) - new Date(b[filters.sortBy])) * sortOrder;
    }

    if (filters.sortBy === 'title') {
      return a.title.localeCompare(b.title) * sortOrder;
    }

    if (filters.sortBy === 'status') {
      return a.status.localeCompare(b.status) * sortOrder;
    }

    return 0;
  });

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow h-full overflow-auto">
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Checklists</h3>
          <button
            onClick={handleAddChecklist}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
          >
            Add Checklist
          </button>
        </div>

        {error && (
          <div className="p-3 mb-4 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
            {error}
          </div>
        )}

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <SearchInput
            placeholder="Search checklists..."
            onSearch={handleSearch}
            initialValue={filters.searchTerm}
          />

          <div className="flex items-center gap-2">
            <ChecklistFilter onFilterChange={handleFilterChange} />

            <div className="text-sm font-medium">
              <span className="text-primary">{sortedChecklists.length}</span> <span className="text-gray-600 dark:text-gray-300">checklists found</span>
              {filteredChecklists.length !== checklists.length && (
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">(filtered from {checklists.length})</span>
              )}
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="space-y-4">
            {sortedChecklists.map((checklist) => (
              <ChecklistCard
                key={checklist.id}
                checklist={checklist}
                isExpanded={expandedChecklistId === checklist.id}
                onToggleExpand={() => toggleExpandChecklist(checklist.id)}
                onEdit={() => handleEditChecklist(checklist)}
                onDelete={() => handleDeleteChecklist(checklist.id)}
                onItemToggle={handleItemToggle}
              />
            ))}
            {sortedChecklists.length === 0 && (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                No checklists found
              </div>
            )}
          </div>
        )}
      </div>

      <ChecklistForm
        contactId={contactId}
        checklist={currentChecklist}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSuccess={handleChecklistSaved}
      />
    </div>
  );
}
