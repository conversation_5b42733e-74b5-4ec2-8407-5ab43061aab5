import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

export async function GET(request, { params }) {
  try {
    const { id } = await params;
    console.log('Fetching upcoming tasks for contact:', id);

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day

    // Get upcoming tasks from the database (not completed and due date >= today)
    const upcomingTasks = await prisma.task.findMany({
      where: { 
        contactId: id,
        dueDate: {
          gte: today
        },
        completed: false
      },
      orderBy: { dueDate: 'asc' }
    });

    console.log('Found upcoming tasks:', upcomingTasks.length);

    return NextResponse.json(upcomingTasks);
  } catch (error) {
    console.error('Failed to fetch upcoming tasks:', error);
    return NextResponse.json(
      { error: `Failed to fetch upcoming tasks: ${error.message}` },
      { status: 500 }
    );
  }
}
