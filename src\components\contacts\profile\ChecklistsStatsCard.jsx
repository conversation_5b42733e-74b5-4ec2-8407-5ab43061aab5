'use client';

import { useState, useEffect } from 'react';
import { Info } from 'lucide-react';
import Tooltip from '@/components/ui/Tooltip';
import { useStatsListener } from '@/hooks/useStatsRefresh';

export default function ChecklistsStatsCard({ contactId }) {
  const [stats, setStats] = useState({
    total: 0,
    completed: 0,
    inProgress: 0,
    items: {
      total: 0,
      completed: 0
    }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Listen for refresh events - only refresh when checklist data changes
  const refreshCounter = useStatsListener(['checklists']);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/contacts/${contactId}/checklists/stats`);

        if (!response.ok) {
          throw new Error('Failed to fetch checklist stats');
        }

        const data = await response.json();
        setStats(data);
      } catch (err) {
        console.error('Error fetching checklist stats:', err);
        setError('Failed to load checklist statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [contactId, refreshCounter]);

  if (loading) {
    return (
      <div className="p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
        <div className="flex justify-between items-center">
          <h4 className="text-sm font-medium text-green-800 dark:text-green-300">Checklists</h4>
        </div>
        <div className="flex justify-center items-center py-4">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
        <div className="flex justify-between items-center">
          <h4 className="text-sm font-medium text-green-800 dark:text-green-300">Checklists</h4>
        </div>
        <div className="text-xs text-red-600 dark:text-red-400 mt-2">{error}</div>
      </div>
    );
  }

  return (
    <div className="p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
      <div className="flex justify-between items-center">
        <h4 className="text-sm font-medium text-green-800 dark:text-green-300">Checklists</h4>
        <Tooltip
          content={
            <div className="w-64">
              <p className="font-medium mb-1">Checklists</p>
              <p className="text-xs mb-1">Shows progress on checklists for this contact.</p>
            </div>
          }
          position="bottom"
        >
          <div className="text-green-500 dark:text-green-400 cursor-help">
            <Info className="h-3.5 w-3.5" />
          </div>
        </Tooltip>
      </div>

      <div className="mt-2 grid grid-cols-2 gap-2">
        <div className="text-center">
          <div className="text-xl font-semibold text-green-700 dark:text-green-300">{stats.total}</div>
          <div className="text-xs text-green-600 dark:text-green-400">Total Checklists</div>
        </div>
        <div className="text-center">
          <div className="text-xl font-semibold text-green-700 dark:text-green-300">{stats.completed}</div>
          <div className="text-xs text-green-600 dark:text-green-400">Completed</div>
        </div>
      </div>

      {/* Item completion progress */}
      <div className="mt-3">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs text-green-700 dark:text-green-400">Items Completion</span>
          <span className="text-xs font-medium text-green-800 dark:text-green-300">
            {stats.items.total > 0
              ? Math.round((stats.items.completed / stats.items.total) * 100)
              : 0}%
          </span>
        </div>
        <div className="w-full bg-green-200 dark:bg-green-800 rounded-full h-1.5">
          <div
            className="bg-green-500 h-1.5 rounded-full"
            style={{
              width: `${stats.items.total > 0
                ? Math.round((stats.items.completed / stats.items.total) * 100)
                : 0}%`
            }}
          ></div>
        </div>
        <div className="mt-1 text-xs text-green-600 dark:text-green-500 text-center">
          {stats.items.completed} of {stats.items.total} items completed
        </div>
      </div>
    </div>
  );
}
