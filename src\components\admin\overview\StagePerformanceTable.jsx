"use client";

import { 
  Clock, 
  Users, 
  Activity, 
  TrendingUp,
  TrendingDown,
  Minus
} from "lucide-react";

export default function StagePerformanceTable({ data }) {
  if (!data || data.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Stage Performance
        </h3>
        <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
          No stage performance data available
        </div>
      </div>
    );
  }

  const formatDuration = (days) => {
    if (!days) return 'N/A';
    if (days < 1) return '< 1 day';
    if (days === 1) return '1 day';
    return `${Math.round(days)} days`;
  };

  const getPerformanceIndicator = (value, threshold = 30) => {
    if (!value) return <Minus className="h-4 w-4 text-gray-400" />;
    if (value > threshold) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <TrendingUp className="h-4 w-4 text-green-500" />;
  };

  const getPerformanceColor = (value, threshold = 30) => {
    if (!value) return 'text-gray-500 dark:text-gray-400';
    if (value > threshold) return 'text-red-600 dark:text-red-400';
    return 'text-green-600 dark:text-green-400';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Stage Performance Analysis
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Detailed metrics for each pipeline stage
        </p>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Stage
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Current Contacts
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Avg Duration
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Activities
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Activities/Contact
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Performance
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {data.map((stage, index) => (
              <tr key={stage.stageId || index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {stage.order || index + 1}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {stage.stage}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-gray-400 mr-2" />
                    <div className="text-sm text-gray-900 dark:text-white">
                      {stage.currentContacts || 0}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-gray-400 mr-2" />
                    <div className={`text-sm ${getPerformanceColor(stage.averageDuration)}`}>
                      {formatDuration(stage.averageDuration)}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Activity className="h-4 w-4 text-gray-400 mr-2" />
                    <div className="text-sm text-gray-900 dark:text-white">
                      {stage.stageActivities || 0}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {stage.activitiesPerContact ? stage.activitiesPerContact.toFixed(1) : '0.0'}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {getPerformanceIndicator(stage.averageDuration)}
                    <span className={`ml-2 text-sm ${getPerformanceColor(stage.averageDuration)}`}>
                      {stage.averageDuration > 30 ? 'Slow' : stage.averageDuration > 0 ? 'Good' : 'N/A'}
                    </span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Summary Statistics */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Total Contacts in Pipeline */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Total in Pipeline
            </h4>
            <Users className="h-4 w-4 text-blue-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {data.reduce((sum, stage) => sum + (stage.currentContacts || 0), 0)}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Across all stages
          </p>
        </div>

        {/* Average Stage Duration */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Avg Duration
            </h4>
            <Clock className="h-4 w-4 text-green-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {(() => {
              const validDurations = data.filter(s => s.averageDuration).map(s => s.averageDuration);
              const avg = validDurations.length > 0 
                ? validDurations.reduce((sum, d) => sum + d, 0) / validDurations.length 
                : 0;
              return formatDuration(avg);
            })()}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Per stage average
          </p>
        </div>

        {/* Total Activities */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Total Activities
            </h4>
            <Activity className="h-4 w-4 text-orange-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {data.reduce((sum, stage) => sum + (stage.stageActivities || 0), 0)}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            In selected period
          </p>
        </div>

        {/* Most Active Stage */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Most Active
            </h4>
            <TrendingUp className="h-4 w-4 text-purple-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {(() => {
              const mostActive = data.reduce((max, stage) => 
                (stage.stageActivities || 0) > (max.stageActivities || 0) ? stage : max
              , data[0] || {});
              return mostActive.stage ? mostActive.stage.substring(0, 10) + (mostActive.stage.length > 10 ? '...' : '') : 'N/A';
            })()}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Highest activity count
          </p>
        </div>
      </div>

      {/* Performance Insights */}
      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">
          Performance Insights
        </h4>
        <div className="space-y-2 text-sm">
          {(() => {
            const slowStages = data.filter(s => s.averageDuration > 30);
            const fastStages = data.filter(s => s.averageDuration > 0 && s.averageDuration <= 7);
            const lowActivityStages = data.filter(s => s.activitiesPerContact < 1);
            
            const insights = [];
            
            if (slowStages.length > 0) {
              insights.push(
                <div key="slow" className="flex items-start space-x-2">
                  <TrendingDown className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <span className="text-blue-800 dark:text-blue-200">
                    <strong>{slowStages.length}</strong> stage{slowStages.length > 1 ? 's' : ''} taking longer than 30 days on average
                  </span>
                </div>
              );
            }
            
            if (fastStages.length > 0) {
              insights.push(
                <div key="fast" className="flex items-start space-x-2">
                  <TrendingUp className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-blue-800 dark:text-blue-200">
                    <strong>{fastStages.length}</strong> stage{fastStages.length > 1 ? 's' : ''} moving quickly (≤7 days average)
                  </span>
                </div>
              );
            }
            
            if (lowActivityStages.length > 0) {
              insights.push(
                <div key="low-activity" className="flex items-start space-x-2">
                  <Activity className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span className="text-blue-800 dark:text-blue-200">
                    <strong>{lowActivityStages.length}</strong> stage{lowActivityStages.length > 1 ? 's' : ''} with low activity levels (&lt; 1 per contact)
                  </span>
                </div>
              );
            }
            
            if (insights.length === 0) {
              insights.push(
                <div key="good" className="flex items-start space-x-2">
                  <TrendingUp className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-blue-800 dark:text-blue-200">
                    All stages are performing within normal parameters
                  </span>
                </div>
              );
            }
            
            return insights;
          })()}
        </div>
      </div>
    </div>
  );
}
