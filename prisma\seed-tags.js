const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedTags() {
  console.log('Seeding tags...');

  const defaultTags = [
    // Contact tags
    { name: 'VIP', type: 'Contact' },
    { name: 'Lead', type: 'Contact' },
    { name: 'Client', type: 'Contact' },
    { name: 'Prospect', type: 'Contact' },
    { name: 'Hot Lead', type: 'Contact' },
    { name: 'Cold Lead', type: 'Contact' },
    { name: 'Referral', type: 'Contact' },
    
    // Document tags
    { name: 'Contract', type: 'Document' },
    { name: 'Invoice', type: 'Document' },
    { name: 'Proposal', type: 'Document' },
    { name: 'Report', type: 'Document' },
    { name: 'Legal', type: 'Document' },
    { name: 'Financial', type: 'Document' },
    { name: 'Marketing', type: 'Document' },
    { name: 'Account Form', type: 'Document' },
    { name: 'W9', type: 'Document' },
    { name: 'W8', type: 'Document' },
    { name: 'Customer Agreement', type: 'Document' },
    
    // Email tags
    { name: 'Newsletter', type: 'Email' },
    { name: 'Promotion', type: 'Email' },
    { name: 'Follow-up', type: 'Email' },
    { name: 'Welcome', type: 'Email' },
    { name: 'Announcement', type: 'Email' },
    { name: 'Survey', type: 'Email' },
    { name: 'Event', type: 'Email' }
  ];

  for (const tag of defaultTags) {
    try {
      await prisma.tag.upsert({
        where: {
          name_type: {
            name: tag.name,
            type: tag.type
          }
        },
        update: {},
        create: tag
      });
      console.log(`✓ Created/updated ${tag.type} tag: ${tag.name}`);
    } catch (error) {
      console.error(`✗ Failed to create ${tag.type} tag: ${tag.name}`, error.message);
    }
  }

  console.log('Tags seeding completed!');
}

seedTags()
  .catch((e) => {
    console.error('Error seeding tags:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
