import { useState } from "react";
import { toast } from "react-hot-toast";

const INSURANCE_STAGES = [
  { key: "gathering_client_info", label: "Gathering Client Info", dateField: null },
  { key: "submitted_client_info", label: "Client Info Submitted", dateField: "insuranceClientInfoSubmittedDate" },
  { key: "received_quote", label: "Quote Received", dateField: "insuranceQuoteReceivedDate" },
  { key: "sent_questionnaire", label: "Questionnaire Sent", dateField: "insuranceQuestionnaireSentToClientDate" },
  { key: "submitted_to_carrier", label: "Documents Submitted to Carrier", dateField: "insuranceDocumentsSubmittedToCarrierDate" },
  { key: "completed", label: "Completed", dateField: "insuranceCompletionDate" },
];

function formatDate(date) {
  if (!date) return "-";
  const d = new Date(date);
  if (isNaN(d)) return "-";
  // Always display as UTC date string, not local
  return d.toLocaleDateString(undefined, { timeZone: 'UTC' });
}

export default function InsuranceProcessDiagram({ contact }) {
  const [showDateInput, setShowDateInput] = useState({});
  const [updatingField, setUpdatingField] = useState(null); // for loading state
  const [localContact, setLocalContact] = useState(contact); // for optimistic UI

  async function handleDateSave(field, value) {
    setUpdatingField(field);
    try {
      const res = await fetch(`/api/contacts/${contact.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ [field]: value }),
      });
      if (!res.ok) throw new Error('Failed to update date');
      const updated = await res.json();
      setLocalContact(updated);
      toast.success('Date updated');
    } catch (err) {
      toast.error('Failed to update date');
    } finally {
      setUpdatingField(null);
    }
  }

  return (
    <div className="flex flex-row gap-2 items-center justify-center flex-wrap">
      {INSURANCE_STAGES.map((stage, idx) => {
        // Green if Gathering Client Info (always), or if date exists
        const hasDate = stage.dateField ? !!localContact[stage.dateField] : true;
        const completed = hasDate;
        const date = stage.dateField && localContact[stage.dateField] ? formatDate(localContact[stage.dateField]) : null;
        const pillKey = stage.key;
        const isInputOpen = !!showDateInput[pillKey];
        const handlePillClick = () => {
          if (!stage.dateField) return;
          setShowDateInput(prev => ({ ...prev, [pillKey]: true }));
        };
        const handleDateChange = async (e) => {
          const newValue = e.target.value;
          setShowDateInput(prev => ({ ...prev, [pillKey]: false }));
          if (newValue && newValue !== (localContact[stage.dateField]?.slice(0, 10) || '')) {
            await handleDateSave(stage.dateField, newValue);
          }
        };
        return (
          <div key={stage.key} className="flex flex-row items-center">
            <div className={`flex flex-col items-center`}>
              <button
                type="button"
                className={`px-6 py-3 min-w-[180px] flex flex-col items-center justify-center rounded-full font-medium text-base shadow transition-all duration-200 focus:outline-none border-2 ${completed ? 'bg-green-500 text-white border-green-600' : 'bg-gray-200 text-gray-700 border-gray-300 hover:bg-gray-300'} ${stage.dateField ? 'cursor-pointer' : 'cursor-default'} ${updatingField === stage.dateField ? 'opacity-60 pointer-events-none' : ''}`}
                style={{ fontWeight: 500, fontSize: '1rem', minHeight: 56 }}
                onClick={handlePillClick}
                disabled={!stage.dateField || updatingField === stage.dateField}
                title={stage.dateField ? (completed ? 'Edit Date' : 'Add Date') : ''}
              >
                <span>{stage.label}</span>
                {/* Only show date if not Gathering Client Info and date exists */}
                {stage.dateField && (isInputOpen ? (
                  <input
                    type="date"
                    className="mt-1 px-2 py-1 rounded text-gray-900 text-xs border border-gray-400 focus:ring-2 focus:ring-primary"
                    defaultValue={localContact[stage.dateField] ? new Date(localContact[stage.dateField]).toISOString().slice(0, 10) : ''}
                    max={new Date().toISOString().slice(0, 10)}
                    onBlur={handleDateChange}
                    onKeyDown={e => { if (e.key === 'Enter') { e.target.blur(); } }}
                    autoFocus
                    disabled={updatingField === stage.dateField}
                  />
                ) : date && (
                  <span className="text-xs font-normal mt-1 tracking-tight" style={{ color: completed ? 'rgba(255,255,255,0.9)' : '#555' }}>{date}</span>
                ))}
              </button>
            </div>
            {idx < INSURANCE_STAGES.length - 1 && (
              <div className="flex items-center mx-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
