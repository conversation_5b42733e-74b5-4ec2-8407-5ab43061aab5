import { NextResponse } from 'next/server';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

export async function GET(request) {
  try {
    const { user } = await getSignedInUser(request);
    
    // Check if user is admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    const eventTypes = await auditLogger.getEventTypes();
    return NextResponse.json(eventTypes);
  } catch (error) {
    console.error('Error fetching event types:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event types' },
      { status: 500 }
    );
  }
}
