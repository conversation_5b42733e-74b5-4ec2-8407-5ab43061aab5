import { NextResponse } from 'next/server';
import { getSignedInUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma/client';
import { auditLogger } from '@/lib/services/auditLogger';

// GET /api/workflows - Get all workflows for the current user
export async function GET(request) {
  try {
    const { user } = await getSignedInUser(request);

    if (!user) {
      console.log('WORKFLOWS API: No user found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;
    console.log('WORKFLOWS API: Fetching workflows for user ID:', userId);

    // First, let's check if there are any workflows in the database at all
    const allWorkflows = await prisma.workflow.findMany({
      select: {
        id: true,
        name: true,
        userId: true
      }
    });
    console.log('WORKFLOWS API: Total workflows in database:', allWorkflows.length);
    console.log('WORKFLOWS API: All workflow user IDs:', allWorkflows.map(w => ({ id: w.id, name: w.name, userId: w.userId })));

    // Get all workflows for the user
    const workflows = await prisma.workflow.findMany({
      where: { userId },
      include: {
        trigger: true,
        conditions: {
          orderBy: { order: 'asc' }
        },
        actions: {
          orderBy: { order: 'asc' }
        }
      },
      orderBy: { updatedAt: 'desc' }
    });

    console.log('WORKFLOWS API: Workflows found for current user:', workflows.length);
    return NextResponse.json(workflows);
  } catch (error) {
    console.error('Error fetching workflows:', error);
    return NextResponse.json({ error: 'Failed to fetch workflows' }, { status: 500 });
  }
}

// POST /api/workflows - Create a new workflow
export async function POST(request) {
  try {
    const { user } = await getSignedInUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;
    const data = await request.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: 'Workflow name is required' }, { status: 400 });
    }

    if (!data.trigger) {
      return NextResponse.json({ error: 'A trigger is required' }, { status: 400 });
    }

    if (!data.actions || data.actions.length === 0) {
      return NextResponse.json({ error: 'At least one action is required' }, { status: 400 });
    }

    // Create the workflow with nested creates for trigger, conditions, and actions
    const workflow = await prisma.workflow.create({
      data: {
        name: data.name,
        description: data.description || '',
        isActive: data.isActive !== undefined ? data.isActive : true,
        userId,
        trigger: {
          create: {
            triggerType: data.trigger.triggerType,
            config: data.trigger.config || {}
          }
        },
        conditions: {
          create: data.conditions?.map((condition, index) => ({
            field: condition.field,
            operator: condition.operator,
            value: condition.value,
            order: index + 1
          })) || []
        },
        actions: {
          create: data.actions.map((action, index) => ({
            actionType: action.actionType,
            config: action.config || {},
            order: index + 1
          }))
        }
      },
      include: {
        trigger: true,
        conditions: true,
        actions: true
      }
    });

    // Log the workflow creation
    await auditLogger.logWorkflowCreate({
      userId,
      workflow,
      request
    });

    return NextResponse.json(workflow, { status: 201 });
  } catch (error) {
    console.error('Error creating workflow:', error);
    return NextResponse.json({ error: 'Failed to create workflow' }, { status: 500 });
  }
}
