export default function Preview({ content, title, className = "" }) {
  return (
    <div className={`bg-white dark:bg-gray-800 ${className}`}>
      <div className="max-w-[600px] mx-auto p-6 space-y-6">
        {title && (
          <h1 className="text-2xl font-semibold text-center text-gray-900 dark:text-gray-100">
            {title}
          </h1>
        )}
        <div
          className="prose dark:prose-invert max-w-none"
          dangerouslySetInnerHTML={{ __html: content }}
        />
      </div>
    </div>
  );
}
