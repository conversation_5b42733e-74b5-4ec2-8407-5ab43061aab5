'use client';

import { useState, useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { X, Plus, Trash2 } from 'lucide-react';

export default function ChecklistForm({
  contactId,
  checklist = null,
  isOpen,
  onClose,
  onSuccess
}) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    status: 'in_progress',
    items: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // If editing an existing checklist, populate the form
  useEffect(() => {
    if (checklist) {
      setFormData({
        title: checklist.title,
        description: checklist.description || '',
        category: checklist.category || '',
        status: checklist.status,
        items: checklist.items || []
      });
    } else {
      // Reset form for new checklist
      setFormData({
        title: '',
        description: '',
        category: '',
        status: 'in_progress',
        items: [{ text: '', description: '', completed: false }] // Start with one empty item
      });
    }
  }, [checklist, isOpen]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    // Validate form
    if (!formData.title.trim()) {
      setError('Title is required');
      setIsSubmitting(false);
      return;
    }

    // Filter out empty items
    const validItems = formData.items.filter(item => item.text.trim() !== '');
    
    if (validItems.length === 0) {
      setError('At least one checklist item is required');
      setIsSubmitting(false);
      return;
    }

    try {
      let url, method, body;

      if (checklist) {
        // Update existing checklist
        url = `/api/checklists/${checklist.id}`;
        method = 'PATCH';
        
        // For updates, we only send the checklist data without items
        body = {
          title: formData.title,
          description: formData.description,
          category: formData.category,
          status: formData.status
        };
        
        // Send the update request
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update checklist');
        }

        // Get the updated checklist
        let updatedChecklist = await response.json();
        
        // Handle items separately - first get existing items
        const existingItemsResponse = await fetch(`/api/checklists/${checklist.id}/items`);
        const existingItems = await existingItemsResponse.json();
        
        // Create a map of existing items by ID
        const existingItemsMap = new Map(existingItems.map(item => [item.id, item]));
        
        // Process each item from the form
        for (const item of validItems) {
          if (item.id) {
            // Update existing item
            await fetch(`/api/checklist-items/${item.id}`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                text: item.text,
                description: item.description,
                completed: item.completed
              })
            });
            
            // Remove from map to track which ones were processed
            existingItemsMap.delete(item.id);
          } else {
            // Create new item
            await fetch(`/api/checklists/${checklist.id}/items`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                text: item.text,
                description: item.description,
                completed: item.completed
              })
            });
          }
        }
        
        // Delete any items that weren't in the form
        for (const [itemId] of existingItemsMap) {
          await fetch(`/api/checklist-items/${itemId}`, {
            method: 'DELETE'
          });
        }
        
        // Fetch the final updated checklist with all items
        const finalResponse = await fetch(`/api/checklists/${checklist.id}`);
        updatedChecklist = await finalResponse.json();
        
        onSuccess(updatedChecklist);
      } else {
        // Create new checklist
        url = `/api/contacts/${contactId}/checklists`;
        method = 'POST';
        body = {
          ...formData,
          items: validItems
        };
        
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create checklist');
        }

        const newChecklist = await response.json();
        onSuccess(newChecklist);
      }

      // Close the form
      onClose();
    } catch (error) {
      console.error('Error saving checklist:', error);
      setError(error.message || 'Failed to save checklist. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addItem = () => {
    setFormData({
      ...formData,
      items: [
        ...formData.items,
        { text: '', description: '', completed: false }
      ]
    });
  };

  const updateItem = (index, field, value) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };
    
    setFormData({
      ...formData,
      items: updatedItems
    });
  };

  const removeItem = (index) => {
    // Don't allow removing the last item
    if (formData.items.length <= 1) {
      return;
    }
    
    const updatedItems = formData.items.filter((_, i) => i !== index);
    
    setFormData({
      ...formData,
      items: updatedItems
    });
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-[100]" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto z-[110]">
          <div className="flex justify-between items-center mb-4">
            <Dialog.Title className="text-xl font-semibold text-gray-900 dark:text-white">
              {checklist ? 'Edit Checklist' : 'Add New Checklist'}
            </Dialog.Title>
            <Dialog.Close className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <X className="h-5 w-5" />
            </Dialog.Close>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Category
              </label>
              <input
                type="text"
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="e.g., Onboarding, Financial Review, etc."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="archived">Archived</option>
              </select>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Checklist Items *
                </label>
                <button
                  type="button"
                  onClick={addItem}
                  className="flex items-center text-sm text-primary hover:text-primary-hover"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Item
                </button>
              </div>
              
              <div className="space-y-3 max-h-60 overflow-y-auto p-1">
                {formData.items.map((item, index) => (
                  <div key={index} className="flex items-start gap-2 p-3 border border-gray-200 dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-700/50">
                    <div className="flex-1 space-y-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                          Item Text *
                        </label>
                        <input
                          type="text"
                          value={item.text}
                          onChange={(e) => updateItem(index, 'text', e.target.value)}
                          className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                          Description
                        </label>
                        <input
                          type="text"
                          value={item.description || ''}
                          onChange={(e) => updateItem(index, 'description', e.target.value)}
                          className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                        />
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`completed-${index}`}
                          checked={item.completed}
                          onChange={(e) => updateItem(index, 'completed', e.target.checked)}
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                        />
                        <label htmlFor={`completed-${index}`} className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Completed
                        </label>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeItem(index)}
                      className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1"
                      title="Remove item"
                      disabled={formData.items.length <= 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover disabled:opacity-50"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : checklist ? 'Update' : 'Add'}
              </button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
