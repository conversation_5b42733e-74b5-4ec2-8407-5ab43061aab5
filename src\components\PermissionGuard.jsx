import { usePermissions, useRole } from '@/hooks/usePermissions';

/**
 * PermissionGuard component that conditionally renders children based on user permissions
 *
 * @param {Object} props - Component props
 * @param {string|string[]} props.permissions - Required permission(s) to render children
 * @param {React.ReactNode} props.children - Children to render if user has permission
 * @param {React.ReactNode} props.fallback - Optional fallback to render if user doesn't have permission
 * @returns {React.ReactNode} - Children if user has permission, fallback or null otherwise
 */
export default function PermissionGuard({ permissions, children, fallback = null }) {

  const { hasPermission, loading } = usePermissions(permissions);

  // While loading, don't render anything
  if (loading) {
    return null;
  }

  // If user has permission, render children
  if (hasPermission) {
    return children;
  }

  // Otherwise, render fallback or null
  return fallback;
}

/**
 * RoleGuard component that conditionally renders children based on user role
 *
 * @param {Object} props - Component props
 * @param {string|string[]} props.roles - Required role(s) to render children
 * @param {React.ReactNode} props.children - Children to render if user has the role
 * @param {React.ReactNode} props.fallback - Optional fallback to render if user doesn't have the role
 * @returns {React.ReactNode} - Children if user has the role, fallback or null otherwise
 */
export function RoleGuard({ roles, children, fallback = null }) {
  const { hasRole, loading } = useRole(roles);

  // While loading, don't render anything
  if (loading) {
    return null;
  }

  // If user has the role, render children
  if (hasRole) {
    return children;
  }

  // Otherwise, render fallback or null
  return fallback;
}
