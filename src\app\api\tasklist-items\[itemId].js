import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { NextResponse } from 'next/server';

// PATCH: Update a tasklist item
export async function PATCH(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { itemId } = await params;
    const data = await request.json();
    // Only allow access to user's own items unless admin
    const item = await prisma.tasklistItem.findUnique({ where: { id: itemId }, include: { tasklist: true } });
    if (!item) {
      return NextResponse.json({ error: 'Tasklist item not found' }, { status: 404 });
    }
    if (user.role !== 'admin' && item.tasklist.contactId !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    const updated = await prisma.tasklistItem.update({ where: { id: itemId }, data });
    return NextResponse.json(updated);
  } catch (error) {
    console.error('Failed to update tasklist item:', error);
    return NextResponse.json({ error: 'Failed to update tasklist item' }, { status: 500 });
  }
}

// DELETE: Delete a tasklist item
export async function DELETE(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { itemId } = await params;
    const item = await prisma.tasklistItem.findUnique({ where: { id: itemId }, include: { tasklist: true } });
    if (!item) {
      return NextResponse.json({ error: 'Tasklist item not found' }, { status: 404 });
    }
    if (user.role !== 'admin' && item.tasklist.contactId !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    await prisma.tasklistItem.delete({ where: { id: itemId } });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete tasklist item:', error);
    return NextResponse.json({ error: 'Failed to delete tasklist item' }, { status: 500 });
  }
}
