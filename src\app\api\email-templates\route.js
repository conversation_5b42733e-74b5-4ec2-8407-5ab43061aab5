import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";
import { auditLogger } from "@/lib/services/auditLogger";
// import { getSession } from "@/lib/auth";

export async function POST(request) {
  try {
    const { user } = await getSignedInUser(request);
    const data = await request.json();
    const { searchParams } = new URL(request.url);
    console.log("The email template data:", data);
    const scope = searchParams.get("scope"); //global

    if (scope === "global") {
      const template = await prisma.emailTemplate.create({
        data: {
          ...data,
          userId: user.id,
          content: data.content,
          isGlobal: true, // Mark as global
          isActiveGlobal: true, // Set as active by default
        },
      });

      // Fetch all global templates to return
      const globalTemplates = await prisma.emailTemplate.findMany({
        where: {
          isGlobal: true,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
      return NextResponse.json(globalTemplates);
    } else {
      const template = await prisma.emailTemplate.create({
        data: {
          ...data,
          userId: user.id,
          content: data.content, // Store content directly, no processing needed
        },
      });

      // Log the email template creation
      await auditLogger.logEmailTemplateCreate({
        userId: user.id,
        template,
        request
      });

      return NextResponse.json(template);
    }
  } catch (error) {
    console.error("Failed to create template:", error);
    return NextResponse.json(
      { error: "Failed to create template: " + error.message },
      { status: 500 }
    );
  }
}

export async function GET(request) {
  try {
    // Get the user

    const { user } = await getSignedInUser(request);
    const { searchParams } = new URL(request.url);
    const scope = searchParams.get("scope"); // personal or global

    if (scope === "global") {
      const templates = await prisma.emailTemplate.findMany({
        where: {
          isGlobal: true,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
      return NextResponse.json(templates);
    } else {
      const templates = await prisma.emailTemplate.findMany({
        where: {
          OR: [
            {
              userId: user.id, // Personal templates
            },
            {
              isGlobal: true, // Global templates
              isActiveGlobal: true, // Only active global templates
            },
          ],
        },
        orderBy: {
          createdAt: "desc",
        },
      });
      return NextResponse.json(templates);
    }
  } catch (error) {
    console.error("Failed to fetch templates:", error);
    return NextResponse.json(
      { error: "Failed to fetch templates" },
      { status: 500 }
    );
  }
}
