import { NextResponse } from "next/server";

export async function GET(request, { params }) {
  try {
    // Generate mock daily stats for the last 14 days
    const dailyStats = Array.from({ length: 14 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (13 - i));
      
      return {
        date: date.toISOString(),
        opens: Math.floor(Math.random() * 100) + 50,
        clicks: Math.floor(Math.random() * 50) + 20,
        bounces: Math.floor(Math.random() * 5),
      };
    });

    // Calculate totals
    const delivered = 1000;
    const opened = 750;
    const clicked = 320;
    const bounced = 25;

    const mockAnalytics = {
      delivered,
      opened,
      clicked,
      bounced,
      openRate: opened / delivered,
      clickRate: clicked / delivered,
      bounceRate: bounced / delivered,
      clickThroughRate: clicked / opened,
      dailyStats,
      // Additional metrics
      uniqueOpens: Math.floor(opened * 0.8),
      uniqueClicks: Math.floor(clicked * 0.7),
      unsubscribes: Math.floor(Math.random() * 10),
      complaints: Math.floor(Math.random() * 5),
      averageEngagementTime: "2.5 minutes",
      deviceBreakdown: {
        mobile: 45,
        desktop: 40,
        tablet: 15,
      },
      topLocations: [
        { location: "United States", percentage: 45 },
        { location: "United Kingdom", percentage: 20 },
        { location: "Canada", percentage: 15 },
        { location: "Australia", percentage: 10 },
        { location: "Other", percentage: 10 },
      ],
      bestSendTime: {
        day: "Tuesday",
        time: "10:00 AM",
      },
    };

    return NextResponse.json(mockAnalytics);
  } catch (error) {
    console.error("Error generating mock campaign analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch campaign analytics" },
      { status: 500 }
    );
  }
}