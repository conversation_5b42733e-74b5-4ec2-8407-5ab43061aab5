'use client';

import TasksTab from './TasksTab';
import { useStatsRefresh } from '@/hooks/useStatsRefresh';

export default function TasksTabWithRefresh({ contactId }) {
  const { triggerRefresh } = useStatsRefresh();
  
  // Function to handle task changes
  const handleTaskChange = () => {
    // Trigger a refresh of the stats
    triggerRefresh(['tasks']);
  };
  
  return <TasksTab contactId={contactId} onTaskChange={handleTaskChange} />;
}
