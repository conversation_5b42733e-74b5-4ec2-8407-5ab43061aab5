import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

// GET /api/documents/[id] - Get a document by ID
export async function GET(request, context) {
  try {
    const { params } = context;
    const { id: documentId } = await params;
    
    const document = await prisma.document.findUnique({
      where: {
        id: documentId
      },
      include: {
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });
    
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }
    
    // Format the document for the response
    const formattedDocument = {
      id: document.id,
      name: document.name,
      type: document.fileType,
      size: document.fileSize,
      tags: document.tags || [],
      uploadedAt: document.uploadedAt,
      uploadedBy: document.uploadedBy,
      contactId: document.contactId,
      contactName: document.contact ? `${document.contact.firstName} ${document.contact.lastName}` : null,
      source: document.source,
      folder: document.folder,
      url: document.url,
      externalId: document.externalId,
      driveId: document.driveId
    };
    
    return NextResponse.json(formattedDocument);
  } catch (error) {
    console.error('Failed to fetch document:', error);
    return NextResponse.json(
      { error: 'Failed to fetch document: ' + error.message },
      { status: 500 }
    );
  }
}

// PATCH /api/documents/[id] - Update a document
export async function PATCH(request, context) {
  try {
    const { params } = context;
    const { id: documentId } = await params;
    const data = await request.json();

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Check if document exists
    const existingDocument = await prisma.document.findUnique({
      where: {
        id: documentId
      }
    });

    if (!existingDocument) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Update the document
    const updatedDocument = await prisma.document.update({
      where: {
        id: documentId
      },
      data: {
        name: data.name !== undefined ? data.name : undefined,
        fileType: data.fileType !== undefined ? data.fileType : undefined,
        fileSize: data.fileSize !== undefined ? data.fileSize : undefined,
        tags: data.tags !== undefined ? data.tags : undefined,
        contactId: data.contactId !== undefined ? data.contactId : undefined,
        source: data.source !== undefined ? data.source : undefined,
        folder: data.folder !== undefined ? data.folder : undefined,
        url: data.url !== undefined ? data.url : undefined,
        externalId: data.externalId !== undefined ? data.externalId : undefined,
        driveId: data.driveId !== undefined ? data.driveId : undefined
      }
    });

    // Log the document update
    await auditLogger.logDocumentUpdate({
      userId: user?.id,
      documentId,
      oldValues: existingDocument,
      newValues: updatedDocument,
      request
    });

    return NextResponse.json(updatedDocument);
  } catch (error) {
    console.error('Failed to update document:', error);
    return NextResponse.json(
      { error: 'Failed to update document: ' + error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/documents/[id] - Delete a document
export async function DELETE(request, context) {
  try {
    const { params } = context;
    const documentId = params.id;

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Check if document exists
    const existingDocument = await prisma.document.findUnique({
      where: {
        id: documentId
      }
    });

    if (!existingDocument) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Log the document deletion
    await auditLogger.logDocumentDelete({
      userId: user?.id,
      documentId,
      oldValues: existingDocument,
      request
    });

    // Delete the document
    await prisma.document.delete({
      where: {
        id: documentId
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete document:', error);
    return NextResponse.json(
      { error: 'Failed to delete document: ' + error.message },
      { status: 500 }
    );
  }
}
