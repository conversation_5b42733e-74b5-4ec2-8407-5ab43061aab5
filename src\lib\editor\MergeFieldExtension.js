import { Extension } from '@tiptap/core';
import { Plugin, Plugin<PERSON>ey } from 'prosemirror-state';

export const MergeFieldExtension = Extension.create({
  name: 'mergeField',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new Plugin<PERSON>ey('mergeField'),
        props: {
          decorations: (state) => {
            const { doc } = state;
            const decorations = [];
            
            const mergeFieldRegex = /{{([^}]+)}}/g;
            
            doc.descendants((node, pos) => {
              if (node.isText) {
                let match;
                while ((match = mergeFieldRegex.exec(node.text)) !== null) {
                  const start = pos + match.index;
                  const end = start + match[0].length;
                  
                  decorations.push(
                    Decoration.inline(start, end, {
                      class: 'merge-field',
                    })
                  );
                }
              }
            });

            return DecorationSet.create(doc, decorations);
          },
        },
      }),
    ];
  },
});