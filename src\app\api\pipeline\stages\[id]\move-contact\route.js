import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

// Handle moving a contact to a stage and executing any configured actions
export async function POST(request, context) {
  // Await the params to fix the Next.js warning
  const { params } = context;
  try {
    const body = await request.json();
    const { contactId } = body;

    if (!contactId) {
      return NextResponse.json(
        { error: 'Contact ID is required' },
        { status: 400 }
      );
    }

    // Get the contact
    const contact = await prisma.contact.findUnique({
      where: { id: contactId }
    });

    if (!contact) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }

    // Get the contact's current pipeline stage
    const currentStage = contact.pipelineStage;

    // Get the stage ID and ensure it's properly awaited
    const stageId = params.id;

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Update the contact's pipeline stage
    const updatedContact = await prisma.contact.update({
      where: { id: contactId },
      data: {
        pipelineStage: stageId,
        updatedAt: new Date()
      }
    });

    // Log the contact stage change
    await auditLogger.logContactStageChange({
      userId: user?.id,
      contactId,
      oldStage: currentStage,
      newStage: stageId,
      request
    });

    // If the contact was previously unassigned (no pipeline stage),
    // log this as a new entry to the pipeline
    if (!currentStage) {
      console.log(`Contact ${contactId} entered the pipeline at stage ${stageId}`);
    }

    // Get any actions configured for this stage
    const stageActions = await prisma.stageAction.findMany({
      where: { stageId: stageId }
    });

    // Execute each action
    const actionResults = await Promise.all(
      stageActions.map(async (action) => {
        try {
          switch (action.actionType) {
            case 'activity':
              // Create an activity
              const activity = await prisma.activity.create({
                data: {
                  type: action.actionDetails.type,
                  description: action.actionDetails.description,
                  contactId: contactId,
                  date: new Date()
                }
              });
              return { success: true, type: 'activity', id: activity.id };

            case 'task':
              // Create a task
              const dueDate = new Date();
              dueDate.setDate(dueDate.getDate() + (action.actionDetails.dueOffset || 1));

              const task = await prisma.task.create({
                data: {
                  title: action.actionDetails.title,
                  description: action.actionDetails.description || '',
                  dueDate: dueDate,
                  priority: action.actionDetails.priority || 'medium',
                  contactId: contactId
                }
              });
              return { success: true, type: 'task', id: task.id };

            case 'note':
              // Create a note
              const note = await prisma.note.create({
                data: {
                  title: action.actionDetails.title,
                  content: action.actionDetails.content,
                  contactId: contactId
                }
              });
              return { success: true, type: 'note', id: note.id };

            case 'email':
              // For now, just log the email that would be sent
              // In a real implementation, this would send an actual email
              console.log(`Would send email: ${action.actionDetails.subject} to contact ${contactId}`);
              return {
                success: true,
                type: 'email',
                details: {
                  subject: action.actionDetails.subject,
                  body: action.actionDetails.body
                }
              };

            default:
              return { success: false, type: action.actionType, error: 'Unknown action type' };
          }
        } catch (error) {
          console.error(`Failed to execute action ${action.id}:`, error);
          return { success: false, type: action.actionType, error: error.message };
        }
      })
    );

    return NextResponse.json({
      contact: updatedContact,
      actions: actionResults
    });
  } catch (error) {
    console.error('Failed to move contact to stage:', error);
    return NextResponse.json(
      { error: 'Failed to move contact to stage: ' + error.message },
      { status: 500 }
    );
  }
}
