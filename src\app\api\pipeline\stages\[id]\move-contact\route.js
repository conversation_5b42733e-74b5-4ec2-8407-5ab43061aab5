import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

// Handle moving a contact to a stage and executing any configured actions
export async function POST(request, context) {
  // Await the params to fix the Next.js warning
  const { params } = context;
  try {
    const body = await request.json();
    const { contactId } = body;

    if (!contactId) {
      return NextResponse.json(
        { error: 'Contact ID is required' },
        { status: 400 }
      );
    }

    // Get the contact
    const contact = await prisma.contact.findUnique({
      where: { id: contactId }
    });

    if (!contact) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }

    // Get the contact's current pipeline stage
    const currentStage = contact.pipelineStage;

    // Get the stage ID and ensure it's properly awaited
    const stageId = params.id;

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Use a transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // 1. Close the current stage history entry if exists
      if (currentStage) {
        const currentStageEntry = await tx.contactStageHistory.findFirst({
          where: {
            contactId,
            stageId: currentStage,
            exitedAt: null
          }
        });

        if (currentStageEntry) {
          const now = new Date();
          const duration = Math.ceil((now - currentStageEntry.enteredAt) / (1000 * 60 * 60 * 24));

          await tx.contactStageHistory.update({
            where: { id: currentStageEntry.id },
            data: {
              exitedAt: now,
              duration
            }
          });
        }
      }

      // 2. Create new stage history entry
      await tx.contactStageHistory.create({
        data: {
          contactId,
          stageId,
          enteredAt: new Date(),
          exitedAt: null,
          duration: null,
          userId: user?.id
        }
      });

      // 3. Update the contact's pipeline stage
      const updatedContact = await tx.contact.update({
        where: { id: contactId },
        data: {
          pipelineStage: stageId,
          updatedAt: new Date()
        }
      });

      // 4. Update or create lifecycle metrics
      await updateContactLifecycleMetrics(tx, contactId);

      return updatedContact;
    });

    // Log the contact stage change
    await auditLogger.logContactStageChange({
      userId: user?.id,
      contactId,
      oldStage: currentStage,
      newStage: stageId,
      request
    });

    // If the contact was previously unassigned (no pipeline stage),
    // log this as a new entry to the pipeline
    if (!currentStage) {
      console.log(`Contact ${contactId} entered the pipeline at stage ${stageId}`);
    }

    // Get any actions configured for this stage
    const stageActions = await prisma.stageAction.findMany({
      where: { stageId: stageId }
    });

    // Execute each action
    const actionResults = await Promise.all(
      stageActions.map(async (action) => {
        try {
          switch (action.actionType) {
            case 'activity':
              // Create an activity
              const activity = await prisma.activity.create({
                data: {
                  type: action.actionDetails.type,
                  description: action.actionDetails.description,
                  contactId: contactId,
                  date: new Date()
                }
              });
              return { success: true, type: 'activity', id: activity.id };

            case 'task':
              // Create a task
              const dueDate = new Date();
              dueDate.setDate(dueDate.getDate() + (action.actionDetails.dueOffset || 1));

              const task = await prisma.task.create({
                data: {
                  title: action.actionDetails.title,
                  description: action.actionDetails.description || '',
                  dueDate: dueDate,
                  priority: action.actionDetails.priority || 'medium',
                  contactId: contactId
                }
              });
              return { success: true, type: 'task', id: task.id };

            case 'note':
              // Create a note
              const note = await prisma.note.create({
                data: {
                  title: action.actionDetails.title,
                  content: action.actionDetails.content,
                  contactId: contactId
                }
              });
              return { success: true, type: 'note', id: note.id };

            case 'email':
              // For now, just log the email that would be sent
              // In a real implementation, this would send an actual email
              console.log(`Would send email: ${action.actionDetails.subject} to contact ${contactId}`);
              return {
                success: true,
                type: 'email',
                details: {
                  subject: action.actionDetails.subject,
                  body: action.actionDetails.body
                }
              };

            default:
              return { success: false, type: action.actionType, error: 'Unknown action type' };
          }
        } catch (error) {
          console.error(`Failed to execute action ${action.id}:`, error);
          return { success: false, type: action.actionType, error: error.message };
        }
      })
    );

    return NextResponse.json({
      contact: result,
      actions: actionResults
    });
  } catch (error) {
    console.error('Failed to move contact to stage:', error);
    return NextResponse.json(
      { error: 'Failed to move contact to stage: ' + error.message },
      { status: 500 }
    );
  }
}

// Helper function to update contact lifecycle metrics
async function updateContactLifecycleMetrics(tx, contactId) {
  // Get contact data with related records
  const contact = await tx.contact.findUnique({
    where: { id: contactId },
    include: {
      activities: {
        orderBy: { createdAt: 'desc' }
      },
      stageHistory: {
        orderBy: { enteredAt: 'asc' }
      }
    }
  });

  if (!contact) return;

  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  // Calculate metrics
  const totalActivities = contact.activities.length;
  const totalStageChanges = contact.stageHistory.length;

  const firstStageEntry = contact.stageHistory[0];
  const daysInPipeline = firstStageEntry
    ? Math.ceil((now - firstStageEntry.enteredAt) / (1000 * 60 * 60 * 24))
    : null;

  const currentStageEntry = contact.stageHistory.find(h => h.exitedAt === null);
  const currentStageDays = currentStageEntry
    ? Math.ceil((now - currentStageEntry.enteredAt) / (1000 * 60 * 60 * 24))
    : null;

  const lastActivity = contact.activities[0];
  const lastActivityDate = lastActivity?.createdAt || null;

  const recentActivities = contact.activities.filter(a => a.createdAt >= thirtyDaysAgo);
  const activityFrequency = recentActivities.length / 4.3; // 30 days ≈ 4.3 weeks

  // Simple engagement score calculation
  const recentActivityScore = Math.min(40, recentActivities.length * 4);
  const totalActivityScore = Math.min(30, totalActivities * 0.5);
  const recencyScore = lastActivityDate
    ? Math.max(0, 30 - Math.ceil((now - lastActivityDate) / (1000 * 60 * 60 * 24)))
    : 0;
  const engagementScore = recentActivityScore + totalActivityScore + recencyScore;

  const isDormant = !lastActivityDate || (now - lastActivityDate) > (30 * 24 * 60 * 60 * 1000);
  const isHighValue = engagementScore > 70 && totalActivities > 10;

  let riskLevel = 'low';
  if (isDormant) {
    riskLevel = 'high';
  } else if (currentStageDays > 60 || engagementScore < 30) {
    riskLevel = 'medium';
  }

  // Simple conversion probability
  let conversionProbability = 0.5;
  if (engagementScore > 70) conversionProbability += 0.3;
  else if (engagementScore < 30) conversionProbability -= 0.3;
  if (currentStageDays > 90) conversionProbability -= 0.2;
  else if (currentStageDays < 30) conversionProbability += 0.1;
  if (totalStageChanges > 3) conversionProbability += 0.1;
  conversionProbability = Math.max(0, Math.min(1, conversionProbability));

  // Update or create lifecycle metrics
  await tx.contactLifecycleMetrics.upsert({
    where: { contactId },
    update: {
      lastActivityDate,
      totalActivities,
      totalStageChanges,
      daysInPipeline,
      currentStageDays,
      engagementScore,
      activityFrequency,
      isDormant,
      isHighValue,
      riskLevel,
      conversionProbability,
      lastCalculated: now,
      updatedAt: now
    },
    create: {
      contactId,
      firstContactDate: contact.createdAt,
      lastActivityDate,
      totalActivities,
      totalStageChanges,
      daysInPipeline,
      currentStageDays,
      engagementScore,
      activityFrequency,
      isDormant,
      isHighValue,
      riskLevel,
      conversionProbability,
      leadSource: contact.leadSource,
      lastCalculated: now,
      createdAt: now,
      updatedAt: now
    }
  });
}
