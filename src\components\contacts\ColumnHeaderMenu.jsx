'use client';

import { useState, useRef, useEffect } from 'react';
import { getFieldById } from '@/lib/constants/contactFields';
import * as Dialog from '@radix-ui/react-dialog';

export default function ColumnHeaderMenu({ fieldId, onHideColumn, onSort, onFilter, activeFilters = {}, contacts = [], pipelineStages = {} }) {
  const [isOpen, setIsOpen] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filterValue, setFilterValue] = useState(activeFilters[fieldId] || '');
  const menuRef = useRef(null);
  const field = getFieldById(fieldId);

  // Check if this column has an active filter
  const hasActiveFilter = activeFilters && activeFilters[fieldId];

  // Safety check - if field is undefined, don't render the menu
  if (!field) {
    console.error(`Field with ID ${fieldId} not found`);
    return null;
  }

  // Get distinct values for this field from contacts
  const getDistinctValues = () => {
    if (!contacts || contacts.length === 0) return [];

    // Special handling for certain fields
    if (fieldId === 'type') {
      // Get distinct contact types
      const types = new Set();
      contacts.forEach(contact => {
        if (contact.type) types.add(contact.type);
      });
      return Array.from(types).sort();
    }

    // Special handling for pipeline stages
    if (fieldId === 'pipelineStage') {
      // Get distinct pipeline stage names
      const stageNames = new Set();
      contacts.forEach(contact => {
        if (contact.pipelineStage) {
          // Use the stage name from the pipelineStages map if available
          const stageName = pipelineStages[contact.pipelineStage] || contact.pipelineStage;
          stageNames.add(stageName);
        }
      });
      // Add 'Not Assigned' for contacts without a pipeline stage
      stageNames.add('Not Assigned');
      return Array.from(stageNames).sort();
    }

    // For other fields, try to get rendered values
    try {
      const values = new Set();
      contacts.forEach(contact => {
        const value = field.render(contact);
        if (value && typeof value === 'string') values.add(value);
      });
      return Array.from(values).sort();
    } catch (error) {
      console.error(`Error getting distinct values for ${fieldId}:`, error);
      return [];
    }
  };

  const distinctValues = getDistinctValues();

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const toggleMenu = (e) => {
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  const handleHideColumn = (e) => {
    e.stopPropagation();
    onHideColumn(fieldId);
    setIsOpen(false);
  };

  const handleSort = (direction) => (e) => {
    e.stopPropagation();
    onSort(field.sortKey, direction);
    setIsOpen(false);
  };

  const handleFilterChange = (e) => {
    setFilterValue(e.target.value);
  };

  const handleFilterApply = (e) => {
    e.stopPropagation();
    if (onFilter) {
      // Only apply filter if there's a value
      if (filterValue.trim()) {
        // For pipeline stages, we need special handling
        if (fieldId === 'pipelineStage') {
          // If the value is 'Not Assigned', use a special identifier
          if (filterValue.trim() === 'Not Assigned') {
            onFilter(fieldId, '_not_assigned_');
          } else {
            // Find the stage ID that corresponds to the selected stage name
            const matchingStage = Object.entries(pipelineStages).find(
              ([id, name]) => name.toLowerCase() === filterValue.trim().toLowerCase()
            );

            if (matchingStage) {
              // If we found a matching stage, use its ID for filtering
              onFilter(fieldId, matchingStage[0]);
            } else {
              // If no exact match, use the original value for partial matching
              onFilter(fieldId, filterValue.trim());
            }
          }
        } else {
          // For other fields, use the filter value as is
          onFilter(fieldId, filterValue.trim());
        }
      } else {
        // If empty, clear the filter
        onFilter(fieldId, '');
      }
    }
    setIsFilterOpen(false);
  };

  const handleFilterClear = (e) => {
    e.stopPropagation();
    setFilterValue('');
    if (onFilter) {
      onFilter(fieldId, '');
    }
    setIsFilterOpen(false);
  };

  const handleFilterClick = (e) => {
    e.stopPropagation();
    // Initialize filter value from active filters if it exists
    if (activeFilters && activeFilters[fieldId]) {
      setFilterValue(activeFilters[fieldId]);
    }
    setIsFilterOpen(true);
    setIsOpen(false);
  };

  return (
    <div className="relative inline-block" ref={menuRef}>
      <button
        onClick={toggleMenu}
        className={`ml-1 focus:outline-none ${hasActiveFilter ? 'text-primary' : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'}`}
        aria-label="Column options"
        title={hasActiveFilter ? `Filtered by: ${activeFilters[fieldId]}` : 'Column options'}
      >
        {hasActiveFilter ? (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </button>

      {isOpen && (
        <div className="absolute z-50 mt-1 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1" role="menu" aria-orientation="vertical">
            <button
              onClick={handleSort('asc')}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              role="menuitem"
            >
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                </svg>
                Sort Ascending
              </span>
            </button>
            <button
              onClick={handleSort('desc')}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              role="menuitem"
            >
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
                </svg>
                Sort Descending
              </span>
            </button>
            <button
              onClick={handleFilterClick}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              role="menuitem"
            >
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                Filter Column
              </span>
            </button>
            <button
              onClick={handleHideColumn}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              role="menuitem"
            >
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Hide Column
              </span>
            </button>
          </div>
        </div>
      )}

      <Dialog.Root open={isFilterOpen} onOpenChange={setIsFilterOpen}>
        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 bg-black/50 z-[100]" />
          <Dialog.Content
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-sm z-[110]"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <Dialog.Title className="text-lg font-semibold text-gray-900 dark:text-white">
                Filter by {field.label}
                {hasActiveFilter && (
                  <span className="ml-2 text-xs text-primary">(Active)</span>
                )}
              </Dialog.Title>
              <button
                onClick={() => setIsFilterOpen(false)}
                className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Filter Value
                </label>
                <input
                  type="text"
                  value={filterValue}
                  onChange={handleFilterChange}
                  placeholder={`Filter ${field.label.toLowerCase()}...`}
                  className="w-full px-3 py-2 text-sm border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  autoFocus
                  list={`${fieldId}-options`}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleFilterApply(e);
                    } else if (e.key === 'Escape') {
                      setIsFilterOpen(false);
                    }
                  }}
                />

                {/* Datalist for distinct values */}
                <datalist id={`${fieldId}-options`}>
                  {distinctValues.map((value, index) => (
                    <option key={index} value={value} />
                  ))}
                </datalist>

                {distinctValues.length > 0 && (
                  <div className="mt-2">
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                      Available Values ({distinctValues.length})
                    </label>
                    <div className="max-h-32 overflow-y-auto mt-1 p-1 border border-gray-200 dark:border-gray-700 rounded-md">
                      <div className="flex flex-wrap gap-1">
                        {distinctValues.map((value, index) => (
                          <button
                            key={index}
                            onClick={() => {
                              setFilterValue(value);
                            }}
                            className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
                          >
                            {value}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 pt-2">
                <button
                  type="button"
                  onClick={handleFilterClear}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Clear
                </button>
                <button
                  type="button"
                  onClick={handleFilterApply}
                  className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover"
                >
                  Apply
                </button>
              </div>
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </div>
  );
}
