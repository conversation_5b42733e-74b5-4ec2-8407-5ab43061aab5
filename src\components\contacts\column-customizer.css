/* Column Customizer Drag and Drop Styles */
:root {
  --color-primary: #3b82f6; /* Blue-500 */
  --color-primary-rgb: 59, 130, 246;
  --color-border: #e5e7eb; /* Gray-200 */
  --color-border-dark: #4b5563; /* Gray-600 */
  --color-bg-hover: #f3f4f6; /* Gray-100 */
  --color-bg-hover-dark: #374151; /* Gray-700 */
  --color-bg-dragging: #eff6ff; /* Blue-50 */
  --color-bg-dragging-dark: rgba(59, 130, 246, 0.1); /* Blue-500 at 10% */
  --color-shadow: rgba(0, 0, 0, 0.1);
  --border-radius: 0.375rem;
  --transition-duration: 150ms;
}

/* Container styles */
.column-list-container {
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) transparent;
}

.column-list-container::-webkit-scrollbar {
  width: 8px;
}

.column-list-container::-webkit-scrollbar-track {
  background: transparent;
}

.column-list-container::-webkit-scrollbar-thumb {
  background-color: var(--color-border);
  border-radius: 4px;
}

.dark .column-list-container {
  border-color: var(--color-border-dark);
  scrollbar-color: var(--color-border-dark) transparent;
}

.dark .column-list-container::-webkit-scrollbar-thumb {
  background-color: var(--color-border-dark);
}

/* Item styles */
.column-list-item {
  position: relative;
  border-bottom: 1px solid var(--color-border);
  background-color: white;
  transition: background-color var(--transition-duration), box-shadow var(--transition-duration), transform var(--transition-duration);
  cursor: default;
}

.dark .column-list-item {
  background-color: #1f2937; /* Gray-800 */
  border-color: var(--color-border-dark);
}

.column-list-item:last-child {
  border-bottom: none;
}

/* Hover state for items */
.column-list-item:hover {
  background-color: #f9fafb; /* Gray-50 */
}

.dark .column-list-item:hover {
  background-color: #374151; /* Gray-700 */
}

/* Active drop target */
.column-list-item.bg-blue-50 {
  background-color: #eff6ff !important; /* Blue-50 */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  z-index: 1;
}

.dark .column-list-item.dark\:bg-blue-900\/10 {
  background-color: rgba(30, 64, 175, 0.1) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  z-index: 1;
}

/* Item content styles */
.column-list-item-content {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  gap: 0.5rem;
}

/* Drag handle styles */
.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: #f3f4f6; /* Gray-100 */
  color: #6b7280; /* Gray-500 */
  cursor: grab;
  transition: all var(--transition-duration);
  margin-right: 8px;
}

.dark .drag-handle {
  background-color: #374151; /* Gray-700 */
  color: #9ca3af; /* Gray-400 */
}

.drag-handle:hover {
  background-color: #e5e7eb; /* Gray-200 */
  color: #4b5563; /* Gray-600 */
  transform: scale(1.1);
}

.dark .drag-handle:hover {
  background-color: #4b5563; /* Gray-600 */
  color: #d1d5db; /* Gray-300 */
}

.drag-handle:active {
  cursor: grabbing;
  transform: scale(0.95);
}

/* Make the drag handle icon look more like a proper drag handle */
.drag-handle svg {
  fill: currentColor;
  stroke-width: 1.5;
}

/* Dragging state */
body.is-dragging-column {
  cursor: grabbing;
}

body.is-dragging-column * {
  cursor: grabbing !important;
}

.column-list-item[data-is-dragging="true"],
.column-list-item-dragging {
  box-shadow: 0 4px 12px var(--color-shadow);
  z-index: 10;
  background-color: var(--color-bg-dragging) !important;
  border: 1px solid var(--color-primary);
  opacity: 0.9;
  transform: translateZ(0);
  position: relative;
}

.dark .column-list-item[data-is-dragging="true"],
.dark .column-list-item-dragging {
  background-color: var(--color-bg-dragging-dark) !important;
  border-color: rgba(59, 130, 246, 0.5);
}

/* Drop target styles */
.column-list-item[data-is-target="true"] {
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.dark .column-list-item[data-is-target="true"] {
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

/* Drop indicator styles */
.drop-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 4px;
  background-color: var(--color-primary);
  z-index: 5;
  box-shadow: 0 0 8px rgba(var(--color-primary-rgb), 0.7);
  border-radius: 4px;
  animation: pulse 1s infinite;
}

.drop-indicator.top {
  top: -2px;
}

.drop-indicator.bottom {
  bottom: -2px;
}

/* Add arrow indicators to make drop position more clear */
.drop-indicator::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  left: 10px;
}

.drop-indicator.top::before {
  top: -4px;
  border-width: 0 4px 4px 4px;
  border-color: transparent transparent var(--color-primary) transparent;
}

.drop-indicator.bottom::before {
  bottom: -4px;
  border-width: 4px 4px 0 4px;
  border-color: var(--color-primary) transparent transparent transparent;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

/* Drag preview styles */
.column-drag-preview {
  padding: 0.5rem 0.75rem;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 8px var(--color-shadow);
  border: 1px solid var(--color-primary);
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.875rem;
  color: #111827; /* Gray-900 */
}

.dark .column-drag-preview {
  background-color: #1f2937; /* Gray-800 */
  color: #f9fafb; /* Gray-50 */
  border-color: rgba(59, 130, 246, 0.5);
}

/* Flash animation for dropped items */
@keyframes flash-animation {
  0% {
    background-color: rgba(var(--color-primary-rgb), 0.4);
    transform: translateX(-4px);
  }
  25% {
    transform: translateX(4px);
  }
  50% {
    background-color: rgba(var(--color-primary-rgb), 0.2);
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
  100% {
    background-color: transparent;
    transform: translateX(0);
  }
}

.flash-animation {
  animation: flash-animation 0.7s ease-out;
  position: relative;
  z-index: 2;
}

/* Drag preview */
.column-drag-preview {
  padding: 8px 12px;
  background-color: var(--color-primary);
  color: white;
  border-radius: 4px;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Empty drop target at the end of the list */
.empty-drop-target {
  margin: 8px 0;
  transition: all 0.2s ease;
}

.empty-drop-target.border-primary {
  border-color: var(--color-primary) !important;
  background-color: rgba(var(--color-primary-rgb), 0.05);
}
