import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';
import { auditLogger } from '@/lib/services/auditLogger';

export async function GET(request, { params }) {
  try {
    const { id } = await params;
    // Get the current user
    const { user } = await (await import('@/lib/auth')).getSignedInUser(request);
    let whereClause = { id: String(id) };
    if (user.role !== 'admin') {
      whereClause.userId = String(user.id);
    }
    // Verify contact exists and is accessible
    const contact = await prisma.contact.findFirst({ where: whereClause });
    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }
    const documents = await prisma.document.findMany({
      where: { contactId: id },
      orderBy: { uploadedAt: 'desc' }
    });

    // Ensure tags field is included in response and map fileSize to size
    const documentsWithTags = documents.map(doc => ({
      ...doc,
      tags: doc.tags || [],
      size: doc.fileSize // Map fileSize to size for consistency
    }));

    return NextResponse.json(documentsWithTags);
  } catch (error) {
    console.error('Failed to fetch documents:', error);
    return NextResponse.json(
      { error: 'Failed to fetch documents' },
      { status: 500 }
    );
  }
}

export async function POST(request, { params }) {
  try {
    const body = await request.json();
    const { id } = await params;
    // Get the current user
    const { user } = await (await import('@/lib/auth')).getSignedInUser(request);
    let whereClause = { id: String(id) };
    if (user.role !== 'admin') {
      whereClause.userId = String(user.id);
    }
    // Verify contact exists and is accessible
    const contact = await prisma.contact.findFirst({ where: whereClause });
    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }
    const document = await prisma.document.create({
      data: {
        ...body,
        tags: body.tags || [],
        contactId: id,
        uploadedAt: new Date()
      }
    });

    // Log the document creation
    await auditLogger.logDocumentCreate({
      userId: user?.id,
      document,
      request
    });

    return NextResponse.json(document);
  } catch (error) {
    console.error('Failed to upload document:', error);
    return NextResponse.json(
      { error: 'Failed to upload document' },
      { status: 500 }
    );
  }
}