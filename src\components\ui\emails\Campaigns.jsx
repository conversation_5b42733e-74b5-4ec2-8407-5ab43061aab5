"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import {
  Trash2,
  Clock,
  Send,
  Users,
  Calendar,
  Pause,
  Play,
  Eye,
  Search,
  X,
  SortAsc,
  SortDesc,
  Ban,
} from "lucide-react";
import Link from "next/link";

export default function Campaigns({ activeTab }) {
  const queryClient = useQueryClient();
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState({
    field: "createdAt",
    direction: "desc",
  });
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");

  const CAMPAIGN_STATUSES = [
    { value: "scheduled", label: "Scheduled" },
    { value: "sending", label: "Sending" },
    { value: "in-progress", label: "In Progress" },
    { value: "paused", label: "Paused" },
    { value: "completed", label: "Completed" },
    { value: "failed", label: "Failed" },
    { value: "cancelled", label: "Cancelled" },
  ];

  const CAMPAIGN_TYPES = [
    { value: "one-time", label: "One-time Campaign" },
    { value: "drip", label: "Drip Campaign" },
  ];

  const {
    data: campaigns = [],
    isLoading,
    error: fetchError,
  } = useQuery({
    queryKey: ["campaigns"],
    queryFn: async () => {
      const response = await fetch("/api/email-campaigns");
      if (!response.ok) throw new Error("Failed to fetch campaigns");
      const data = await response.json();
      return data;
    },
    enabled: activeTab === "campaigns",
    staleTime: 30000,
    cacheTime: 5 * 60 * 1000,
  });

  // Filter and sort campaigns
  const filteredAndSortedCampaigns = campaigns
    .filter((campaign) => {
      const matchesSearch =
        searchTerm.toLowerCase().trim() === "" ||
        campaign.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        campaign.description
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        campaign.tags?.some((tag) =>
          tag.toLowerCase().includes(searchTerm.toLowerCase())
        ) ||
        campaign.status?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        campaign.distributionList?.name
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase());

      const matchesStatus =
        statusFilter === "all" || campaign.status === statusFilter;
      const matchesType =
        typeFilter === "all" || campaign.campaignType === typeFilter;

      return matchesSearch && matchesStatus && matchesType;
    })
    .sort((a, b) => {
      const direction = sortConfig.direction === "asc" ? 1 : -1;

      switch (sortConfig.field) {
        case "name":
          return direction * (a.name?.localeCompare(b.name) || 0);
        case "status":
          return direction * (a.status?.localeCompare(b.status) || 0);
        case "type":
          return (
            direction * (a.campaignType?.localeCompare(b.campaignType) || 0)
          );
        case "createdAt":
          return direction * (new Date(a.createdAt) - new Date(b.createdAt));
        default:
          return 0;
      }
    });

  const handleSort = (field) => {
    setSortConfig((current) => ({
      field,
      direction:
        current.field === field && current.direction === "asc" ? "desc" : "asc",
    }));
  };

  const handleDelete = async (campaignId, e) => {
    e.preventDefault();

    if (
      !confirm(
        "Are you sure you want to delete this campaign? This action cannot be undone."
      )
    ) {
      return;
    }

    setIsDeleting(true);
    setError("");

    try {
      const response = await fetch(`/api/email-campaigns/${campaignId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete campaign");
      }

      queryClient.invalidateQueries(["campaigns"]);
    } catch (error) {
      console.error("Error deleting campaign:", error);
      setError(error.message);
    } finally {
      setIsDeleting(false);
    }
  };

  const handlePauseToggle = async (campaign, currentStatus, e) => {
    e.preventDefault();

    // Can only pause and unpause a campaign if it is scheduled, automated drip and not sending, or paused
    if (
      currentStatus !== "scheduled" &&
      currentStatus !== "paused" &&
      currentStatus !== "sending" &&
      currentStatus !== "in-progress"
    ) {
      return;
    }

    let newStatus = "";

    if (currentStatus === "paused") {
      // Check what the new status should be based on the campaign type
      if (
        campaign.sendType === "scheduled" &&
        new Date(campaign.scheduledAt) > new Date()
      ) {
        newStatus = "scheduled";
      }

      // Check if it's a drip campaign and the first email hasn't gone out yet
      // Need to see if the campaignType is drip and if sendType is scheduled and the  first email of the sequence (scheduledAt) has already passed
      if (
        campaign.campaignType === "drip" &&
        campaign.sendType === "scheduled" &&
        new Date(campaign.scheduledAt) < new Date()
      ) {
        newStatus = "in-progress";
      }
    } else {
      newStatus = "paused";
    }

    // console.log(campaign);

    console.log("the new status: ", newStatus);

    // return;

    try {
      const response = await fetch(
        `/api/email-campaigns/${campaign.id}/status`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ status: newStatus }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update campaign status");
      }

      queryClient.invalidateQueries(["campaigns"]);
    } catch (error) {
      console.error("Error updating campaign status:", error);
      setError(error.message);
    }
  };

  const handleCancel = async (campaign, e) => {
    e.preventDefault();

    // Only allow cancellation of campaigns that are scheduled, in-progress, or sending
    if (!["scheduled", "in-progress", "sending"].includes(campaign.status)) {
      return;
    }

    if (
      !confirm(
        "Are you sure you want to cancel this campaign? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      const response = await fetch(
        `/api/email-campaigns/${campaign.id}/status`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ status: "cancelled" }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to cancel campaign");
      }

      queryClient.invalidateQueries(["campaigns"]);
    } catch (error) {
      console.error("Error cancelling campaign:", error);
      setError(error.message);
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case "completed":
        return "bg-emerald-100 text-emerald-800 dark:bg-emerald-800/30 dark:text-emerald-300 whitespace-nowrap";
      case "sending":
        return "bg-sky-100 text-sky-800 dark:bg-sky-800/30 dark:text-sky-300 whitespace-nowrap";
      case "in-progress":
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-800/30 dark:text-indigo-300 whitespace-nowrap";
      case "failed":
        return "bg-rose-100 text-rose-800 dark:bg-rose-800/30 dark:text-rose-300 whitespace-nowrap";
      case "scheduled":
        return "bg-violet-100 text-violet-800 dark:bg-violet-800/30 dark:text-violet-300 whitespace-nowrap";
      case "paused":
        return "bg-amber-100 text-amber-800 dark:bg-amber-800/30 dark:text-amber-300 whitespace-nowrap";
      case "cancelled":
        return "bg-slate-100 text-slate-800 dark:bg-slate-800/30 dark:text-slate-300 whitespace-nowrap";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-300 whitespace-nowrap";
    }
  };

  const getStatusIconAndLabel = (status) => {
    switch (status) {
      case "scheduled":
        return (
          <>
            <Clock className="h-3 w-3 mr-1" /> Scheduled
          </>
        );
      case "completed":
        return (
          <>
            <Send className="h-3 w-3 mr-1" /> Completed
          </>
        );
      case "paused":
        return (
          <>
            <Pause className="h-3 w-3 mr-1" /> Paused
          </>
        );
      case "sending":
        return (
          <>
            <Play className="h-3 w-3 mr-1" /> Sending
          </>
        );
      case "in-progress":
        return (
          <span className="inline-flex items-center">
            <Eye className="h-3 w-3 mr-1 flex-shrink-0" />
            <span>In Progress</span>
          </span>
        );
      case "cancelled":
        return (
          <>
            <Trash2 className="h-3 w-3 mr-1" /> Cancelled
          </>
        );
      case "failed":
        return (
          <>
            <Trash2 className="h-3 w-3 mr-1" /> Failed
          </>
        );
      default:
        return <></>;
    }
  };

  const formatScheduledTime = (date, time) => {
    console.log(date, time);
    if (!date) return "Not scheduled";
    const dateObj = new Date(`${date.split("T")[0]}T${time || "00:00:00"}`);
    return dateObj.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    });
  };

  if (isLoading) return <div>Loading campaigns...</div>;
  if (fetchError) return <div>Error: {fetchError.message}</div>;

  return (
    <div className="space-y-4">
      {/* Search and Filter Bar */}
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-wrap items-center gap-4">
          {/* Search Input */}
          <div className="relative flex-1 min-w-[250px]">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search campaigns..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-10 py-2 w-full border rounded-lg dark:bg-gray-800 dark:border-gray-700 focus:ring-primary focus:border-primary"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border rounded-lg dark:bg-gray-800 dark:border-gray-700 min-w-[150px]"
          >
            <option value="all">All Statuses</option>
            {CAMPAIGN_STATUSES.map((status) => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>

          {/* Type Filter */}
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-2 border rounded-lg dark:bg-gray-800 dark:border-gray-700 min-w-[150px]"
          >
            <option value="all">All Types</option>
            {CAMPAIGN_TYPES.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Sort Buttons */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Sort by:
          </span>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleSort("name")}
              className={`flex items-center px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md ${
                sortConfig.field === "name"
                  ? "bg-primary text-white"
                  : "bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              Name
              {sortConfig.field === "name" &&
                (sortConfig.direction === "asc" ? (
                  <SortAsc className="h-3 w-3 ml-1" />
                ) : (
                  <SortDesc className="h-3 w-3 ml-1" />
                ))}
            </button>
            <button
              onClick={() => handleSort("status")}
              className={`flex items-center px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md ${
                sortConfig.field === "status"
                  ? "bg-primary text-white"
                  : "bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              Status
              {sortConfig.field === "status" &&
                (sortConfig.direction === "asc" ? (
                  <SortAsc className="h-3 w-3 ml-1" />
                ) : (
                  <SortDesc className="h-3 w-3 ml-1" />
                ))}
            </button>
            <button
              onClick={() => handleSort("type")}
              className={`flex items-center px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md ${
                sortConfig.field === "type"
                  ? "bg-primary text-white"
                  : "bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              Type
              {sortConfig.field === "type" &&
                (sortConfig.direction === "asc" ? (
                  <SortAsc className="h-3 w-3 ml-1" />
                ) : (
                  <SortDesc className="h-3 w-3 ml-1" />
                ))}
            </button>
            <button
              onClick={() => handleSort("createdAt")}
              className={`flex items-center px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md ${
                sortConfig.field === "createdAt"
                  ? "bg-primary text-white"
                  : "bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              Date
              {sortConfig.field === "createdAt" &&
                (sortConfig.direction === "asc" ? (
                  <SortAsc className="h-3 w-3 ml-1" />
                ) : (
                  <SortDesc className="h-3 w-3 ml-1" />
                ))}
            </button>
          </div>
        </div>

        {/* Results count */}
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {filteredAndSortedCampaigns.length} campaign
          {filteredAndSortedCampaigns.length !== 1 ? "s" : ""} found
        </div>
      </div>

      {!filteredAndSortedCampaigns.length && (
        <div className="col-span-full">
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {searchTerm
                ? "No campaigns found matching your search"
                : "No email campaigns yet"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {searchTerm
                ? "Try adjusting your search terms or clear the search"
                : "Get started by creating your first email campaign"}
            </p>
            {!searchTerm && (
              <Link
                href="/emails/compose"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-hover"
              >
                Create Campaign
              </Link>
            )}
          </div>
        </div>
      )}

      {filteredAndSortedCampaigns.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredAndSortedCampaigns.map((campaign) => (
            <div
              key={campaign.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 transition-all duration-200"
            >
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {campaign.name}
                  </h3>
                  {campaign.description && (
                    <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                      {campaign.description}
                    </p>
                  )}
                  <div className="flex flex-wrap gap-2 mt-2">
                    {campaign.tags?.map((tag) => (
                      <span
                        key={tag}
                        className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full text-xs text-gray-600 dark:text-gray-300"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                      campaign.status
                    )}`}
                  >
                    {getStatusIconAndLabel(campaign.status)}
                  </span>
                </div>
              </div>
              <div className="mt-4 flex flex-wrap gap-4 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  {campaign.sendType === "scheduled"
                    ? formatScheduledTime(
                        campaign.scheduledDate,
                        campaign.scheduledTime
                      )
                    : "Immediate"}
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  {campaign.campaignType === "one-time"
                    ? "One-Time Campaign"
                    : "Automated Drip Campaign"}
                </div>
                {campaign.distributionList && (
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {campaign.distributionList.name}
                    <span className="ml-1 text-xs">
                      (
                      {campaign.distributionList.contacts?.length +
                        campaign.distributionList?.groups.reduce(
                          (acc, group) => acc + (group?.contacts?.length || 0),
                          0
                        )}{" "}
                      contacts)
                    </span>
                  </div>
                )}
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 flex justify-end items-center space-x-2">
                {/* Show cancel button only for scheduled, in-progress, or sending campaigns */}
                {["scheduled", "in-progress"].includes(campaign.status) && (
                  <button
                    onClick={(e) => handleCancel(campaign, e)}
                    className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                    title="Cancel campaign"
                  >
                    <Ban className="h-4 w-4" />
                  </button>
                )}

                {/* Existing pause/resume button */}
                {(campaign.status === "scheduled" ||
                  campaign.status === "paused" ||
                  campaign.status === "in-progress") && (
                  <button
                    onClick={(e) =>
                      handlePauseToggle(campaign, campaign.status, e)
                    }
                    className="p-2 text-gray-400 hover:text-yellow-600 dark:hover:text-yellow-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                    title={
                      campaign.status === "paused"
                        ? "Resume campaign"
                        : "Pause campaign"
                    }
                  >
                    {campaign.status === "paused" ? (
                      <Play className="h-4 w-4" />
                    ) : (
                      <Pause className="h-4 w-4" />
                    )}
                  </button>
                )}

                {/* Existing delete button */}
                <button
                  onClick={(e) => handleDelete(campaign.id, e)}
                  className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                  disabled={isDeleting}
                  title="Delete campaign"
                >
                  <Trash2 className="h-4 w-4" />
                </button>

                {/* Existing view details link */}
                <Link
                  href={`/emails/campaigns/${campaign.id}`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
