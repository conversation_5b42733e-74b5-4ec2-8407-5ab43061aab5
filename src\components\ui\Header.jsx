"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import LogoutButton from "../auth/LogoutButton";
import AccountModal from "../auth/AccountModal";
import { useTheme } from "../providers/ThemeProvider";
import { User, Shield, Sun, Moon } from "lucide-react";
import { hasRole } from "@/lib/auth";
import { ROLE_ADMIN } from "@/constants/permissions";
import LoginButton from "@/components/auth/LoginButton";
import { useAuth } from "@/components/providers/AuthContext";

export default function Header() {
  const { theme, toggleTheme } = useTheme();
  const [isAccountModalOpen, setIsAccountModalOpen] = useState(false);
  const { user, isAuthenticated, loading } = useAuth();

  useEffect(() => {
    console.log("THE USER IN HEADER.JSX: ", user);
    console.log("ISAUTHENTICATED IN HEADER.JSX: ", isAuthenticated);
  }, [isAuthenticated, user]);

  return (
    <header className="sticky top-0 z-50 w-full bg-white dark:bg-gray-800 shadow-sm">
      <div className="w-full px-2 sm:px-4 lg:px-6 h-14 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/dashboard" className="mr-8">
            <Image
              src="/brand-assets/great-wealth-investments-logo.png"
              alt="SlimCRM Logo"
              width={120}
              height={32}
            />
          </Link>

          <nav className="hidden md:flex items-center space-x-6">
            {/* Hide the links if the user is not authenticated */}
            {isAuthenticated && (
              <>
                <Link
                  href="/dashboard"
                  className="text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary"
                >
                  Dashboard
                </Link>
                <Link
                  href="/contacts"
                  className="text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary"
                >
                  Contacts
                </Link>
                <Link
                  href="/emails"
                  className="text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary"
                >
                  Emails
                </Link>
                <Link
                  href="/documents"
                  className="text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary"
                >
                  Documents
                </Link>
                <Link
                  href="/pipeline"
                  className="text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary"
                >
                  Pipeline
                </Link>
                <Link
                  href="/workflows"
                  className="text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary"
                >
                  Workflows
                </Link>
              </>
            )}
          </nav>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={toggleTheme}
            className="p-2 text-gray-500 hover:text-primary dark:text-gray-400 dark:hover:text-primary"
            aria-label="Toggle theme"
            type="button"
          >
            {theme === "dark" ? (
              <Sun className="h-5 w-5" strokeWidth={1.8} />
            ) : (
              <Moon className="h-5 w-5" strokeWidth={1.8} />
            )}
          </button>
          {isAuthenticated ? (
            <>
              {/* <Link
                href="/admin"
                className="flex items-center space-x-2 p-2 text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary"
                aria-label="Admin Panel"
              >
                <Shield className="h-5 w-5" />
                <span className="hidden sm:block text-sm">Admin</span>
              </Link> */}
              {/* Uncomment this when login is implemented */}
              {user && hasRole(user, ROLE_ADMIN) && (
                <Link
                  href="/admin"
                  className="flex items-center space-x-2 p-2 text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary"
                  aria-label="Admin Panel"
                >
                  <Shield className="h-5 w-5" />
                  <span className="hidden sm:block text-sm">Admin</span>
                </Link>
              )}

              <button
                onClick={() => setIsAccountModalOpen(true)}
                className="flex items-center space-x-2 p-2 text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary"
                aria-label="Account information"
              >
                <User className="h-5 w-5" />
                <span className="hidden sm:block text-sm">
                  {user?.name.split(" ")[0] || "Account"}
                </span>
              </button>
              <LogoutButton />
              <AccountModal
                isOpen={isAccountModalOpen}
                onClose={() => setIsAccountModalOpen(false)}
                user={user}
              />
            </>
          ) : (
            <LoginButton />
          )}
        </div>
      </div>
    </header>
  );
}
