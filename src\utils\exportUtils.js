import <PERSON> from 'papaparse';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { CONTACT_FIELDS, getFieldById } from '@/lib/constants/contactFields';

/**
 * Export contacts to CSV file
 * @param {Array} contacts - Array of contact objects
 * @param {String} filename - Name of the file to download
 * @param {Array} selectedFieldIds - Array of field IDs to export
 */
export const exportToCSV = (contacts, filename = 'contacts.csv', selectedFieldIds = []) => {
  // Get the field definitions for the selected fields
  const selectedFields = selectedFieldIds.length > 0
    ? selectedFieldIds.map(id => getFieldById(id)).filter(Boolean)
    : CONTACT_FIELDS.filter(field => field.isDefault);

  // Map contacts to only include the fields we want
  const data = contacts.map(contact => {
    const mappedContact = {};
    selectedFields.forEach(field => {
      // Use the field's render function to get the display value
      const value = field.render(contact);
      mappedContact[field.label] = value !== undefined ? value : '';
    });
    return mappedContact;
  });

  // Convert to CSV
  const csv = Papa.unparse(data, {
    header: true,
    skipEmptyLines: true
  });

  // Create a blob and download
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Export contacts to PDF file
 * @param {Array} contacts - Array of contact objects
 * @param {String} filename - Name of the file to download
 * @param {Array} selectedFieldIds - Array of field IDs to export
 */
export const exportToPDF = (contacts, filename = 'contacts.pdf', selectedFieldIds = []) => {
  // Create a new PDF document in landscape orientation
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // Add metadata to the PDF
  doc.setProperties({
    title: 'Contacts Export',
    subject: 'Contact List Export',
    author: 'SlimCRM',
    keywords: 'contacts, export, crm',
    creator: 'SlimCRM'
  });

  // Add title
  doc.setFontSize(20);
  doc.setTextColor(41, 128, 185); // Match header color
  doc.text('Contacts List', 14, 20);

  // Add date and count
  doc.setFontSize(11);
  doc.setTextColor(100);
  doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 28);
  doc.text(`Total Contacts: ${contacts.length}`, 14, 34);

  // Add selected fields info
  const fieldNames = selectedFieldIds.length > 0
    ? selectedFieldIds.map(id => {
        const field = getFieldById(id);
        return field ? field.label : id;
      }).join(', ')
    : 'Default fields';

  doc.text(`Fields: ${fieldNames}`, 14, 40);

  // Get the field definitions for the selected fields
  const selectedFields = selectedFieldIds.length > 0
    ? selectedFieldIds.map(id => getFieldById(id)).filter(Boolean)
    : CONTACT_FIELDS.filter(field => field.isDefault);

  // Define the columns for the table
  const columns = selectedFields.map(field => ({
    header: field.label,
    dataKey: field.id
  }));

  // Helper function to truncate and format text based on column type
  const formatText = (text, fieldId) => {
    if (text === undefined || text === null) return '';

    // Convert to string if it's not already
    const str = typeof text === 'string' ? text : String(text);

    // Different truncation lengths based on field type
    const truncationLengths = {
      notes: 150,       // Notes can be longer
      description: 150, // Descriptions can be longer
      email: 80,        // Emails can be medium length
      company: 50,      // Company names can be medium length
      firstName: 30,    // Names should be shorter
      lastName: 30,     // Names should be shorter
      phone: 20,        // Phone numbers are usually fixed length
      type: 20,         // Type is usually short
      pipelineStage: 30 // Pipeline stage names can vary
    };

    // Get appropriate max length or use default
    const maxLength = truncationLengths[fieldId] || 50;

    // Truncate if needed
    if (str.length > maxLength) {
      return str.substring(0, maxLength) + '...';
    }

    return str;
  };

  // Prepare the data for the table
  const rows = contacts.map(contact => {
    const row = {};
    selectedFields.forEach(field => {
      // Use the field's render function to get the display value
      let value = field.render(contact);

      // Format the value based on field type
      value = formatText(value, field.id);

      row[field.id] = value;
    });
    return row;
  });

  // Helper function to optimize table settings based on number of columns
  const getOptimizedTableSettings = () => {
    const columnCount = columns.length;

    // Base settings
    const settings = {
      startY: 46, // Start below our metadata
      head: [columns.map(col => col.header)],
      body: rows.map(row => columns.map(col => row[col.dataKey] || '')),
      theme: 'grid', // Use grid theme for better readability
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold',
        halign: 'center', // Center-align headers
        valign: 'middle',
        lineWidth: 0.5,
        lineColor: [220, 220, 220]
      },
      bodyStyles: {
        valign: 'top',
        lineWidth: 0.5,
        lineColor: [220, 220, 220]
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245]
      },
      styles: {
        overflow: 'linebreak',
        cellPadding: 2, // Reduce padding to fit more content
        fontSize: 8, // Slightly smaller font to fit more content
        font: 'helvetica', // Use a clean font
        cellWidth: 'wrap', // Wrap text in cells
        halign: 'left', // Left-align text for better readability
        valign: 'middle', // Vertically center content
        minCellHeight: 10, // Ensure minimum height for cells
        lineColor: [200, 200, 200], // Lighter grid lines
        lineWidth: 0.1 // Thinner grid lines
      }
    };

    // Adjust settings based on number of columns
    if (columnCount > 8) {
      // For many columns, reduce font size and padding further
      settings.styles.fontSize = 7;
      settings.styles.cellPadding = 1;
      settings.headStyles.fontSize = 7;
    } else if (columnCount <= 4) {
      // For few columns, we can afford larger text and padding
      settings.styles.fontSize = 9;
      settings.styles.cellPadding = 3;
    }

    return settings;
  };

  // Get optimized table settings
  const tableSettings = getOptimizedTableSettings();

  // Add dynamic column styles to the settings
  tableSettings.columnStyles = (() => {
    const totalColumns = columns.length;
    const pageWidth = doc.internal.pageSize.width - 20; // Subtract margins
    const styles = {};

    // Define column width priorities (smaller number = higher priority for space)
    const columnPriorities = {
      email: 3,        // Email needs more space
      notes: 3,        // Notes need more space
      description: 3,  // Descriptions need more space
      firstName: 2,    // Names are important but can be narrower
      lastName: 2,
      company: 2,      // Company names can be medium width
      phone: 1,        // Phone numbers can be narrow
      type: 1,         // Type is usually short
      pipelineStage: 2, // Pipeline stage names can vary in length
      createdAt: 1,    // Dates can be narrow
      updatedAt: 1
    };

    // Calculate average content length for each column
    const contentLengths = {};
    columns.forEach(col => {
      const values = rows.map(row => String(row[col.dataKey] || ''));
      const avgLength = values.reduce((sum, val) => sum + val.length, 0) / (values.length || 1);
      contentLengths[col.dataKey] = Math.max(avgLength, col.header.length);
    });

    // Calculate initial proportional widths based on content length and priority
    const totalContentLength = Object.values(contentLengths).reduce((sum, len) => sum + len, 0);
    const initialWidths = {};

    columns.forEach(col => {
      const priority = columnPriorities[col.dataKey] || 2; // Default priority
      const contentRatio = contentLengths[col.dataKey] / totalContentLength;
      initialWidths[col.dataKey] = contentRatio * pageWidth * (priority / 2);
    });

    // Normalize widths to fit page width
    const totalInitialWidth = Object.values(initialWidths).reduce((sum, width) => sum + width, 0);
    const scaleFactor = pageWidth / totalInitialWidth;

    // Apply calculated widths with minimums based on column type
    columns.forEach((col, index) => {
      let width = initialWidths[col.dataKey] * scaleFactor;

      // Apply minimum widths based on column type
      if (col.dataKey === 'email' || col.dataKey === 'notes' || col.dataKey === 'description') {
        width = Math.max(width, 25); // Minimum width for text-heavy columns
      } else if (col.dataKey === 'firstName' || col.dataKey === 'lastName' || col.dataKey === 'company') {
        width = Math.max(width, 20); // Minimum width for name columns
      } else if (col.dataKey === 'phone' || col.dataKey === 'type' ||
                col.dataKey === 'createdAt' || col.dataKey === 'updatedAt') {
        width = Math.max(width, 15); // Minimum width for date/short text columns
      }

      styles[index] = { cellWidth: width };
    });

    return styles;
  })();

  // Add margins and page settings
  tableSettings.margin = { top: 10, right: 10, bottom: 10, left: 10 };
  tableSettings.showHead = 'everyPage';
  tableSettings.showFoot = 'everyPage';

  // Add page numbers
  tableSettings.didDrawPage = function(data) {
    // Footer with page numbers
    const pageCount = doc.internal.getNumberOfPages();
    const str = `Page ${doc.internal.getCurrentPageInfo().pageNumber} of ${pageCount}`;
    doc.setFontSize(8);
    doc.text(str, doc.internal.pageSize.width / 2, doc.internal.pageSize.height - 10, { align: 'center' });
  };

  // Add the table to the PDF using the imported autoTable function
  autoTable(doc, tableSettings);

  // Save the PDF
  doc.save(filename);
};
