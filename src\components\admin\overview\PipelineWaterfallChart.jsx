"use client";

import { useState } from "react";
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Cell
} from "recharts";
import { TrendingDown, TrendingUp, Eye } from "lucide-react";

export default function PipelineWaterfallChart({ data, detailed = false }) {
  const [selectedStage, setSelectedStage] = useState(null);

  // Define getStageColor function before using it
  const getStageColor = (index, total) => {
    const colors = [
      '#3b82f6', // blue
      '#10b981', // green
      '#f59e0b', // amber
      '#ef4444', // red
      '#8b5cf6', // purple
      '#06b6d4', // cyan
      '#84cc16', // lime
      '#f97316'  // orange
    ];
    return colors[index % colors.length];
  };

  if (!data || data.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Pipeline Waterfall
        </h3>
        <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
          No pipeline data available
        </div>
      </div>
    );
  }

  // Prepare data for the chart
  const chartData = data.map((stage, index) => ({
    ...stage,
    index,
    color: getStageColor(index, data.length),
    efficiency: stage.entered > 0 ? ((stage.converted / stage.entered) * 100) : 0
  }));

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
            {data.stage}
          </h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Entered:</span>
              <span className="font-medium text-gray-900 dark:text-white">{data.entered}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Current:</span>
              <span className="font-medium text-gray-900 dark:text-white">{data.current}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Converted:</span>
              <span className="font-medium text-green-600 dark:text-green-400">{data.converted}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Dropped:</span>
              <span className="font-medium text-red-600 dark:text-red-400">{data.dropped}</span>
            </div>
            <div className="flex justify-between border-t border-gray-200 dark:border-gray-700 pt-1 mt-2">
              <span className="text-gray-600 dark:text-gray-400">Conversion Rate:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {data.conversionRate.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const handleStageClick = (stage) => {
    setSelectedStage(selectedStage?.stageId === stage.stageId ? null : stage);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Pipeline Waterfall
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Contact flow through pipeline stages
          </p>
        </div>
        {detailed && (
          <button className="flex items-center space-x-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm">
            <Eye className="h-4 w-4" />
            <span>View Details</span>
          </button>
        )}
      </div>

      {/* Chart */}
      <div className="h-80 mb-6">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
            <XAxis 
              dataKey="stage" 
              stroke="#6b7280"
              fontSize={12}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis stroke="#6b7280" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey="entered" 
              name="Entered"
              radius={[4, 4, 0, 0]}
              cursor="pointer"
              onClick={handleStageClick}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Stage Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Total Pipeline Flow */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Total Flow
            </h4>
            <TrendingUp className="h-4 w-4 text-blue-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {data.reduce((sum, stage) => sum + stage.entered, 0)}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Total entries across all stages
          </p>
        </div>

        {/* Average Conversion */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Avg Conversion
            </h4>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {(data.reduce((sum, stage) => sum + stage.conversionRate, 0) / data.length).toFixed(1)}%
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Average stage-to-stage conversion
          </p>
        </div>

        {/* Total Drop-offs */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Total Drop-offs
            </h4>
            <TrendingDown className="h-4 w-4 text-red-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {data.reduce((sum, stage) => sum + stage.dropped, 0)}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Contacts that left the pipeline
          </p>
        </div>
      </div>

      {/* Selected Stage Details */}
      {selectedStage && (
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">
            {selectedStage.stage} - Detailed Metrics
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-blue-700 dark:text-blue-300 font-medium">Entered:</span>
              <div className="text-lg font-bold text-blue-900 dark:text-blue-100">
                {selectedStage.entered}
              </div>
            </div>
            <div>
              <span className="text-blue-700 dark:text-blue-300 font-medium">Current:</span>
              <div className="text-lg font-bold text-blue-900 dark:text-blue-100">
                {selectedStage.current}
              </div>
            </div>
            <div>
              <span className="text-blue-700 dark:text-blue-300 font-medium">Conversion Rate:</span>
              <div className="text-lg font-bold text-blue-900 dark:text-blue-100">
                {selectedStage.conversionRate.toFixed(1)}%
              </div>
            </div>
            <div>
              <span className="text-blue-700 dark:text-blue-300 font-medium">Drop-off Rate:</span>
              <div className="text-lg font-bold text-blue-900 dark:text-blue-100">
                {selectedStage.dropOffRate.toFixed(1)}%
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
