'use client';

import NotesTab from './NotesTab';
import { useStatsRefresh } from '@/hooks/useStatsRefresh';

export default function NotesTabWithRefresh({ contactId }) {
  const { triggerRefresh } = useStatsRefresh();
  
  // Function to handle note changes
  const handleNoteChange = () => {
    // Trigger a refresh of the stats
    triggerRefresh(['notes']);
  };
  
  return <NotesTab contactId={contactId} onNoteChange={handleNoteChange} />;
}
