"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import ContactSelector from "@/components/ui/emails/ContactSelector";
import GroupsSelector from "@/components/ui/emails/GroupsSelector";

export default function EditList({ params }) {
  const router = useRouter();
  const { id: listId } = useParams(params);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    contactIds: [],
    groups: [],
    id: "",
  });
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    async function fetchList() {
      try {
        const response = await fetch(`/api/distribution-lists/${listId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch distribution list");
        }
        const list = await response.json();
        setFormData({
          name: list.name,
          description: list.description || "",
          contactIds: list.contacts.map((contact) => contact.id),
          id: list.id,
          groups: list.groups,
        });
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    }

    fetchList();
  }, [listId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");

    try {
      const response = await fetch(`/api/distribution-lists/${listId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const data = await response.json();
        if (data.status === 409) {
          // scroll page to top
          window.scrollTo(0, 0);
          setError(
            "Error updating distribution list. Please delete any active campaigns using this list and try again."
          );
        }
        throw new Error(data.error || "Failed to update list");
        return;
      }

      router.push("/emails?tab=lists");
    } catch (err) {
      // setError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto py-8 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      <div className="flex items-center mb-6">
        <Link
          href="/emails?tab=lists"
          className="mr-4 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
        >
          <span>← Back to Emails</span>
        </Link>
        <h1 className="text-2xl font-semibold">Edit Distribution List</h1>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-red-600 dark:text-red-400 text-sm">
                {error}
              </div>
            </div>
            <button
              onClick={() => setError("")}
              className="text-red-400 hover:text-red-600 dark:text-red-300 dark:hover:text-red-100"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* {error && (
          <div className="bg-red-50 dark:bg-red-900/50 text-red-600 dark:text-red-200 p-3 rounded-md">
            {error}
          </div>
        )} */}

        <div>
          <label htmlFor="name" className="block text-sm font-medium mb-2">
            List Name
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
            required
          />
        </div>

        <div>
          <label
            htmlFor="description"
            className="block text-sm font-medium mb-2"
          >
            Description
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
            rows={3}
          />
        </div>

        <div className="overflow-y-auto">
          <label className="block text-sm font-medium mb-2">
            Select Groups
          </label>
          <GroupsSelector
            selectedGroups={formData.groups}
            selectedContactIds={formData.contactIds}
            onChange={(groups) => setFormData({ ...formData, groups: groups })}
          />
        </div>

        <div className="overflow-y-auto">
          <label className="block text-sm font-medium mb-2">
            Select Contacts
          </label>
          <ContactSelector
            selectedIds={formData.contactIds}
            selectedGroups={formData.groups}
            onChange={(ids) => setFormData({ ...formData, contactIds: ids })}
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Link
            href="/emails?tab=lists"
            className="px-4 py-2 border rounded-md hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover disabled:opacity-50"
          >
            {isSubmitting ? "Saving..." : "Save Changes"}
          </button>
        </div>
      </form>
    </div>
  );
}
