"use client";
import { useState, useEffect } from "react";
import NavigationTabs from "./NavigationTabs";
import DistributionLists from "./DistributionLists";
import Templates from "./Templates";
import Campaigns from "./Campaigns";
import Analytics from "./Analytics";

export default function EmailContent() {
  // Initialize state with value from localStorage or default to "analytics"
  const [activeTab, setActiveTab] = useState(() => {
    // Only access localStorage after component mounts (client-side)
    if (typeof window !== "undefined") {
      return localStorage.getItem("emailActiveTab") || "analytics";
    }
    return "analytics";
  });

  // Update localStorage when activeTab changes
  useEffect(() => {
    localStorage.setItem("emailActiveTab", activeTab);
  }, [activeTab]);

  return (
    <>
      <NavigationTabs activeTab={activeTab} setActiveTab={setActiveTab} />
      {activeTab === "analytics" && <Analytics />}
      {activeTab === "lists" && <DistributionLists activeTab={activeTab} />}
      {activeTab === "templates" && <Templates activeTab={activeTab} />}
      {activeTab === "campaigns" && <Campaigns activeTab={activeTab} />}
    </>
  );
}
