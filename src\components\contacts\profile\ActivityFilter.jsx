"use client";

import { useState, useEffect, useRef } from "react";
import { ACTIVITY_TYPES } from "@/lib/constants/activityTypes";
import * as LucideIcons from "lucide-react";
import {
  ArrowDownAZ,
  ArrowUpZA,
  Clock,
  RotateCcw,
  X,
  Funnel,
  ChevronDown,
} from "lucide-react";

export default function ActivityFilter({ onFilterChange }) {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState({
    types: [], // Changed to array for multiselect
    sortBy: "date",
    sortOrder: "desc",
  });
  const [activityTypes, setActivityTypes] = useState([]);
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);

  const filterRef = useRef(null);
  const typeDropdownRef = useRef(null);

  // Fetch activity types from API
  useEffect(() => {
    const fetchActivityTypes = async () => {
      try {
        const response = await fetch('/api/activity-types');
        if (response.ok) {
          const types = await response.json();
          setActivityTypes(types);
        } else {
          // Fallback to hardcoded types
          setActivityTypes(ACTIVITY_TYPES);
        }
      } catch (error) {
        console.error('Error fetching activity types:', error);
        // Fallback to hardcoded types
        setActivityTypes(ACTIVITY_TYPES);
      }
    };

    fetchActivityTypes();
  }, []);

  // Handle click outside to close the filter popup
  useEffect(() => {
    function handleClickOutside(event) {
      if (filterRef.current && !filterRef.current.contains(event.target)) {
        setIsOpen(false);
      }
      if (typeDropdownRef.current && !typeDropdownRef.current.contains(event.target)) {
        setIsTypeDropdownOpen(false);
      }
    }

    // Add event listener when the popup is open or type dropdown is open
    if (isOpen || isTypeDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // Clean up the event listener
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, isTypeDropdownOpen]);

  const handleFilterChange = (key, value) => {
    const newFilters = {
      ...filters,
      [key]: value,
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleReset = () => {
    const defaultFilters = {
      types: [], // Reset to empty array
      sortBy: "date",
      sortOrder: "desc",
    };
    setFilters(defaultFilters);
    onFilterChange(defaultFilters);
  };

  return (
    <div className="relative" ref={filterRef}>
      <div className="flex items-center space-x-2">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 flex items-center"
        >
          <Funnel className="h-4 w-4 mr-1.5" />
          Filter
          {filters.types.length > 0 && (
            <span className="ml-1 text-xs bg-primary text-white rounded-full px-1.5">
              {filters.types.length}
            </span>
          )}
        </button>

        <select
          value={`${filters.sortBy}-${filters.sortOrder}`}
          onChange={(e) => {
            const [sortBy, sortOrder] = e.target.value.split("-");
            const newFilters = {
              ...filters,
              sortBy,
              sortOrder
            };
            setFilters(newFilters);
            onFilterChange(newFilters);
          }}
          className="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-700 dark:text-white"
        >
          <option value="date-desc">Newest First</option>
          <option value="date-asc">Oldest First</option>
          <option value="title-asc">Title (A-Z)</option>
          <option value="title-desc">Title (Z-A)</option>
        </select>
      </div>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-10">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Activity Type
              </label>
              <div className="relative" ref={typeDropdownRef}>
                {/* Custom dropdown button */}
                <button
                  type="button"
                  onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 pl-3 pr-10 py-2 text-sm text-gray-900 dark:text-white text-left focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <div className="flex items-center">
                    {(() => {
                      if (filters.types.length === 0) {
                        return <span className="text-gray-500">All Types</span>;
                      }

                      if (filters.types.length === 1) {
                        // Show single selected type with icon
                        const typeId = filters.types[0];
                        let selectedType = activityTypes.find(t => t.id === typeId);

                        if (!selectedType) {
                          selectedType = activityTypes.find(t => t.name.toLowerCase() === typeId?.toLowerCase());
                        }

                        if (selectedType) {
                          const IconComponent = selectedType.icon && LucideIcons[selectedType.icon];
                          return (
                            <div className="flex items-center">
                              <div
                                className="w-4 h-4 rounded-full flex items-center justify-center mr-2"
                                style={{ backgroundColor: selectedType.color }}
                              >
                                {IconComponent ? (
                                  <IconComponent className="h-2.5 w-2.5 text-white" />
                                ) : (
                                  <span className="text-xs text-white font-bold">
                                    {selectedType.name.charAt(0)}
                                  </span>
                                )}
                              </div>
                              <span>{selectedType.name}</span>
                            </div>
                          );
                        }
                      }

                      // Multiple types selected
                      return <span className="text-gray-900 dark:text-white">{filters.types.length} types selected</span>;
                    })()}
                  </div>
                </button>

                {/* Dropdown arrow */}
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                </div>

                {/* Custom dropdown menu */}
                {isTypeDropdownOpen && (
                  <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-700 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                    {/* Clear All option */}
                    <div
                      onClick={() => {
                        handleFilterChange("types", []);
                        setIsTypeDropdownOpen(false);
                      }}
                      className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100 dark:hover:bg-gray-600 border-b border-gray-200 dark:border-gray-600"
                    >
                      <div className="flex items-center">
                        <span className="font-medium block truncate text-gray-900 dark:text-white">Clear All</span>
                      </div>
                    </div>

                    {/* Activity type options */}
                    {activityTypes.map((type) => {
                      const IconComponent = type.icon && LucideIcons[type.icon];
                      const isSelected = filters.types.includes(type.id) ||
                        filters.types.some(selectedId => {
                          const selectedType = activityTypes.find(t => t.id === selectedId);
                          return selectedType && selectedType.name.toLowerCase() === type.name.toLowerCase();
                        });

                      return (
                        <div
                          key={type.id}
                          onClick={(e) => {
                            e.stopPropagation();
                            const newTypes = isSelected
                              ? filters.types.filter(id => id !== type.id)
                              : [...filters.types, type.id];
                            handleFilterChange("types", newTypes);
                            // Don't close dropdown for multiselect
                          }}
                          className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100 dark:hover:bg-gray-600"
                        >
                          <div className="flex items-center">
                            {/* Checkbox */}
                            <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${
                              isSelected
                                ? 'bg-primary border-primary'
                                : 'border-gray-300 dark:border-gray-600'
                            }`}>
                              {isSelected && (
                                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              )}
                            </div>

                            {/* Icon and name */}
                            <div
                              className="w-4 h-4 rounded-full flex items-center justify-center mr-3"
                              style={{ backgroundColor: type.color }}
                            >
                              {IconComponent ? (
                                <IconComponent className="h-2.5 w-2.5 text-white" />
                              ) : (
                                <span className="text-xs text-white font-bold">
                                  {type.name.charAt(0)}
                                </span>
                              )}
                            </div>
                            <span className="font-normal block truncate text-gray-900 dark:text-white">
                              {type.name}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-between pt-2">
              <button
                onClick={handleReset}
                className="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 flex items-center"
              >
                <RotateCcw className="h-3.5 w-3.5 mr-1" />
                Reset
              </button>
              <button
                onClick={() => setIsOpen(false)}
                className="text-sm text-primary hover:text-primary-hover flex items-center"
              >
                <X className="h-3.5 w-3.5 mr-1" />
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
