import { NextResponse } from "next/server";
// import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";

export async function POST(request) {
  try {
    const { user } = await getSignedInUser(request);

    console.log("THE USER: ", user);

    const data = await request.json();
    console.log("The campaign data in the API: ", data);
    // return NextResponse.json(data);
    // Ensure template exists
    if (!data.template || !data.template.id) {
      return NextResponse.json(
        { error: "Email template is required" },
        { status: 400 }
      );
    }
    const templateId = data.template.id;
    const template = await prisma.emailTemplate.findUnique({
      where: { id: templateId },
    });
    if (!template) {
      return NextResponse.json(
        { error: "Email template not found" },
        { status: 404 }
      );
    } else {
      console.log("Found required template...");
    }
    // Ensure distribution list exists
    if (!data.distributionList || !data.distributionList.id) {
      return NextResponse.json(
        { error: "Distribution list is required" },
        { status: 400 }
      );
    }
    const listId = data.distributionList.id;
    const list = await prisma.distributionList.findUnique({
      where: { id: listId },
    });
    if (!list) {
      return NextResponse.json(
        { error: "Distribution list not found" },
        { status: 404 }
      );
    } else {
      console.log("Found required distribution list...");
    }
    const [campaign, sequence, steps] = await prisma.$transaction(
      async (tx) => {
        // Create Campaign without sequence reference
        // Create EmailSequence and add the campaign reference
        // Create EmailSequenceSteps and add the sequence reference

        // If campaign is immediate, there won't be:
        // scheduledDate
        // scheduledTime
        // scheduledAt

        // If campaign is one-time:
        // data.sequence.length === 0
        // data.campaignType === "one-time"

        let campaignStatus = "";

        // Scheduled, Sending, Paused, Completed, Failed, Cancelled

        // Paused: User needs to manually click pause on the front-end for this status if the campaign is scheduled

        // In Progress: Only if an automted drip campaign is in progress

        // Completed: Only when the script that is sending emails has completed and it will update the campaign record accordingly

        // Failed: If there is an error sending the email, it will update the campaign record accordingly

        // Cancelled: If the user cancels the campaign, it will update the campaign record accordingly
        if (data.sendType === "scheduled") {
          campaignStatus = "scheduled";
        } else if (data.sendType === "immediate") {
          campaignStatus = "sending";
        }

        const campaign = await tx.emailCampaign.create({
          data: {
            name: data.name,
            description: data.description,
            tags: data.tags,
            campaignType: data.campaignType, // one-time or drip
            status: campaignStatus,
            sendType: data.sendType, // immediate or scheduled
            templateId: templateId,
            distributionListId: listId,
            userId: user.id,
            // If scheduled
            // Only include scheduling fields if sendType is "scheduled"
            ...(data.sendType === "scheduled" && {
              scheduledAt: new Date(
                `${data.scheduledDate}T${data.scheduledTime}`
              ),
              scheduledDate: new Date(data.scheduledDate),
              scheduledTime: data.scheduledTime,
            }),
            // No reference to sequence yet
          },
        });

        if (data.campaignType === "drip") {
          const sequence = await tx.emailSequence.create({
            data: {
              name: `${data.name} Sequence - ${data.sequence.length} Steps`,
              userId: user.id,
              status: "ACTIVE",
              campaign: {
                connect: {
                  id: campaign.id,
                },
              },
            },
          });

          // 3. Create all steps
          const steps = await Promise.all(
            data.sequence.map((stepData) =>
              tx.emailSequenceStep.create({
                data: {
                  sequenceId: sequence.id,
                  order: stepData.order,
                  delayDays: stepData.delayDays,
                  delayHours: stepData.delayHours,
                  templateId: stepData.templateId,
                  condition: stepData.condition,
                  error: "test error",
                  // Add scheduled date if available
                  ...(stepData.scheduledDate &&
                    stepData.scheduledTime && {
                      scheduledDate: new Date(
                        `${stepData.scheduledDate}T${stepData.scheduledTime}:00`
                      ),
                      scheduledTime: stepData.scheduledTime,
                    }),
                  completed: false,
                },
              })
            )
          );

          // Update the campaign to include the sequence
          const updatedCampaign = await tx.emailCampaign.update({
            where: { id: campaign.id },
            data: {
              sequence: {
                connect: {
                  id: sequence.id,
                },
              },
            },
            include: {
              sequence: true,
            },
          });

          return [updatedCampaign, sequence, steps];
        }

        return [campaign, null, null];
      }
    );

    console.log("Created campaign:", campaign);
    console.log("Campaign sequence:", campaign.sequence);
    console.log("Created sequence:", sequence);
    console.log("Created steps:", steps);

    // After the transaction, fetch the complete campaign with all relations
    const completeCampaign = await prisma.emailCampaign.findUnique({
      where: { id: campaign.id },
      include: {
        template: true,
        distributionList: true,
        sequence: {
          include: {
            steps: true,
          },
        },
      },
    });

    console.log("Complete campaign with relations:", completeCampaign);

    return NextResponse.json({
      message: "Campaign created successfully",
      campaign: completeCampaign,
      ...(data.campaignType === "drip" && { sequence, steps }),
    });
  } catch (error) {
    console.error("Failed to create campaign:", error);
    return NextResponse.json(
      { error: "Failed to create campaign" },
      { status: 500 }
    );
  }
}

export async function GET(request) {
  try {
    const { user } = await getSignedInUser(request);

    const campaigns = await prisma.emailCampaign.findMany({
      where: {
        userId: user.id,
      },
      include: {
        template: true,
        distributionList: {
          include: {
            contacts: true,
            groups: {
              include: {
                contacts: {
                  include: {
                    contact: true,
                  },
                },
              },
            },
          },
        },
        sequence: {
          include: {
            steps: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(campaigns);
  } catch (error) {
    console.error("Failed to fetch campaigns:", error);
    return NextResponse.json(
      { error: "Failed to fetch campaigns" },
      { status: 500 }
    );
  }
}
