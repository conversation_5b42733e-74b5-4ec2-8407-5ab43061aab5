'use client';

import { useState, useEffect } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth,
         isSameDay, addMonths, subMonths, parseISO, isWithinInterval } from 'date-fns';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import * as LucideIcons from 'lucide-react';
import ActivityForm from './ActivityForm';
import TaskForm from './TaskForm';
import NoteForm from './NoteForm';
import Tooltip from '@/components/common/Tooltip';

export default function CalendarTab({ contactId }) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [activities, setActivities] = useState([]);
  const [tasklists, setTasklists] = useState([]);
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stickyTooltips, setStickyTooltips] = useState(new Set());
  const [tooltipPositions, setTooltipPositions] = useState({});
  const [activityTypes, setActivityTypes] = useState([]);

  // Function to get icon for activity type using admin-configured icons
  const getActivityIcon = (type) => {
    const iconProps = { className: "h-3 w-3 text-white" };

    // First try exact ID match
    let activityType = activityTypes.find(at => at.id === type);

    // If not found by ID, try matching by name (case-insensitive)
    if (!activityType) {
      activityType = activityTypes.find(at => at.name.toLowerCase() === type?.toLowerCase());
    }

    if (activityType && activityType.icon) {
      console.log(`Calendar: Found admin type for "${type}":`, activityType);
      // Try to get the icon from Lucide
      const IconComponent = LucideIcons[activityType.icon];
      if (IconComponent) {
        return <IconComponent {...iconProps} />;
      }
    }

    console.warn(`Calendar: Activity type "${type}" not found in admin activity types.`);
    console.warn('Available admin types:', activityTypes.map(t => ({ id: t.id, name: t.name, isSystem: t.isSystem })));

    // Fallback to default icon
    const DefaultIcon = LucideIcons.Calendar || LucideIcons.Activity;
    return <DefaultIcon {...iconProps} />;
  };

  // Function to get color for activity type using admin-configured colors
  const getActivityColor = (type) => {
    // First try exact ID match
    let activityType = activityTypes.find(at => at.id === type);

    // If not found by ID, try matching by name (case-insensitive)
    if (!activityType) {
      activityType = activityTypes.find(at => at.name.toLowerCase() === type?.toLowerCase());
    }

    if (activityType && activityType.color) {
      // Return the hex color for inline styles
      return activityType.color;
    }

    // Fallback to default color
    return '#6b7280'; // gray-500
  };
  const [error, setError] = useState(null);

  // Dialog states
  const [isActivityFormOpen, setIsActivityFormOpen] = useState(false);
  const [isTaskFormOpen, setIsTaskFormOpen] = useState(false);
  const [isNoteFormOpen, setIsNoteFormOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);

  // Fetch data when component mounts or contactId changes
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch activity types first
        console.log('Calendar: Fetching activity types from /api/activity-types');
        const activityTypesResponse = await fetch('/api/activity-types');
        console.log('Calendar: API response status:', activityTypesResponse.status);
        if (activityTypesResponse.ok) {
          const activityTypesData = await activityTypesResponse.json();
          console.log('Calendar: Fetched activity types from API:', activityTypesData);
          console.log('Calendar: System types:', activityTypesData.filter(t => t.isSystem));
          console.log('Calendar: Non-system types:', activityTypesData.filter(t => !t.isSystem));
          setActivityTypes(activityTypesData);
        }

        // Fetch activities
        const activitiesResponse = await fetch(`/api/contacts/${contactId}/activities`);
        if (!activitiesResponse.ok) throw new Error('Failed to fetch activities');
        const activitiesData = await activitiesResponse.json();
        setActivities(activitiesData.activities || []);

        // Fetch tasklists
        const tasklistsResponse = await fetch(`/api/contacts/${contactId}/tasklists`);
        if (!tasklistsResponse.ok) throw new Error('Failed to fetch tasklists');
        const tasklistsData = await tasklistsResponse.json();
        setTasklists(tasklistsData || []);

        // Fetch all tasklist items for all tasklists
        let allTasklistItems = [];
        for (const tl of tasklistsData || []) {
          try {
            const itemsRes = await fetch(`/api/tasklists/${tl.id}/items`);
            if (itemsRes.ok) {
              const items = await itemsRes.json();
              allTasklistItems = allTasklistItems.concat(items.map(item => ({ ...item, tasklistId: tl.id, tasklistTitle: tl.title })));
            }
          } catch (err) {
            // Ignore individual tasklist errors
          }
        }
        // Attach all items to a state variable if needed (or flatten for events)
        // setTasklistItems(allTasklistItems); // Uncomment if you want to keep them in state

        // Fetch notes
        const notesResponse = await fetch(`/api/contacts/${contactId}/notes`);
        if (!notesResponse.ok) throw new Error('Failed to fetch notes');
        const notesData = await notesResponse.json();
        setNotes(notesData || []);

        // Optionally, you can set allTasklistItems to state if you want to use it elsewhere
        // For now, you can use allTasklistItems directly in getEventsForDay
        // Example: setTasklistItems(allTasklistItems);
        // Or attach to tasklists: setTasklists(tasklistsData.map(tl => ({...tl, items: ...})))
        // But for now, just use tasklists + items for event rendering
      } catch (err) {
        if (err instanceof Error && err.message) {
          setError(err.message);
          console.error('Error fetching calendar data:', err.message);
        } else {
          setError('Failed to load calendar data. Please try again later.');
          console.error('Error fetching calendar data:', err);
        }
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [contactId]);

  // Add click-away functionality for sticky tooltips
  useEffect(() => {
    const handleClickAway = (event) => {
      // Check if the click is outside any sticky tooltip or "+X more" button
      const isClickOnTooltip = event.target.closest('.sticky-tooltip');
      const isClickOnMoreButton = event.target.closest('.more-button');

      if (!isClickOnTooltip && !isClickOnMoreButton) {
        // Close all sticky tooltips
        setStickyTooltips(prev => {
          const newSet = new Set();
          // Keep only hover tooltips, remove sticky ones
          prev.forEach(key => {
            if (key.startsWith('hover-')) {
              newSet.add(key);
            }
          });
          return newSet;
        });
        setTooltipPositions({});
      }
    };

    document.addEventListener('mousedown', handleClickAway);
    return () => {
      document.removeEventListener('mousedown', handleClickAway);
    };
  }, []);

  // Navigate to previous month
  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  // Navigate to next month
  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  // Navigate to current month
  const goToToday = () => {
    setCurrentMonth(new Date());
  };

  // Generate detailed tooltip content for an event
  const getEventTooltipContent = (event) => {
    let content = '';

    switch (event.type) {
      case 'activity':
        content = (
          <div className="space-y-1">
            <div className="font-semibold">{event.typeLabel}: {event.title}</div>
            {event.originalData.description && (
              <div className="text-gray-200">{event.originalData.description.substring(0, 100)}{event.originalData.description.length > 100 ? '...' : ''}</div>
            )}
            <div className="text-gray-300 text-[10px] mt-1">
              Date: {format(parseISO(event.originalData.date), 'MMM d, yyyy')}
              {event.originalData.time && ` at ${event.originalData.time}`}
            </div>
          </div>
        );
        break;
      case 'task':
        content = (
          <div className="space-y-1">
            <div className="font-semibold">{event.typeLabel}: {event.title}</div>
            {event.originalData.description && (
              <div className="text-gray-200">{event.originalData.description.substring(0, 100)}{event.originalData.description.length > 100 ? '...' : ''}</div>
            )}
            <div className="text-gray-300 text-[10px] mt-1">
              Due: {format(parseISO(event.originalData.dueDate), 'MMM d, yyyy')}
              {event.originalData.completed && (
                <span className="ml-2 text-green-400">✓ Completed</span>
              )}
            </div>
          </div>
        );
        break;
      case 'note':
        content = (
          <div className="space-y-1">
            <div className="font-semibold">{event.typeLabel}: {event.title}</div>
            {event.originalData.content && (
              <div className="text-gray-200">{event.originalData.content.substring(0, 100)}{event.originalData.content.length > 100 ? '...' : ''}</div>
            )}
            <div className="text-gray-300 text-[10px] mt-1">
              Created: {format(parseISO(event.originalData.createdAt), 'MMM d, yyyy')}
            </div>
          </div>
        );
        break;
      default:
        content = <div>{event.typeLabel}: {event.title}</div>;
    }

    return content;
  };

  // Handle event click to open the corresponding dialog
  const handleEventClick = (event) => {
    setSelectedEvent(event);

    switch (event.type) {
      case 'activity':
        setIsActivityFormOpen(true);
        break;
      case 'task':
        setIsTaskFormOpen(true);
        break;
      case 'note':
        setIsNoteFormOpen(true);
        break;
      default:
        console.log('Unknown event type:', event.type);
    }
  };

  const handleMoreButtonClick = (dayKey, e) => {
    e.stopPropagation();

    // Calculate tooltip position relative to the button
    const buttonRect = e.target.getBoundingClientRect();
    const position = {
      top: buttonRect.top - 10, // Position above the button
      left: buttonRect.left + (buttonRect.width / 2), // Center horizontally
    };

    setStickyTooltips(prev => {
      const newSet = new Set(prev);
      if (newSet.has(dayKey)) {
        newSet.delete(dayKey);
        setTooltipPositions(prevPos => {
          const newPos = { ...prevPos };
          delete newPos[dayKey];
          return newPos;
        });
      } else {
        newSet.add(dayKey);
        setTooltipPositions(prevPos => ({
          ...prevPos,
          [dayKey]: position
        }));
      }
      return newSet;
    });
  };

  const handleCloseTooltip = (dayKey) => {
    setStickyTooltips(prev => {
      const newSet = new Set(prev);
      newSet.delete(dayKey);
      return newSet;
    });
  };

  // Handle dialog close
  const handleDialogClose = () => {
    setIsActivityFormOpen(false);
    setIsTaskFormOpen(false);
    setIsNoteFormOpen(false);
    setSelectedEvent(null);
  };

  // Handle event update
  const handleEventUpdated = () => {
    // Just refetch all data
    const fetchData = async () => {
      setLoading(true);
      try {
        const activitiesResponse = await fetch(`/api/contacts/${contactId}/activities`);
        const activitiesData = activitiesResponse.ok ? await activitiesResponse.json() : { activities: [] };
        setActivities(activitiesData.activities || []);

        const tasklistsResponse = await fetch(`/api/contacts/${contactId}/tasklists`);
        const tasklistsData = tasklistsResponse.ok ? await tasklistsResponse.json() : [];
        setTasklists(tasklistsData || []);

        const notesResponse = await fetch(`/api/contacts/${contactId}/notes`);
        const notesData = notesResponse.ok ? await notesResponse.json() : [];
        setNotes(notesData || []);
      } catch (err) {
        // ignore
      } finally {
        setLoading(false);
      }
    };
    fetchData();
    handleDialogClose();
  };

  // Generate calendar days for the current month
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Get events for a specific day
  const getEventsForDay = (day) => {
    const dayEvents = [];

    // Add activities with admin-configured types and colors
    activities.forEach(activity => {
      const activityDate = parseISO(activity.date);
      if (isSameDay(activityDate, day)) {
        // Get admin-configured color and label
        const colorHex = getActivityColor(activity.type);

        // Find activity type for label (try both ID and name matching)
        let activityType = activityTypes.find(at => at.id === activity.type);
        if (!activityType) {
          activityType = activityTypes.find(at => at.name.toLowerCase() === activity.type?.toLowerCase());
        }
        const typeLabel = activityType ? activityType.name : (activity.type ? activity.type.charAt(0).toUpperCase() + activity.type.slice(1) : 'Activity');

        // Generate a display title - prefer title, then truncated description, then type label
        let displayTitle = '';
        if (activity.title) {
          displayTitle = activity.title;
        } else if (activity.description) {
          displayTitle = activity.description.length > 25 ?
            activity.description.substring(0, 25) + '...' :
            activity.description;
        } else {
          displayTitle = typeLabel;
        }

        dayEvents.push({
          id: activity.id,
          title: displayTitle,
          type: activity.type, // Use the actual activity type for icon lookup
          typeLabel: typeLabel,
          color: colorHex, // Store hex color for inline styles
          originalData: activity
        });
      }
    });

    // Add tasklist items (flattened)
    tasklists.forEach(tasklist => {
      (tasklist.items || []).forEach(item => {
        if (!item.dueDate) return;
        const itemDate = parseISO(item.dueDate);
        if (isSameDay(itemDate, day)) {
          // Get admin-configured color for tasks
          const taskColor = item.completed ? '#6b7280' : getActivityColor('task'); // gray for completed, admin color for active

          dayEvents.push({
            id: item.id,
            title: item.title,
            type: 'task',
            typeLabel: item.completed ? 'Completed Task' : 'Task',
            color: taskColor,
            originalData: item
          });
        }
      });
    });

    // Add notes (using createdAt date)
    notes.forEach(note => {
      const noteDate = parseISO(note.createdAt);
      if (isSameDay(noteDate, day)) {
        // Get admin-configured color for notes
        const noteColor = getActivityColor('note');

        dayEvents.push({
          id: note.id,
          title: note.title,
          type: 'note',
          typeLabel: 'Note',
          color: noteColor,
          originalData: note
        });
      }
    });

    return dayEvents;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md text-red-800 dark:text-red-200">
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-sm underline hover:text-red-600 dark:hover:text-red-300"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow h-full overflow-auto">
      {/* Calendar Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          {format(currentMonth, 'MMMM yyyy')}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={prevMonth}
            className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
            aria-label="Previous month"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
          <button
            onClick={goToToday}
            className="px-3 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm font-medium"
          >
            Today
          </button>
          <button
            onClick={nextMonth}
            className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
            aria-label="Next month"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="p-4">
        {/* Day names header */}
        <div className="grid grid-cols-7 gap-0.5 mb-1 text-center">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="text-sm font-medium text-gray-500 dark:text-gray-400">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar days */}
        <div className="grid grid-cols-7 gap-0.5 auto-rows-fr" style={{ minHeight: '600px' }}>
          {/* Empty cells for days before the start of the month */}
          {Array.from({ length: monthStart.getDay() }).map((_, index) => (
            <div key={`empty-start-${index}`} className="border border-gray-200 dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-800/50 h-28"></div>
          ))}

          {/* Actual days of the month */}
          {calendarDays.map((day) => {
            const events = getEventsForDay(day);
            const isToday = isSameDay(day, new Date());
            const dayKey = day.toISOString();
            const isTooltipSticky = stickyTooltips.has(dayKey);

            return (
              <div
                key={dayKey}
                className={`border rounded-md p-1 flex flex-col transition-colors h-28 overflow-hidden
                  ${isToday ? 'border-primary dark:border-primary-light bg-primary/5 dark:bg-primary-dark/10 shadow-sm' : 'border-gray-200 dark:border-gray-700'}
                  ${!isSameMonth(day, currentMonth) ? 'bg-gray-50 dark:bg-gray-800/50 text-gray-400 dark:text-gray-600' : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'}
                `}
              >
                <div className="text-right mb-1 flex-shrink-0">
                  <span className={`text-xs font-medium inline-flex items-center justify-center
                    ${isToday ? 'h-5 w-5 rounded-full bg-primary text-white' : 'text-gray-700 dark:text-gray-300'}
                  `}>
                    {format(day, 'd')}
                  </span>
                </div>

                <div className="flex-1 space-y-0.5 overflow-hidden">
                  {events.length > 0 ? (
                    <div className="space-y-0.5">
                      {/* Show up to 2 line items for better fit */}
                      {events.slice(0, 2).map((event) => (
                        <div
                          key={`${event.type}-${event.id}`}
                          className="text-xs px-1 py-0.5 rounded cursor-pointer hover:opacity-80 transition-opacity text-white"
                          style={{ backgroundColor: event.color }}
                          onClick={() => handleEventClick(event)}
                          role="button"
                          tabIndex="0"
                          aria-label={`${event.typeLabel}: ${event.title}`}
                          title={`${event.typeLabel}: ${event.title}`}
                        >
                          <div className="flex items-center space-x-1">
                            {getActivityIcon(event.type)}
                            <div className="font-medium truncate flex-1 text-xs leading-tight">
                              {event.title || (event.originalData.description ? event.originalData.description.substring(0, 15) + '...' : event.typeLabel)}
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Show count indicator if more than 2 events */}
                      {events.length > 2 && (
                        <>
                          <div
                            className={`more-button text-xs px-1 py-0.5 cursor-pointer text-center border rounded transition-colors ${
                              isTooltipSticky
                                ? 'text-primary dark:text-primary-light border-primary dark:border-primary-light bg-primary/10 dark:bg-primary-dark/20'
                                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-gray-300 dark:border-gray-600 hover:border-primary dark:hover:border-primary-light'
                            }`}
                            onClick={(e) => handleMoreButtonClick(dayKey, e)}
                            onMouseEnter={() => {
                              if (!isTooltipSticky) {
                                setStickyTooltips(prev => new Set([...prev, `hover-${dayKey}`]));
                              }
                            }}
                            onMouseLeave={() => {
                              if (!isTooltipSticky) {
                                setStickyTooltips(prev => {
                                  const newSet = new Set(prev);
                                  newSet.delete(`hover-${dayKey}`);
                                  return newSet;
                                });
                              }
                            }}
                          >
                            +{events.length - 2} more
                          </div>


                        </>
                      )}
                    </div>
                  ) : null}
                </div>
              </div>
            );
          })}

          {/* Empty cells for days after the end of the month */}
          {Array.from({ length: 6 - monthEnd.getDay() }).map((_, index) => (
            <div key={`empty-end-${index}`} className="border border-gray-200 dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-800/50 h-28"></div>
          ))}
        </div>

        {/* Sticky tooltips rendered outside the grid to avoid affecting cell heights */}
        {Array.from(stickyTooltips).filter(key => !key.startsWith('hover-')).map(dayKey => {
          const position = tooltipPositions[dayKey];
          const day = new Date(dayKey);
          const events = getEventsForDay(day);

          if (!position || events.length <= 2) return null;

          return (
            <div
              key={dayKey}
              className="fixed z-50"
              style={{
                top: `${position.top}px`,
                left: `${position.left}px`,
                transform: 'translate(-50%, -100%)'
              }}
            >
              <div className="sticky-tooltip bg-gray-900 dark:bg-gray-800 text-white rounded-lg shadow-lg border border-gray-700 dark:border-gray-600 max-w-xs">
                <div className="p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-semibold text-sm">{events.length - 2} more events:</div>
                    <button
                      onClick={() => handleCloseTooltip(dayKey)}
                      className="text-gray-400 hover:text-gray-300 ml-2"
                      aria-label="Close"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div className="space-y-1">
                    {events.slice(2).map((event, idx) => (
                      <div
                        key={idx}
                        className="flex items-center p-2 rounded hover:bg-gray-700 dark:hover:bg-gray-600 cursor-pointer transition-colors group"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEventClick(event);
                          handleCloseTooltip(dayKey);
                        }}
                      >
                        <div className="flex items-center mr-2 flex-shrink-0">
                          <div
                            className="w-6 h-6 rounded-full flex items-center justify-center mr-2"
                            style={{ backgroundColor: event.color }}
                          >
                            {getActivityIcon(event.type)}
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-xs font-medium text-white truncate group-hover:text-blue-300">
                            {event.title || (event.originalData.description ? event.originalData.description.substring(0, 30) + '...' : event.typeLabel)}
                          </div>
                          <div className="text-xs text-gray-300">
                            {event.typeLabel}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-2 pt-2 border-t border-gray-600 text-xs text-gray-300">
                    Click on any event to view details
                  </div>
                </div>
                {/* Tooltip arrow */}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 dark:bg-gray-800 rotate-45 border-r border-b border-gray-700 dark:border-gray-600"></div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Legend */}
      <div className="p-3 border-t border-gray-200 dark:border-gray-700">
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Activity Types</div>
        <div className="flex flex-wrap gap-3">
          {/* Admin-configured activity types */}
          {activityTypes.map((activityType) => {
            const IconComponent = activityType.icon && LucideIcons[activityType.icon];
            return (
              <div key={activityType.id} className="flex items-center">
                <div
                  className="h-4 w-4 rounded-full mr-2 flex items-center justify-center"
                  style={{ backgroundColor: activityType.color }}
                >
                  {IconComponent && (
                    <IconComponent className="h-2.5 w-2.5 text-white" />
                  )}
                </div>
                <span className="text-xs text-gray-600 dark:text-gray-400">{activityType.name}</span>
              </div>
            );
          })}

          {/* System types that aren't in admin config */}
          <div className="flex items-center">
            <div
              className="h-4 w-4 rounded-full mr-2 flex items-center justify-center bg-gray-400"
            >
              {(() => {
                // Find the task activity type to get its icon
                const taskType = activityTypes.find(t => t.name.toLowerCase() === 'task');
                const IconComponent = taskType?.icon && LucideIcons[taskType.icon];
                return IconComponent ? (
                  <IconComponent className="h-2.5 w-2.5 text-white" />
                ) : (
                  <LucideIcons.CheckSquare className="h-2.5 w-2.5 text-white" />
                );
              })()}
            </div>
            <span className="text-xs text-gray-600 dark:text-gray-400">Completed Task</span>
          </div>
        </div>
      </div>

      {/* Dialog components */}
      {selectedEvent && selectedEvent.type === 'activity' && (
        <ActivityForm
          contactId={contactId}
          activity={selectedEvent.originalData}
          isOpen={isActivityFormOpen}
          onClose={handleDialogClose}
          onSuccess={handleEventUpdated}
        />
      )}

      {selectedEvent && selectedEvent.type === 'task' && (
        <TaskForm
          contactId={contactId}
          task={selectedEvent.originalData}
          isOpen={isTaskFormOpen}
          onClose={handleDialogClose}
          onSuccess={handleEventUpdated}
        />
      )}

      {selectedEvent && selectedEvent.type === 'note' && (
        <NoteForm
          contactId={contactId}
          note={selectedEvent.originalData}
          isOpen={isNoteFormOpen}
          onClose={handleDialogClose}
          onSuccess={handleEventUpdated}
        />
      )}
    </div>
  );
}
