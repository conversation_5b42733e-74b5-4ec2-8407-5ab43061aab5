'use client';

import { useState, useEffect } from 'react';
import { Settings, Info } from 'lucide-react';
import Tooltip from '@/components/ui/Tooltip';
import Switch from '@/components/ui/Switch';

import * as Dialog from '@radix-ui/react-dialog';
import GroupMembersDialog from '../GroupMembersDialog';
import { useStatsListener } from '@/hooks/useStatsRefresh';


export default function ContactStats({ contactId }) {
  // Listen for refresh events - only refresh when relevant data changes
  const refreshCounter = useStatsListener(['activities', 'tasks', 'notes', 'communication', 'engagement']);
  const [stats, setStats] = useState({
    activityCount: 0,
    callCount: 0,
    upcomingTasks: [],
    overdueTasks: [],
    latestNote: null,
    groups: [],
    relatedContacts: [],
    lastContactDate: null,
    emailsSent: 0,
    latestEmail: null,
    engagementScore: 0,
    responseRate: '0%',
    avgResponseTime: 'N/A',
  });
  const [loading, setLoading] = useState(true);
  const [isPreferencesOpen, setIsPreferencesOpen] = useState(false);
  const [isMockData, setIsMockData] = useState(true);
  const [isMembersDialogOpen, setIsMembersDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [allContacts, setAllContacts] = useState([]);

  // Default card preferences
  const defaultPreferences = {
    visible: [
      'activity',
      'tasks',
      'notes',
      'communication',
      'engagement',
      'relationships',
    ],
    order: [
      'activity',
      'tasks',
      'notes',
      'communication',
      'engagement',
      'relationships',
    ]
  };

  const [cardPreferences, setCardPreferences] = useState(defaultPreferences);

  // Load preferences from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedPreferences = localStorage.getItem('quickStatsPreferences');
      if (savedPreferences) {
        try {
          const parsedPreferences = JSON.parse(savedPreferences);
          // Ensure we have valid preferences
          if (parsedPreferences &&
              parsedPreferences.visible && Array.isArray(parsedPreferences.visible) &&
              parsedPreferences.order && Array.isArray(parsedPreferences.order)) {



            setCardPreferences(parsedPreferences);
          }
        } catch (error) {
          console.error('Failed to parse quick stats preferences:', error);
        }
      }
    }
  }, []);

  // Function to fetch all contacts for the group members dialog
  const fetchAllContacts = async () => {
    try {
      const response = await fetch('/api/contacts');
      if (response.ok) {
        const data = await response.json();
        setAllContacts(data);
      }
    } catch (error) {
      console.error('Error fetching contacts:', error);
    }
  };

  // Function to handle opening the group contacts dialog
  const handleManageContacts = (group) => {
    setSelectedGroup(group);
    setIsMembersDialogOpen(true);
  };

  // Function to handle group contacts updates
  const handleContactsUpdated = async () => {
    try {
      // Refresh the groups data
      const response = await fetch(`/api/contacts/${contactId}/groups`);
      if (response.ok) {
        const updatedGroups = await response.json();
        setStats(prev => ({
          ...prev,
          groups: updatedGroups
        }));
      }
    } catch (error) {
      console.error('Error refreshing groups:', error);
    }
  };

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch all stats in parallel, except tasks (handled below)
        const [activityStatsRes, latestNoteRes, groupsRes, relatedContactsRes, communicationRes, engagementRes, milestonesRes, tasklistsRes] = await Promise.all([
          fetch(`/api/contacts/${contactId}/activities/stats`),
          fetch(`/api/contacts/${contactId}/notes/latest`),
          fetch(`/api/contacts/${contactId}/groups`),
          fetch(`/api/contacts/${contactId}/related`),
          fetch(`/api/contacts/${contactId}/communication`),
          fetch(`/api/contacts/${contactId}/engagement`),
          fetch(`/api/contacts/${contactId}/milestones`),
          fetch(`/api/contacts/${contactId}/tasklists`)
        ]);

        // Process responses
        const activityStats = activityStatsRes.ok ? await activityStatsRes.json() : { total: 0, byType: {} };
        const latestNote = latestNoteRes.ok ? await latestNoteRes.json() : null;
        let groups = groupsRes.ok ? await groupsRes.json() : [];

        // Fetch all groups to get member counts
        try {
          const allGroupsResponse = await fetch('/api/groups?includeMemberCounts=true');
          if (allGroupsResponse.ok) {
            const allGroups = await allGroupsResponse.json();
            groups = groups.map(group => {
              const fullGroup = allGroups.find(g => g.id === group.id);
              return {
                ...group,
                memberCount: fullGroup ? fullGroup.memberCount : 0
              };
            });
          }
        } catch (error) {
          console.error('Failed to fetch group member counts:', error);
        }
        const relatedContacts = relatedContactsRes.ok ? await relatedContactsRes.json() : [];
        const communication = communicationRes.ok ? await communicationRes.json() : { lastContactDate: null, emailsSent: 0, latestEmail: null };
        const engagement = engagementRes.ok ? await engagementRes.json() : { engagementScore: 0, responseRate: 'N/A', avgResponseTime: 'N/A' };
        const milestones = milestonesRes.ok ? await milestonesRes.json() : [];

        // --- NEW: Compute upcoming/overdue tasks from all tasklists/items ---
        let upcomingTasks = [];
        let overdueTasks = [];
        if (tasklistsRes.ok) {
          const tasklists = await tasklistsRes.json();
          // Flatten all items from all tasklists
          const allItems = tasklists.flatMap(tl => (tl.items || []).map(item => ({ ...item, tasklistTitle: tl.title })));
          const now = new Date();
          upcomingTasks = allItems.filter(item => !item.completed && item.dueDate && new Date(item.dueDate) > now)
            .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate));
          overdueTasks = allItems.filter(item => !item.completed && item.dueDate && new Date(item.dueDate) <= now)
            .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate));
        }

        setStats({
          activityCount: activityStats.total || 0,
          callCount: activityStats.byType?.call || 0,
          upcomingTasks: upcomingTasks.slice(0, 3),
          overdueTasks: overdueTasks.slice(0, 3),
          latestNote,
          groups,
          relatedContacts,
          lastContactDate: communication.lastContactDate,
          emailsSent: communication.emailsSent,
          latestEmail: communication.latestEmail,
          engagementScore: engagement.engagementScore,
          responseRate: engagement.responseRate,
          avgResponseTime: engagement.avgResponseTime,
          milestones
        });
        setIsMockData(false);
      } catch (error) {
        console.error('Failed to fetch contact stats:', error);
        setIsMockData(true);
        loadMockData();
      } finally {
        setLoading(false);
      }
    };

    // Function to use mock data
    const loadMockData = () => {
      const mockData = {
        activityCount: 12,
        callCount: 5,
        upcomingTasks: [
          { id: '1', title: 'Follow-up call', dueDate: new Date(Date.now() + 86400000), completed: false },
          { id: '2', title: 'Send proposal', dueDate: new Date(Date.now() + 259200000), completed: false }
        ],
        overdueTasks: [
          { id: '3', title: 'Submit contract', dueDate: new Date(Date.now() - 86400000), completed: false },
          { id: '4', title: 'Schedule meeting', dueDate: new Date(Date.now() - 172800000), completed: false }
        ],
        latestNote: {
          id: '1',
          title: 'Initial consultation notes',
          content: 'Client expressed interest in our premium package. They have concerns about implementation timeline. Need to address this in the proposal.',
          createdAt: new Date(Date.now() - *********)
        },
        groups: [
          { id: '1', name: 'VIP Clients', color: '#4F46E5', memberCount: 5 },
          { id: '2', name: 'Tech Industry', color: '#10B981', memberCount: 12 }
        ],
        relatedContacts: [
          { id: '1', name: 'John Smith', relationship: 'Same company' },
          { id: '2', name: 'Sarah Johnson', relationship: 'Referred by' }
        ],
        lastContactDate: new Date(Date.now() - *********),
        emailsSent: 8,
        latestEmail: {
          id: '1',
          subject: 'Proposal for services - follow up',
          date: new Date(Date.now() - *********)
        },
        engagementScore: 75,
        responseRate: '85%',
        avgResponseTime: '3.2 hours',
        milestones: [
          { id: '1', title: 'Contract renewal', date: new Date(Date.now() + 2592000000) },
          { id: '2', title: 'Quarterly review', date: new Date(Date.now() + 1209600000) }
        ]
      };
      setStats(mockData);
      setIsMockData(true);
    };

    // Try to fetch real data first
    fetchStats().catch(() => {
      // If real data fetch fails completely, use mock data
      console.log('Using mock data for Quick Stats');
      loadMockData();
    });

    // Fetch all contacts for the group members dialog
    fetchAllContacts();
  }, [contactId, refreshCounter]);

  // Format relative date (e.g., "Tomorrow", "In 3 days", "2 days ago")
  const formatRelativeDate = (date) => {
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Tomorrow';
    if (diffDays > 0 && diffDays < 7) return `In ${diffDays} days`;
    if (diffDays === 0) return 'Today';
    if (diffDays === -1) return 'Yesterday';
    if (diffDays > -7 && diffDays < 0) return `${Math.abs(diffDays)} days ago`;
    return new Date(date).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="card-light dark:bg-gray-800 rounded-lg h-full p-4 flex items-center justify-center">
        <div className="animate-pulse text-gray-400 dark:text-gray-500">Loading stats...</div>
      </div>
    );
  }

  // Function to toggle preferences dialog
  const togglePreferences = () => {
    setIsPreferencesOpen(!isPreferencesOpen);
  };

  // Function to save preferences to localStorage
  const savePreferences = (preferences) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('quickStatsPreferences', JSON.stringify(preferences));
    }
  };

  // Function to reset preferences to default
  const resetToDefault = () => {
    setCardPreferences(defaultPreferences);
    savePreferences(defaultPreferences);
  };

  // Function to toggle card visibility
  const toggleCardVisibility = (cardId) => {
    setCardPreferences(prev => {
      const isVisible = prev.visible.includes(cardId);
      let newVisible = [...prev.visible];

      if (isVisible) {
        // Remove card from visible array
        newVisible = newVisible.filter(id => id !== cardId);
      } else {
        // Add card to visible array
        newVisible.push(cardId);
      }

      const newPreferences = {
        ...prev,
        visible: newVisible
      };

      // Save to localStorage
      savePreferences(newPreferences);

      return newPreferences;
    });
  };

  // Function to move card up in order
  const moveCardUp = (cardId) => {
    setCardPreferences(prev => {
      const index = prev.order.indexOf(cardId);
      if (index <= 0) return prev; // Already at the top

      const newOrder = [...prev.order];
      [newOrder[index], newOrder[index - 1]] = [newOrder[index - 1], newOrder[index]];

      const newPreferences = {
        ...prev,
        order: newOrder
      };

      // Save to localStorage
      savePreferences(newPreferences);

      return newPreferences;
    });
  };

  // Function to move card down in order
  const moveCardDown = (cardId) => {
    setCardPreferences(prev => {
      const index = prev.order.indexOf(cardId);
      if (index === -1 || index === prev.order.length - 1) return prev; // Not found or already at the bottom

      const newOrder = [...prev.order];
      [newOrder[index], newOrder[index + 1]] = [newOrder[index + 1], newOrder[index]];

      const newPreferences = {
        ...prev,
        order: newOrder
      };

      // Save to localStorage
      savePreferences(newPreferences);

      return newPreferences;
    });
  };

  // Function to render a specific card by ID
  const renderCard = (cardId) => {
    if (!cardPreferences.visible.includes(cardId)) return null;

    switch(cardId) {
      case 'activity':
        return (
          <div key="activity" className="p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300">Activity Summary</h4>
              <Tooltip
                content={
                  <div className="w-64">
                    <p className="font-medium mb-1">Activity Summary</p>
                    <p className="text-xs mb-1">Shows the total number of activities and calls in the last 30 days.</p>
                  </div>
                }
                position="bottom"
              >
                <div className="text-blue-500 dark:text-blue-400 cursor-help">
                  <Info className="h-3.5 w-3.5" />
                </div>
              </Tooltip>
            </div>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">Last 30 days</p>
            <div className="mt-2 grid grid-cols-2 gap-2">
              <div className="text-center">
                <div className="text-xl font-semibold text-blue-700 dark:text-blue-300">{stats.activityCount}</div>
                <div className="text-xs text-blue-600 dark:text-blue-400">Activities</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-semibold text-blue-700 dark:text-blue-300">{stats.callCount}</div>
                <div className="text-xs text-blue-600 dark:text-blue-400">Calls</div>
              </div>
            </div>
          </div>
        );

      case 'tasks':
        return (
          <div key="tasks" className="p-3 bg-gray-50 dark:bg-gray-900/30 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium text-gray-800 dark:text-gray-300">Tasks</h4>
              <Tooltip
                content={
                  <div className="w-64">
                    <p className="font-medium mb-1">Tasks</p>
                    <p className="text-xs mb-1">Shows upcoming and overdue tasks that are not completed.</p>
                  </div>
                }
                position="bottom"
              >
                <div className="text-gray-500 dark:text-gray-400 cursor-help">
                  <Info className="h-3.5 w-3.5" />
                </div>
              </Tooltip>
            </div>

            {/* Upcoming Tasks Section */}
            <div className="mb-3">
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-green-500 mr-1.5"></div>
                <h5 className="text-xs font-medium text-green-700 dark:text-green-400">Upcoming</h5>
              </div>

              <div className="mt-1.5 pl-3.5 border-l border-green-200 dark:border-green-800 space-y-2">
                {stats.upcomingTasks.length > 0 ? (
                  stats.upcomingTasks.map(task => (
                    <div key={task.id} className="text-xs text-gray-700 dark:text-gray-400 flex justify-between">
                      <span className="truncate mr-2">{task.title}</span>
                      <span className="whitespace-nowrap text-green-600 dark:text-green-500">{formatRelativeDate(new Date(task.dueDate))}</span>
                    </div>
                  ))
                ) : (
                  <div className="text-xs text-gray-500 dark:text-gray-500 italic">No upcoming tasks</div>
                )}
              </div>
            </div>

            {/* Overdue Tasks Section */}
            <div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-red-500 mr-1.5"></div>
                <h5 className="text-xs font-medium text-red-700 dark:text-red-400">Overdue</h5>
              </div>

              <div className="mt-1.5 pl-3.5 border-l border-red-200 dark:border-red-800 space-y-2">
                {stats.overdueTasks.length > 0 ? (
                  stats.overdueTasks.map(task => (
                    <div key={task.id} className="text-xs text-gray-700 dark:text-gray-400 flex justify-between">
                      <span className="truncate mr-2">{task.title}</span>
                      <span className="whitespace-nowrap text-red-600 dark:text-red-500">Due {formatRelativeDate(new Date(task.dueDate))}</span>
                    </div>
                  ))
                ) : (
                  <div className="text-xs text-gray-500 dark:text-gray-500 italic">No overdue tasks</div>
                )}
              </div>
            </div>
          </div>
        );

      case 'notes':
        return (
          <div key="notes" className="p-3 bg-amber-50 dark:bg-amber-900/30 rounded-lg">
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium text-amber-800 dark:text-amber-300">Latest Note</h4>
              <Tooltip
                content={
                  <div className="w-64">
                    <p className="font-medium mb-1">Latest Note</p>
                    <p className="text-xs mb-1">Shows the most recent note added for this contact.</p>
                  </div>
                }
                position="bottom"
              >
                <div className="text-amber-500 dark:text-amber-400 cursor-help">
                  <Info className="h-3.5 w-3.5" />
                </div>
              </Tooltip>
            </div>
            {stats.latestNote ? (
              <div className="mt-2">
                <div className="text-xs font-medium text-amber-700 dark:text-amber-400">{stats.latestNote.title}</div>
                <div className="text-xs text-amber-600 dark:text-amber-500 mt-1 line-clamp-3">{stats.latestNote.content}</div>
                <div className="text-xs text-amber-500 dark:text-amber-600 mt-1">
                  {new Date(stats.latestNote.createdAt).toLocaleDateString()}
                </div>
              </div>
            ) : (
              <div className="text-xs text-amber-600 dark:text-amber-400 italic mt-2">No notes yet</div>
            )}
          </div>
        );



      case 'communication':
        return (
          <div key="communication" className="p-3 bg-indigo-50 dark:bg-indigo-900/30 rounded-lg">
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium text-indigo-800 dark:text-indigo-300">Communication</h4>
              <Tooltip
                content={
                  <div className="w-64">
                    <p className="font-medium mb-1">Communication</p>
                    <p className="text-xs mb-1">Shows communication history with this contact, including last contact date and emails sent.</p>
                  </div>
                }
                position="bottom"
              >
                <div className="text-indigo-500 dark:text-indigo-400 cursor-help">
                  <Info className="h-3.5 w-3.5" />
                </div>
              </Tooltip>
            </div>
            <div className="mt-2 grid grid-cols-2 gap-2">
              <div>
                <div className="text-xs text-indigo-700 dark:text-indigo-400">Last Contact</div>
                <div className="text-sm font-medium text-indigo-800 dark:text-indigo-300">
                  {stats.lastContactDate ? formatRelativeDate(new Date(stats.lastContactDate)) : 'Never'}
                </div>
              </div>
              <div>
                <div className="text-xs text-indigo-700 dark:text-indigo-400">Emails Sent</div>
                <div className="text-sm font-medium text-indigo-800 dark:text-indigo-300">{stats.emailsSent || 0}</div>
              </div>
            </div>

            {/* Latest Email */}
            {stats.latestEmail && (
              <div className="mt-2 pt-2 border-t border-indigo-100 dark:border-indigo-800">
                <div className="text-xs font-medium text-indigo-700 dark:text-indigo-400">Latest Email</div>
                <div className="text-xs text-indigo-600 dark:text-indigo-500 mt-1 line-clamp-1">{stats.latestEmail.subject}</div>
                <div className="text-xs text-indigo-500 dark:text-indigo-600 mt-0.5">
                  {new Date(stats.latestEmail.date).toLocaleDateString()}
                </div>
              </div>
            )}
          </div>
        );

      case 'engagement':
        return (
          <div key="engagement" className="p-3 bg-teal-50 dark:bg-teal-900/30 rounded-lg">
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium text-teal-800 dark:text-teal-300">Engagement</h4>
              <Tooltip
                content={
                  <div className="w-64">
                    <p className="font-medium mb-1">Engagement</p>
                    <p className="text-xs mb-1">Shows engagement metrics for this contact, including engagement score, response rate, and average response time.</p>
                  </div>
                }
                position="bottom"
              >
                <div className="text-teal-500 dark:text-teal-400 cursor-help">
                  <Info className="h-3.5 w-3.5" />
                </div>
              </Tooltip>
            </div>
            <div className="mt-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-teal-700 dark:text-teal-400">Engagement Score</span>
                <span className="text-sm font-medium text-teal-800 dark:text-teal-300">{stats.engagementScore || 'N/A'}</span>
              </div>

              {/* Progress bar */}
              <div className="w-full bg-teal-200 dark:bg-teal-800 rounded-full h-1.5 mt-1">
                <div
                  className="bg-teal-500 h-1.5 rounded-full"
                  style={{ width: `${stats.engagementScore || 0}%` }}
                ></div>
              </div>

              <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                <div>
                  <div className="text-teal-700 dark:text-teal-400">Response Rate</div>
                  <div className="font-medium text-teal-800 dark:text-teal-300">{stats.responseRate || 'N/A'}</div>
                </div>
                <div>
                  <div className="text-teal-700 dark:text-teal-400">Avg. Response Time</div>
                  <div className="font-medium text-teal-800 dark:text-teal-300">{stats.avgResponseTime || 'N/A'}</div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'relationships':
        return (
          <div key="relationships" className="p-3 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium text-purple-800 dark:text-purple-300">Relationships</h4>
              <Tooltip
                content={
                  <div className="w-64">
                    <p className="font-medium mb-1">Relationships</p>
                    <p className="text-xs mb-1">Shows relationships with other contacts, including groups and related contacts.</p>
                  </div>
                }
                position="bottom"
              >
                <div className="text-purple-500 dark:text-purple-400 cursor-help">
                  <Info className="h-3.5 w-3.5" />
                </div>
              </Tooltip>
            </div>

            {/* Groups */}
            {stats.groups && stats.groups.length > 0 && (
              <div className="mt-2">
                <div className="text-xs font-medium text-purple-700 dark:text-purple-400 mb-1">Groups</div>
                <div className="space-y-1">
                  {stats.groups.map(group => (
                    <div
                      key={group.id}
                      className="flex items-center justify-between p-1 rounded hover:bg-purple-100 dark:hover:bg-purple-800/30 cursor-pointer transition-colors"
                      onClick={() => handleManageContacts(group)}
                    >
                      <div className="flex items-center">
                        <span
                          className="w-2 h-2 rounded-full mr-2"
                          style={{ backgroundColor: group.color }}
                        ></span>
                        <span
                          className="text-xs font-medium"
                          style={{ color: group.color }}
                        >
                          {group.name}
                        </span>
                      </div>
                      <div className="text-xs text-purple-500 dark:text-purple-400">
                        {group.memberCount || '0'} contacts
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Related Contacts */}
            {stats.relatedContacts && stats.relatedContacts.length > 0 && (
              <div className="mt-2">
                <div className="text-xs font-medium text-purple-700 dark:text-purple-400 mb-1">Related Contacts</div>
                <div className="space-y-1">
                  {stats.relatedContacts.map(contact => (
                    <div key={contact.id} className="text-xs text-purple-700 dark:text-purple-400 flex items-center">
                      <span className="w-1.5 h-1.5 rounded-full bg-purple-500 mr-1.5"></span>
                      {contact.name} <span className="text-purple-500 dark:text-purple-500 ml-1">({contact.relationship})</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Placeholder for future Household feature */}
            <div className="mt-2 border-t border-purple-100 dark:border-purple-800 pt-2">
              <div className="text-xs text-purple-600 dark:text-purple-500 flex items-center">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Household: <span className="ml-1 italic">None</span>
              </div>
            </div>
          </div>
        );



      default:
        return null;
    }
  };

  return (
    <>
      <Dialog.Root open={isMembersDialogOpen} onOpenChange={setIsMembersDialogOpen}>
        {selectedGroup && (
          <GroupMembersDialog
            group={selectedGroup}
            contacts={allContacts}
            onClose={() => setIsMembersDialogOpen(false)}
            onMembersUpdated={handleContactsUpdated}
          />
        )}
      </Dialog.Root>

      <div className="card-light dark:bg-gray-800 rounded-lg h-full overflow-auto p-3">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-base font-medium text-gray-900 dark:text-white">Quick Stats</h3>
        <button
          onClick={togglePreferences}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
          title="Customize Quick Stats"
        >
          <Settings className="h-4 w-4" />
        </button>
      </div>

      {isPreferencesOpen && (
        <div className="mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Customize Quick Stats</h4>
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">Select which stats to display and their order.</p>

          <div className="space-y-2">
            {cardPreferences.order.map((cardId) => {
              const isVisible = cardPreferences.visible.includes(cardId);
              let cardName = '';
              switch(cardId) {
                case 'activity': cardName = 'Activity Summary'; break;
                case 'tasks': cardName = 'Tasks'; break;
                case 'notes': cardName = 'Latest Note'; break;
                case 'communication': cardName = 'Communication'; break;
                case 'engagement': cardName = 'Engagement'; break;
                case 'relationships': cardName = 'Relationships'; break;
                case 'milestones': cardName = 'Milestones'; break;
                default: cardName = cardId;
              }

              return (
                <div key={cardId} className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center w-[calc(100%-60px)] min-w-0">
                    <Switch
                      id={`card-${cardId}`}
                      checked={isVisible}
                      onChange={() => toggleCardVisibility(cardId)}
                      size="sm"
                    />
                    <div className="min-w-0 flex-1 overflow-hidden">
                      <Tooltip content={cardName} position="bottom">
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300 truncate block cursor-help">
                          {cardName}
                        </span>
                      </Tooltip>
                    </div>
                  </div>
                  <div className="flex space-x-1 flex-shrink-0 ml-2">
                    <Tooltip content="Move up" position="bottom">
                      <button
                        onClick={() => moveCardUp(cardId)}
                        disabled={cardPreferences.order.indexOf(cardId) === 0}
                        className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 disabled:opacity-30 disabled:cursor-not-allowed"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                        </svg>
                      </button>
                    </Tooltip>
                    <Tooltip content="Move down" position="bottom">
                      <button
                        onClick={() => moveCardDown(cardId)}
                        disabled={cardPreferences.order.indexOf(cardId) === cardPreferences.order.length - 1}
                        className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 disabled:opacity-30 disabled:cursor-not-allowed"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                    </Tooltip>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="mt-3 flex justify-between">
            <button
              onClick={resetToDefault}
              className="px-3 py-1 text-xs text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded transition-colors"
            >
              Reset to Default
            </button>
            <button
              onClick={togglePreferences}
              className="px-3 py-1 text-xs text-white bg-primary hover:bg-primary-hover rounded transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}

      <div className="space-y-3">
        {/* Render cards in the order specified in preferences */}
        {cardPreferences.order.map(cardId => renderCard(cardId))}
      </div>
    </div>
    </>
  );
}
