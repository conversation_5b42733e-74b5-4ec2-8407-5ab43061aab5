import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

// GET /api/contacts/[id]/checklists - Get all checklists for a contact
export async function GET(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const contactId = params.id;
    // Get the current user
    const { user } = await (await import('@/lib/auth')).getSignedInUser(request);
    let whereClause = { id: String(contactId) };
    if (user.role !== 'admin') {
      whereClause.userId = String(user.id);
    }
    // Verify contact exists and is accessible
    const contact = await prisma.contact.findFirst({ where: whereClause });
    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }
    
    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const search = searchParams.get('search');
    
    // Build the where clause based on query parameters
    const where = { contactId };
    
    if (category) {
      where.category = category;
    }
    
    if (status) {
      where.status = status;
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }
    
    // Get checklists with their items
    const checklists = await prisma.checklist.findMany({
      where,
      include: {
        items: {
          orderBy: {
            order: 'asc'
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      }
    });
    
    return NextResponse.json(checklists);
  } catch (error) {
    console.error('Failed to fetch checklists:', error);
    return NextResponse.json(
      { error: 'Failed to fetch checklists: ' + error.message },
      { status: 500 }
    );
  }
}

// POST /api/contacts/[id]/checklists - Create a new checklist for a contact
export async function POST(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const contactId = params.id;
    // Get the current user
    const { user } = await (await import('@/lib/auth')).getSignedInUser(request);
    let whereClause = { id: String(contactId) };
    if (user.role !== 'admin') {
      whereClause.userId = String(user.id);
    }
    // Verify contact exists and is accessible
    const contact = await prisma.contact.findFirst({ where: whereClause });
    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }
    
    const body = await request.json();
    
    // Validate required fields
    if (!body.title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      );
    }
    
    // Create the checklist with its items
    const checklist = await prisma.checklist.create({
      data: {
        title: body.title,
        description: body.description,
        category: body.category,
        status: body.status || 'in_progress',
        contactId,
        items: {
          create: body.items?.map((item, index) => ({
            text: item.text,
            description: item.description,
            completed: item.completed || false,
            completedAt: item.completed ? new Date() : null,
            order: index
          })) || []
        }
      },
      include: {
        items: {
          orderBy: {
            order: 'asc'
          }
        }
      }
    });
    
    return NextResponse.json(checklist);
  } catch (error) {
    console.error('Failed to create checklist:', error);
    return NextResponse.json(
      { error: 'Failed to create checklist: ' + error.message },
      { status: 500 }
    );
  }
}
