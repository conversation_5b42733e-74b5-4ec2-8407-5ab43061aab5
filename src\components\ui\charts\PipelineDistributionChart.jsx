'use client';

import { Bar } from 'react-chartjs-2';

export default function PipelineDistributionChart({ data }) {
  const chartData = {
    labels: data.map(item => item.stage),
    datasets: [{
      label: 'Contacts',
      data: data.map(item => item.count),
      backgroundColor: '#3B82F6',
      borderRadius: 4,
    }],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#1F2937',
        titleColor: '#F3F4F6',
        bodyColor: '#F3F4F6',
        padding: 12,
        displayColors: false,
        callbacks: {
          label: function(context) {
            return `${context.parsed.y} contacts`;
          }
        }
      },
    },
    barThickness: 24,
  };

  return (
    <div className="h-[300px]">
      <Bar data={chartData} options={options} />
    </div>
  );
}