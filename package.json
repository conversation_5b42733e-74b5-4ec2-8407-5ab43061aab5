{"name": "slim-crm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.5.2", "@azure/msal-browser": "^4.11.0", "@azure/msal-node": "^3.5.1", "@azure/msal-react": "^3.0.10", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-tabs": "^1.1.3", "@tanstack/react-query": "^5.74.4", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "chart.js": "^4.4.8", "date-fns": "^4.1.0", "express-rate-limit": "^7.5.0", "file-saver": "^2.0.5", "framer-motion": "^12.6.5", "jsdom": "^26.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.488.0", "mongodb": "^6.15.0", "next": "15.2.4", "papaparse": "^5.5.2", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-window": "^1.8.11", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.17", "eslint": "^9", "eslint-config-next": "15.2.4", "postcss": "^8.4.35", "prisma": "^6.7.0", "tailwindcss": "^3.4.1"}, "prisma": {"seed": "node prisma/seed.js"}}