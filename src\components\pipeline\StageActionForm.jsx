'use client';

import { useState, useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { STAGE_ACTION_TYPES } from '@/lib/constants/stageActionTypes';
import { ACTIVITY_TYPES } from '@/lib/constants/activityTypes';

export default function StageActionForm({
  stageId,
  stageName,
  action = null,
  isOpen,
  onClose,
  onSuccess
}) {
  const [formData, setFormData] = useState({
    actionType: 'activity',
    actionDetails: {
      type: 'call',
      title: '',
      description: '',
      date: new Date().toISOString().split('T')[0], // For activities
      dueDate: new Date().toISOString().split('T')[0], // For tasks
      priority: 'medium'
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // If editing an existing action, populate the form
  useEffect(() => {
    if (action) {
      setFormData({
        actionType: action.actionType,
        actionDetails: action.actionDetails
      });
    } else {
      // Reset form for new action
      setFormData({
        actionType: 'activity',
        actionDetails: {
          type: 'call',
          title: '',
          description: '',
          date: new Date().toISOString().split('T')[0], // For activities
          dueDate: new Date().toISOString().split('T')[0], // For tasks
          priority: 'medium'
        }
      });
    }
  }, [action, isOpen]);

  // Update action details based on action type
  useEffect(() => {
    if (formData.actionType === 'activity') {
      setFormData(prev => {
        // Make sure we have all required fields for an activity
        const activityDetails = {
          type: prev.actionDetails.type || 'call',
          description: prev.actionDetails.description || '',
          date: new Date().toISOString().split('T')[0] // Add a default date
        };

        console.log('Setting activity details:', activityDetails);

        return {
          ...prev,
          actionDetails: activityDetails
        };
      });
    } else if (formData.actionType === 'task') {
      setFormData(prev => ({
        ...prev,
        actionDetails: {
          title: prev.actionDetails.title || '',
          description: prev.actionDetails.description || '',
          dueDate: prev.actionDetails.dueDate || new Date().toISOString().split('T')[0],
          priority: prev.actionDetails.priority || 'medium'
        }
      }));
    } else if (formData.actionType === 'note') {
      setFormData(prev => ({
        ...prev,
        actionDetails: {
          title: prev.actionDetails.title || '',
          content: prev.actionDetails.content || ''
        }
      }));
    } else if (formData.actionType === 'email') {
      setFormData(prev => ({
        ...prev,
        actionDetails: {
          subject: prev.actionDetails.subject || '',
          body: prev.actionDetails.body || ''
        }
      }));
    }
  }, [formData.actionType]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    // Make sure we have all required fields for the selected action type
    if (formData.actionType === 'activity' && !formData.actionDetails.type) {
      setError('Activity type is required');
      setIsSubmitting(false);
      return;
    }

    if (formData.actionType === 'task' && !formData.actionDetails.title) {
      setError('Task title is required');
      setIsSubmitting(false);
      return;
    }

    if (formData.actionType === 'note' && (!formData.actionDetails.title || !formData.actionDetails.content)) {
      setError('Note title and content are required');
      setIsSubmitting(false);
      return;
    }

    if (formData.actionType === 'email' && (!formData.actionDetails.subject || !formData.actionDetails.body)) {
      setError('Email subject and body are required');
      setIsSubmitting(false);
      return;
    }

    try {
      let url, method, body;

      // Prepare the data based on action type
      let preparedFormData = { ...formData };

      // For activities, make sure we have all required fields
      if (formData.actionType === 'activity') {
        preparedFormData = {
          ...formData,
          actionDetails: {
            ...formData.actionDetails,
            date: formData.actionDetails.date || new Date().toISOString().split('T')[0]
          }
        };
      }

      console.log('Prepared form data:', preparedFormData);

      if (action) {
        // Update existing action
        url = `/api/pipeline/actions/${action.id}`;
        method = 'PATCH';
        body = preparedFormData;
      } else {
        // Create new action
        url = `/api/pipeline/stages/${stageId}/actions`;
        method = 'POST';
        body = preparedFormData;
      }

      console.log('Sending action data:', JSON.stringify(body, null, 2));

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);

        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.error || 'Failed to save action');
        } catch (parseError) {
          throw new Error(`Failed to save action: ${errorText}`);
        }
      }

      const savedAction = await response.json();
      onSuccess(savedAction);
      onClose();
    } catch (error) {
      console.error('Failed to save action:', error);
      setError(error.message || 'Failed to save action');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render different form fields based on action type
  const renderActionFields = () => {
    switch (formData.actionType) {
      case 'activity':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Activity Type
              </label>
              <select
                value={formData.actionDetails.type}
                onChange={(e) => setFormData({
                  ...formData,
                  actionDetails: {
                    ...formData.actionDetails,
                    type: e.target.value
                  }
                })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                required
              >
                {ACTIVITY_TYPES.map((type) => (
                  <option key={type.id} value={type.id}>
                    {type.icon} {type.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                value={formData.actionDetails.description}
                onChange={(e) => setFormData({
                  ...formData,
                  actionDetails: {
                    ...formData.actionDetails,
                    description: e.target.value
                  }
                })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                rows={3}
                placeholder="Activity description"
                required
              />
            </div>
          </>
        );

      case 'task':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Title
              </label>
              <input
                type="text"
                value={formData.actionDetails.title}
                onChange={(e) => setFormData({
                  ...formData,
                  actionDetails: {
                    ...formData.actionDetails,
                    title: e.target.value
                  }
                })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Task title"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                value={formData.actionDetails.description}
                onChange={(e) => setFormData({
                  ...formData,
                  actionDetails: {
                    ...formData.actionDetails,
                    description: e.target.value
                  }
                })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                rows={3}
                placeholder="Task description"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Due Date Offset (days)
                </label>
                <input
                  type="number"
                  value={formData.actionDetails.dueOffset || 1}
                  onChange={(e) => setFormData({
                    ...formData,
                    actionDetails: {
                      ...formData.actionDetails,
                      dueOffset: parseInt(e.target.value)
                    }
                  })}
                  min={0}
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Priority
                </label>
                <select
                  value={formData.actionDetails.priority}
                  onChange={(e) => setFormData({
                    ...formData,
                    actionDetails: {
                      ...formData.actionDetails,
                      priority: e.target.value
                    }
                  })}
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>
            </div>
          </>
        );

      case 'note':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Title
              </label>
              <input
                type="text"
                value={formData.actionDetails.title}
                onChange={(e) => setFormData({
                  ...formData,
                  actionDetails: {
                    ...formData.actionDetails,
                    title: e.target.value
                  }
                })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Note title"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Content
              </label>
              <textarea
                value={formData.actionDetails.content}
                onChange={(e) => setFormData({
                  ...formData,
                  actionDetails: {
                    ...formData.actionDetails,
                    content: e.target.value
                  }
                })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                rows={4}
                placeholder="Note content"
                required
              />
            </div>
          </>
        );

      case 'email':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Subject
              </label>
              <input
                type="text"
                value={formData.actionDetails.subject}
                onChange={(e) => setFormData({
                  ...formData,
                  actionDetails: {
                    ...formData.actionDetails,
                    subject: e.target.value
                  }
                })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Email subject"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Body
              </label>
              <textarea
                value={formData.actionDetails.body}
                onChange={(e) => setFormData({
                  ...formData,
                  actionDetails: {
                    ...formData.actionDetails,
                    body: e.target.value
                  }
                })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                rows={4}
                placeholder="Email body"
                required
              />
            </div>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-xl">
          <Dialog.Title className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            {action ? 'Edit Automated Action' : 'Add New Automated Action'}
          </Dialog.Title>

          <div className="mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Configure an action that will be triggered when a contact enters the <span className="font-medium text-primary">{stageName}</span> stage.
            </p>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Action Type
              </label>
              <select
                value={formData.actionType}
                onChange={(e) => setFormData({
                  ...formData,
                  actionType: e.target.value
                })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                required
              >
                {STAGE_ACTION_TYPES.map((type) => (
                  <option key={type.id} value={type.id}>
                    {type.icon} {type.label}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                {STAGE_ACTION_TYPES.find(t => t.id === formData.actionType)?.description}
              </p>
            </div>

            {renderActionFields()}

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover disabled:opacity-50"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : action ? 'Update Action' : 'Add Action'}
              </button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
