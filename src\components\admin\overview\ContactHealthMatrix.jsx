"use client";

import { useState } from "react";
import { 
  PieChart, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Toolt<PERSON>,
  Legend
} from "recharts";
import { 
  Users, 
  AlertTriangle, 
  TrendingUp, 
  Activity,
  Eye
} from "lucide-react";
import CustomTooltip from "@/components/ui/Tooltip";

export default function ContactHealthMatrix({ data, detailed = false, compact = false }) {
  const [activeChart, setActiveChart] = useState('risk');

  if (!data) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Contact Health Matrix
        </h3>
        <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
          No health data available
        </div>
      </div>
    );
  }

  // Prepare risk distribution data for pie chart
  const riskData = [
    { name: 'Low Risk', value: data.riskDistribution.low || 0, color: '#10b981' },
    { name: 'Medium Risk', value: data.riskDistribution.medium || 0, color: '#f59e0b' },
    { name: 'High Risk', value: data.riskDistribution.high || 0, color: '#ef4444' }
  ].filter(item => item.value > 0);

  // Prepare engagement distribution data for bar chart
  const engagementData = Object.entries(data.engagementRanges || {}).map(([range, count]) => ({
    range,
    count,
    color: range.includes('High') ? '#10b981' : range.includes('Medium') ? '#f59e0b' : '#ef4444'
  }));

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3">
          <p className="font-medium text-gray-900 dark:text-white">
            {data.name || label}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Count: <span className="font-medium">{data.value || data.payload?.count}</span>
          </p>
        </div>
      );
    }
    return null;
  };

  const formatPercentage = (value, total) => {
    return total > 0 ? `${((value / total) * 100).toFixed(1)}%` : '0%';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      {/* Header */}
      <div className={`flex items-center justify-between ${compact ? "mb-3" : "mb-6"}`}>
        <div>
          <CustomTooltip content="Analysis of contact risk levels and engagement scores to identify healthy vs at-risk relationships" position="top">
            <h3 className={`font-semibold text-gray-900 dark:text-white ${compact ? "text-base" : "text-lg"}`}>
              Contact Health Matrix
            </h3>
          </CustomTooltip>
          {!compact && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Risk assessment and engagement analysis
            </p>
          )}
        </div>
        {detailed && (
          <button className="flex items-center space-x-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm">
            <Eye className="h-4 w-4" />
            <span>View Details</span>
          </button>
        )}
      </div>

      {/* Chart Toggle */}
      <div className="flex space-x-2 mb-6">
        <button
          onClick={() => setActiveChart('risk')}
          className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
            activeChart === 'risk'
              ? 'bg-primary text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          Risk Distribution
        </button>
        <button
          onClick={() => setActiveChart('engagement')}
          className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
            activeChart === 'engagement'
              ? 'bg-primary text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          Engagement Levels
        </button>
      </div>

      {/* Charts */}
      <div className={`mb-4 ${compact ? "h-48" : "h-64"}`}>
        {activeChart === 'risk' && riskData.length > 0 && (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={riskData}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={5}
                dataKey="value"
              >
                {riskData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        )}

        {activeChart === 'engagement' && engagementData.length > 0 && (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={engagementData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
              <XAxis 
                dataKey="range" 
                stroke="#6b7280"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis stroke="#6b7280" fontSize={12} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="count" radius={[4, 4, 0, 0]}>
                {engagementData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        )}

        {((activeChart === 'risk' && riskData.length === 0) || 
          (activeChart === 'engagement' && engagementData.length === 0)) && (
          <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
            No {activeChart} data available
          </div>
        )}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* Total Contacts */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Total Contacts
            </h4>
            <Users className="h-4 w-4 text-blue-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {data.totalWithMetrics || 0}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            With health metrics
          </p>
        </div>

        {/* Dormant Contacts */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Dormant
            </h4>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {data.dormantCount || 0}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {formatPercentage(data.dormantCount || 0, data.totalWithMetrics || 0)} of total
          </p>
        </div>

        {/* High Value */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              High Value
            </h4>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {data.highValueCount || 0}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {formatPercentage(data.highValueCount || 0, data.totalWithMetrics || 0)} of total
          </p>
        </div>

        {/* Health Score */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Avg Health
            </h4>
            <Activity className="h-4 w-4 text-purple-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {(() => {
              // Calculate average engagement score from engagement ranges
              const ranges = data.engagementRanges || {};
              const total = Object.values(ranges).reduce((sum, count) => sum + count, 0);
              if (total === 0) return 'N/A';

              const weightedSum =
                (ranges['High (70-100)'] || 0) * 85 +
                (ranges['Medium (40-69)'] || 0) * 55 +
                (ranges['Low (0-39)'] || 0) * 20;

              return `${(weightedSum / total).toFixed(1)}%`;
            })()}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Overall engagement score
          </p>
        </div>
      </div>

      {/* Risk Breakdown */}
      {activeChart === 'risk' && riskData.length > 0 && (
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
            Risk Level Breakdown
          </h4>
          <div className="space-y-2">
            {riskData.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {item.name}
                  </span>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {item.value} ({formatPercentage(item.value, data.totalWithMetrics || 0)})
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
