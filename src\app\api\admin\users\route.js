import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser, hasRole } from "@/lib/auth";
import { auditLogger } from "@/lib/services/auditLogger";
import { ROLE_ADMIN } from "@/constants/permissions";

/**
 * GET /api/admin/users - Get all users
 * This endpoint returns all users in the system
 * Only accessible to admins
 */
export async function GET(request) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);

    if (user.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get all users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        permissions: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            contacts: true,
            contactGroups: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(users);
  } catch (error) {
    console.error("Failed to fetch users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/users - Create a new user
 * This endpoint creates a new user
 * Only accessible to admins
 */
export async function POST(request) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);

    if (user.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.email || !body.name) {
      return NextResponse.json(
        { error: "Email and name are required" },
        { status: 400 }
      );
    }

    // Check if user with this email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: body.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 400 }
      );
    }

    // Create the user
    const newUser = await prisma.user.create({
      data: {
        email: body.email,
        name: body.name,
        azureId: body.azureId || `azure-id-${Date.now()}`, // Generate a temporary Azure ID
        role: body.role || "user",
        permissions: body.permissions || [],
      },
    });

    // Log the user creation
    await auditLogger.logUserCreate({
      userId: user.id,
      user: newUser,
      request
    });

    return NextResponse.json(newUser);
  } catch (error) {
    console.error("Failed to create user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
}
