import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";

// export const runtime = "nodejs";

export async function POST(request) {
  try {
    console.log(34);
    console.log(34);
    console.log(34);
    console.log(34);
    console.log(34);
    console.log(34);
    console.log(34);
    console.log(34);
    console.log(34);
    console.log(34);
    console.log(34);
    console.log(34);
    const { sessionID } = await request.json();

    console.log("THE SESSIONID IN THE VERIFY-SESSION ROUTE:", sessionID);

    const session = await prisma.session.findUnique({
      where: {
        sessionID: sessionID,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!session) {
      return NextResponse.json({ valid: false }, { status: 401 });
    }

    return NextResponse.json({ valid: true });
  } catch (error) {
    console.error("Session verification error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// In verify-session/route.js
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    console.log(
      "THE SEARCH PARAMS IN THE VERIFY-SESSION GET ROUTE:",
      searchParams
    );
    const sessionID = searchParams.get("sessionID");
    console.log("THE SESSIONID IN THE VERIFY-SESSION GET ROUTE:", sessionID);

    const session = await prisma.session.findUnique({
      where: {
        sessionID: sessionID,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!session) {
      return NextResponse.json({ valid: false }, { status: 401 });
    }
    return NextResponse.json({ valid: true });
  } catch (error) {
    console.error("Session verification error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }

  // Rest of your code...
}
