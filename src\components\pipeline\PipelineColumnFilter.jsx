'use client';

import { useState, useEffect, useRef } from 'react';
import { Filter, SortAsc, SortDesc, X } from 'lucide-react';
import Tooltip from '@/components/ui/Tooltip';

export default function PipelineColumnFilter({ onFilterChange, onSortChange, activeFilters = {}, activeSort = {} }) {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState(activeFilters);
  const [localSort, setLocalSort] = useState(activeSort);
  const menuRef = useRef(null);

  // Check if any filters are active
  const hasActiveFilters = Object.values(localFilters).some(value => value !== '') || localSort.field;

  // Effect for click-away functionality
  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }

    // Add event listener when menu is open
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Clean up the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleFilterChange = (field, value) => {
    const newFilters = { ...localFilters, [field]: value };
    setLocalFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleSortChange = (field) => {
    let direction = 'asc';

    // If already sorting by this field, toggle direction
    if (localSort.field === field) {
      direction = localSort.direction === 'asc' ? 'desc' : 'asc';
    }

    const newSort = { field, direction };
    setLocalSort(newSort);
    onSortChange(newSort);
  };

  const clearFilters = () => {
    setLocalFilters({});
    setLocalSort({ field: null, direction: 'asc' });
    onFilterChange({});
    onSortChange({ field: null, direction: 'asc' });
    setIsOpen(false); // Close the menu after clearing
  };

  return (
    <div className="relative">
      <div className="flex items-center space-x-1">
        {hasActiveFilters && (
          <Tooltip content="Clear all filters" position="bottom">
            <button
              onClick={clearFilters}
              className="p-1 rounded-md text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20"
            >
              <X className="h-3 w-3" />
            </button>
          </Tooltip>
        )}
        <Tooltip content="Filter and sort contacts" position="bottom">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className={`p-1 rounded-md ${
            hasActiveFilters || localSort.field
              ? 'text-primary bg-primary/10 dark:bg-primary/20'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
        >
          <Filter className="h-3.5 w-3.5" />
          </button>
        </Tooltip>
      </div>

      {isOpen && (
        <div ref={menuRef} className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
          <div className="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">Filter & Sort</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          <div className="p-3">
            <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Filter by</h4>

            <div className="space-y-2 mb-4">
              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">Name</label>
                <input
                  type="text"
                  value={localFilters.name || ''}
                  onChange={(e) => handleFilterChange('name', e.target.value)}
                  placeholder="Filter by name"
                  className="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">Company</label>
                <input
                  type="text"
                  value={localFilters.company || ''}
                  onChange={(e) => handleFilterChange('company', e.target.value)}
                  placeholder="Filter by company"
                  className="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">Type</label>
                <select
                  value={localFilters.type || ''}
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                  className="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="">All Types</option>
                  <option value="lead">Lead</option>
                  <option value="customer">Customer</option>
                  <option value="partner">Partner</option>
                </select>
              </div>
            </div>

            <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Sort by</h4>

            <div className="space-y-1">
              <button
                onClick={() => handleSortChange('name')}
                className={`flex items-center justify-between w-full px-2 py-1 text-sm rounded-md ${
                  localSort.field === 'name'
                    ? 'bg-primary/10 dark:bg-primary/20 text-primary'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <span>Name</span>
                {localSort.field === 'name' && (
                  localSort.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                )}
              </button>

              <button
                onClick={() => handleSortChange('company')}
                className={`flex items-center justify-between w-full px-2 py-1 text-sm rounded-md ${
                  localSort.field === 'company'
                    ? 'bg-primary/10 dark:bg-primary/20 text-primary'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <span>Company</span>
                {localSort.field === 'company' && (
                  localSort.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                )}
              </button>

              <button
                onClick={() => handleSortChange('createdAt')}
                className={`flex items-center justify-between w-full px-2 py-1 text-sm rounded-md ${
                  localSort.field === 'createdAt'
                    ? 'bg-primary/10 dark:bg-primary/20 text-primary'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <span>Date Added</span>
                {localSort.field === 'createdAt' && (
                  localSort.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                )}
              </button>

              <button
                onClick={() => handleSortChange('lastContactDate')}
                className={`flex items-center justify-between w-full px-2 py-1 text-sm rounded-md ${
                  localSort.field === 'lastContactDate'
                    ? 'bg-primary/10 dark:bg-primary/20 text-primary'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <span>Last Contact</span>
                {localSort.field === 'lastContactDate' && (
                  localSort.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                )}
              </button>
            </div>

            {hasActiveFilters && (
              <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={clearFilters}
                  className="text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
                >
                  Clear all filters
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
