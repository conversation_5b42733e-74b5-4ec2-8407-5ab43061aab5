import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

export async function GET(request, { params }) {
  try {
    const { id } = await params;
    console.log('Fetching communication stats for contact:', id);

    // Get activities to determine last contact date
    const activities = await prisma.activity.findMany({
      where: { contactId: id },
      orderBy: { date: 'desc' },
      take: 1
    });

    // In a real implementation, you would:
    // 1. Count emails sent to this contact
    // 2. Get the latest email
    // 3. Calculate response rates and times
    
    // For now, we'll return mock data
    const communicationStats = {
      lastContactDate: activities.length > 0 ? activities[0].date : null,
      emailsSent: 0, // Mock value
      latestEmail: null // Mock value
    };

    return NextResponse.json(communicationStats);
  } catch (error) {
    console.error('Failed to fetch communication stats:', error);
    return NextResponse.json(
      { error: `Failed to fetch communication stats: ${error.message}` },
      { status: 500 }
    );
  }
}
