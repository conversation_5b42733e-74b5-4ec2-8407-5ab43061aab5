import { NextResponse } from "next/server";
import { cookies } from "next/headers";
// import { getSession } from "../login/route.js";
import { getSession } from "@/lib/auth";

export async function GET(request) {
  console.log("IN THE STATUS ROUTE");

  const sessionData = await getSession(request);

  if (!sessionData) {
    return NextResponse.json({ isAuthenticated: false });
  }

  console.log("THE SESSION DATA IN THE STATUS ROUTE: ", sessionData);

  const { user } = sessionData;

  console.log("THE USER DATA IN THE STATUS ROUTE: ", user);

  if (!user) {
    return NextResponse.json({ isAuthenticated: false });
  }

  return NextResponse.json({
    isAuthenticated: true,
    userData: user,
  });
}
