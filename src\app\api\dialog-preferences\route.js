import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';

// GET /api/dialog-preferences - Get dialog field preferences for the current user
export async function GET(request) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const dialogKey = searchParams.get('dialogKey');

    // Validate required parameters
    if (!dialogKey) {
      return NextResponse.json(
        { error: 'dialogKey parameter is required' },
        { status: 400 }
      );
    }

    // Find existing preferences
    const preferences = await prisma.dialogFieldPreferences.findUnique({
      where: {
        userId_dialogKey: {
          userId: user.id,
          dialogKey: dialogKey
        }
      }
    });

    // If no preferences exist, return default (empty array means all fields are visible)
    if (!preferences) {
      return NextResponse.json({ visibleFields: [] });
    }

    return NextResponse.json(preferences);
  } catch (error) {
    console.error('Failed to fetch dialog preferences:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dialog preferences' },
      { status: 500 }
    );
  }
}

// POST /api/dialog-preferences - Create or update dialog field preferences
export async function POST(request) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    
    // Validate required fields
    if (!body.dialogKey) {
      return NextResponse.json(
        { error: 'dialogKey is required' },
        { status: 400 }
      );
    }

    if (!Array.isArray(body.visibleFields)) {
      return NextResponse.json(
        { error: 'visibleFields must be an array' },
        { status: 400 }
      );
    }

    // Upsert the preferences (create if doesn't exist, update if it does)
    const preferences = await prisma.dialogFieldPreferences.upsert({
      where: {
        userId_dialogKey: {
          userId: user.id,
          dialogKey: body.dialogKey
        }
      },
      update: {
        visibleFields: body.visibleFields,
        updatedAt: new Date()
      },
      create: {
        userId: user.id,
        dialogKey: body.dialogKey,
        visibleFields: body.visibleFields,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    return NextResponse.json(preferences);
  } catch (error) {
    console.error('Failed to save dialog preferences:', error);
    return NextResponse.json(
      { error: 'Failed to save dialog preferences' },
      { status: 500 }
    );
  }
}
