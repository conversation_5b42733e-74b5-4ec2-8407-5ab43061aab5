"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Stepper from "@/components/ui/Stepper";
import { X, Clock } from "lucide-react";

const CAMPAIGN_TYPES = [
  { id: "one-time", label: "One-time Campaign" },
  { id: "drip", label: "Automated Drip Campaign" },
];

const STEPS = [
  { id: "basic", label: "Basic" },
  { id: "automation", label: "Automation" },
  { id: "review", label: "Review" },
];

const CONDITION_TYPES = {
  OPENED: "opened",
  CLICKED: "clicked",
  NOT_OPENED: "not_opened",
  NOT_CLICKED: "not_clicked",
  NO_CONDITION: "no_condition",
};

const CONDITION_LABELS = {
  [CONDITION_TYPES.OPENED]:
    "Send only to recipients who opened the previous email",
  [CONDITION_TYPES.CLICKED]:
    "Send only to recipients who clicked a link in the previous email",
  [CONDITION_TYPES.NOT_OPENED]:
    "Send only to recipients who did not open the previous email",
  [CONDITION_TYPES.NOT_CLICKED]:
    "Send only to recipients who did not click any links in the previous email",
  [CONDITION_TYPES.NO_CONDITION]:
    "Send to all recipients regardless of previous actions",
};

export default function CreateCampaign() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    campaignType: "one-time", // Or "drip"
    tags: [],
    template: null, // Changed from templateId
    distributionList: null, // Changed from distributionListId
    sendType: "immediate", // Or "scheduled"
    scheduledTime: "",
    scheduledDate: "", // Add this new field
    sequence: [], // Add this line
  });

  const [tagInput, setTagInput] = useState("");
  const [templates, setTemplates] = useState([]);
  const [distributionLists, setDistributionLists] = useState([]);
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tags, setTags] = useState([]);

  console.log("The formData after each state change: ", formData);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const templatesResponse = await fetch("/api/email-templates");
        const templatesData = await templatesResponse.json();
        setTemplates(templatesData);

        const listsResponse = await fetch("/api/distribution-lists");
        const listsData = await listsResponse.json();
        setDistributionLists(listsData);

        // Fetch global tags
        const tagsResponse = await fetch("/api/admin/tags");
        const tagsData = await tagsResponse.json();

        const filteredEmailTags = tagsData.filter(
          (tag) => tag.type === "Email"
        );
        setTags(filteredEmailTags);
      } catch (err) {
        setError("Failed to load templates or distribution lists");
      }
    };

    fetchData();
  }, []);

  console.log("The Tag Data: ", tags);

  const handleTemplateChange = async (templateId) => {
    // If there is no template selected, clear the template and content
    if (!templateId) {
      setFormData((prev) => ({
        ...prev,
        template: null,
        content: "",
        subject: "",
      }));
      return;
    }
    // Otherwise, add the template to the formdata
    try {
      const response = await fetch(`/api/email-templates/${templateId}`);
      const template = await response.json();

      setFormData((prev) => ({
        ...prev,
        template,
      }));
    } catch (err) {
      setError("Failed to load template");
    }
  };

  const handleDistributionListChange = (listId) => {
    const selectedList = distributionLists.find((list) => list.id === listId);
    setFormData((prev) => ({
      ...prev,
      distributionList: selectedList || null,
    }));
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      if (currentStep === 0) {
        if (formData.campaignType === "one-time") {
          // Skip automation step for one-time campaigns
          setCurrentStep(2); // Go directly to review step
        } else {
          // For drip campaigns, initialize the sequence with the template from step 1
          // if the sequence is empty
          if (formData.sequence.length === 0 && formData.template) {
            setFormData((prev) => ({
              ...prev,
              sequence: [
                {
                  templateId: formData.template.id,
                  delayDays: 0,
                  delayHours: 0,
                  condition: CONDITION_TYPES.NO_CONDITION,
                  order: 1,
                  // Use the scheduled date/time from step 1 if available, otherwise use current date/time
                  scheduledDate:
                    prev.sendType === "scheduled"
                      ? prev.scheduledDate
                      : new Date().toISOString().split("T")[0],
                  scheduledTime:
                    prev.sendType === "scheduled"
                      ? prev.scheduledTime
                      : "09:00",
                },
              ],
            }));
          }
          setCurrentStep((prev) => prev + 1);
        }
      } else if (currentStep === STEPS.length - 1) {
        handleSubmit();
      } else {
        setCurrentStep((prev) => prev + 1);
      }
    }
  };

  const handleSubmit = async () => {
    try {
      console.log("Creating campaign...");

      const response = await fetch("/api/email-campaigns", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push("/emails?tab=campaigns");
        console.log("Campaign created successfully");
      } else {
        const data = await response.json();
        throw new Error(data.error || "Failed to create campaign");
      }
    } catch (error) {
      console.error("Failed to create campaign:", error);
    }
  };

  const handleBack = () => {
    // if the campaignType is "one-time" and the current step is 2, go back to step 0
    if (formData.campaignType === "one-time" && currentStep === 2) {
      setCurrentStep(0);
      return;
    } else {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const handleAddTag = (e) => {
    e.preventDefault();
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }));
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  const validateCurrentStep = () => {
    switch (currentStep) {
      case 0: // Basic step
        if (
          !formData.name ||
          !formData.description ||
          !formData.template ||
          !formData.distributionList ||
          (formData.sendType === "scheduled" &&
            (!formData.scheduledTime || !formData.scheduledDate))
        ) {
          setError("Please fill in all required fields");
          return false;
        }

        // Additional validation for scheduled campaigns
        if (formData.sendType === "scheduled") {
          const selectedDateTime = new Date(
            `${formData.scheduledDate}T${formData.scheduledTime}`
          );
          if (selectedDateTime < new Date()) {
            setError("Scheduled time must be in the future");
            return false;
          }
        }

        setError("");
        return true;
      case 1: // Automation step
        if (formData.campaignType === "drip") {
          if (!formData.sequence || formData.sequence.length === 0) {
            setError("Please add at least one email to the sequence");
            return false;
          }

          if (formData.sequence.some((email) => !email.templateId)) {
            setError("Please select a template for each email in the sequence");
            return false;
          }

          if (formData.sequence.length > 1) {
            const invalidSchedules = formData.sequence
              .slice(1)
              .some((email) => !email.scheduledDate || !email.scheduledTime);
            if (invalidSchedules) {
              setError(
                "Please set a date and time for each email after the first one"
              );
              return false;
            }

            // Check that each email is scheduled after the previous one
            for (let i = 1; i < formData.sequence.length; i++) {
              const currentEmail = formData.sequence[i];
              const prevEmail = formData.sequence[i - 1];

              const currentDateTime = new Date(
                `${currentEmail.scheduledDate}T${currentEmail.scheduledTime}:00`
              );
              const prevDateTime = new Date(
                `${prevEmail.scheduledDate}T${prevEmail.scheduledTime}:00`
              );

              if (currentDateTime <= prevDateTime) {
                setError(`Email ${i + 1} must be scheduled after Email ${i}`);
                return false;
              }
            }

            const invalidConditions = formData.sequence
              .slice(1)
              .some((email) => !email.condition);
            if (invalidConditions) {
              setError(
                "Please set a condition for each email after the first one"
              );
              return false;
            }
          }
        }
        setError("");
        return true;
      case 2: // Review Step
        setError("");
        return true;
      default:
        return true;
    }
  };

  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const ampm = hour >= 12 ? "PM" : "AM";
        const displayHour = hour % 12 || 12; // Convert 24h to 12h format
        const formattedHour = hour.toString().padStart(2, "0"); // Keep 24h format for value
        const formattedMinute = minute.toString().padStart(2, "0");
        const timeString = `${formattedHour}:${formattedMinute}`; // Value stays in 24h format
        const displayString = `${displayHour}:${formattedMinute} ${ampm}`; // Display in 12h format
        options.push({ value: timeString, label: displayString });
      }
    }
    return options;
  };

  // Add helper function to get minimum allowed date (today)
  const getMinDate = () => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  };

  const renderBasicStep = () => (
    <div className="space-y-6">
      <div>
        <label htmlFor="name" className="block text-sm font-medium mb-2">
          Campaign Name *
        </label>
        <input
          type="text"
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
          required
        />
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium mb-2">
          Campaign Description *
        </label>
        <textarea
          id="description"
          value={formData.description}
          onChange={(e) =>
            setFormData({ ...formData, description: e.target.value })
          }
          className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
          rows={3}
          required
        />
      </div>

      <div>
        <label
          htmlFor="campaignType"
          className="block text-sm font-medium mb-2"
        >
          Campaign Type *
        </label>
        <div className="grid grid-cols-2 gap-4">
          {CAMPAIGN_TYPES.map((type) => (
            <div
              key={type.id}
              className={`border rounded-md p-4 cursor-pointer transition-colors ${
                formData.campaignType === type.id
                  ? "border-primary bg-primary/5"
                  : "hover:border-gray-400"
              }`}
              onClick={() =>
                setFormData({ ...formData, campaignType: type.id })
              }
            >
              <div className="flex items-center space-x-2">
                <div
                  className={`w-4 h-4 rounded-full border-2 ${
                    formData.campaignType === type.id
                      ? "border-primary bg-primary"
                      : "border-gray-400"
                  }`}
                />
                <span className="font-medium">{type.label}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <label htmlFor="tags" className="block text-sm font-medium mb-2">
          Tags
        </label>
        <div className="space-y-2">
          <div className="flex flex-wrap gap-2 mb-2">
            {formData.tags.map((tag) => (
              <span
                key={tag}
                className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md flex items-center space-x-1"
              >
                <span>{tag}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveTag(tag)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
          <div className="flex space-x-2">
            <select
              value=""
              onChange={(e) => {
                const selectedTagName = e.target.value;
                if (
                  selectedTagName &&
                  !formData.tags.includes(selectedTagName)
                ) {
                  setFormData((prev) => ({
                    ...prev,
                    tags: [...prev.tags, selectedTagName],
                  }));
                }
                // Reset the select to show placeholder
                e.target.value = "";
              }}
              className="flex-1 px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
            >
              <option value="" disabled>
                Select a tag to add
              </option>
              {tags
                .filter((tag) => !formData.tags.includes(tag.name))
                .map((tag) => (
                  <option key={tag.id} value={tag.name}>
                    {tag.name}
                  </option>
                ))}
            </select>
          </div>
        </div>
      </div>

      <div>
        <label htmlFor="template" className="block text-sm font-medium mb-2">
          Email Template *
        </label>
        <select
          id="template"
          value={formData.template?.id || ""}
          onChange={(e) => handleTemplateChange(e.target.value)}
          className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
          required
        >
          <option value="">Select a template</option>
          {templates.map((template) => (
            <option key={template.id} value={template.id}>
              {template.name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label
          htmlFor="distributionList"
          className="block text-sm font-medium mb-2"
        >
          Distribution List *
        </label>
        <select
          id="distributionList"
          value={formData.distributionList?.id || ""}
          onChange={(e) => handleDistributionListChange(e.target.value)}
          className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
          required
        >
          <option value="">Select a distribution list</option>
          {distributionLists.map((list) => (
            <option key={list.id} value={list.id}>
              {list.name} ({list.subscriberCount} subscribers)
            </option>
          ))}
        </select>
      </div>

      <div className="space-y-4">
        <label className="block text-sm font-medium mb-2">Send Options *</label>
        <div className="grid grid-cols-2 gap-4">
          <div
            onClick={() => setFormData({ ...formData, sendType: "immediate" })}
            className={`border rounded-md p-4 cursor-pointer transition-colors ${
              formData.sendType === "immediate"
                ? "border-primary bg-primary/5"
                : "hover:border-gray-400"
            }`}
          >
            <div className="flex items-center space-x-2">
              <div
                className={`w-4 h-4 rounded-full border-2 ${
                  formData.sendType === "immediate"
                    ? "border-primary bg-primary"
                    : "border-gray-400"
                }`}
              />
              <span className="font-medium">Send Immediately</span>
            </div>
          </div>

          <div
            onClick={() => setFormData({ ...formData, sendType: "scheduled" })}
            className={`border rounded-md p-4 cursor-pointer transition-colors ${
              formData.sendType === "scheduled"
                ? "border-primary bg-primary/5"
                : "hover:border-gray-400"
            }`}
          >
            <div className="flex items-center space-x-2">
              <div
                className={`w-4 h-4 rounded-full border-2 ${
                  formData.sendType === "scheduled"
                    ? "border-primary bg-primary"
                    : "border-gray-400"
                }`}
              />
              <span className="font-medium">Schedule for Later</span>
            </div>
          </div>
        </div>

        {formData.sendType === "scheduled" && (
          <div className="mt-4 space-y-4 p-4 border-l-2 border-primary/30">
            <div>
              <label className="block text-sm font-medium mb-1">
                Select Date *
              </label>
              <input
                type="date"
                value={formData.scheduledDate}
                min={getMinDate()}
                onChange={(e) =>
                  setFormData({ ...formData, scheduledDate: e.target.value })
                }
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                required={formData.sendType === "scheduled"}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">
                Select Time (15-minute intervals) *
              </label>
              <select
                value={formData.scheduledTime}
                onChange={(e) =>
                  setFormData({ ...formData, scheduledTime: e.target.value })
                }
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                required={formData.sendType === "scheduled"}
              >
                <option value="">Select a time</option>
                {generateTimeOptions().map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500">
                Campaigns are processed every 15 minutes
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderAutomationStep = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium">Email Sequence</h2>
        <button
          onClick={() => {
            setFormData((prev) => {
              // Get the last email in the sequence or use current date/time if it's the first
              const lastEmail =
                prev.sequence.length > 0
                  ? prev.sequence[prev.sequence.length - 1]
                  : null;

              // Default to tomorrow's date if there's a previous email
              let defaultDate = "";
              let defaultTime = "";

              if (lastEmail && lastEmail.scheduledDate) {
                // For subsequent emails, use the previous email's date + 1 day
                const nextDay = new Date(lastEmail.scheduledDate);
                nextDay.setDate(nextDay.getDate() + 1);
                defaultDate = nextDay.toISOString().split("T")[0];
                defaultTime = lastEmail.scheduledTime || "09:00";
              } else if (prev.sendType === "scheduled" && prev.scheduledDate) {
                // For first email in sequence, use campaign scheduled date
                defaultDate = prev.scheduledDate;
                defaultTime = prev.scheduledTime || "09:00";
              } else {
                // Default to tomorrow
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                defaultDate = tomorrow.toISOString().split("T")[0];
                defaultTime = "09:00";
              }

              return {
                ...prev,
                sequence: [
                  ...(prev.sequence || []),
                  {
                    templateId: "",
                    delayDays: prev.sequence.length > 0 ? 1 : 0, // 0 for first email, 1 for subsequent
                    delayHours: 0,
                    condition: CONDITION_TYPES.NO_CONDITION,
                    order: prev.sequence.length + 1,
                    scheduledDate: defaultDate,
                    scheduledTime: defaultTime,
                  },
                ],
              };
            });
          }}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover"
        >
          Add Email to Sequence
        </button>
      </div>

      {!formData.sequence || formData.sequence.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p className="text-gray-500 dark:text-gray-400">
            No emails in sequence yet. Add your first email to get started.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {formData.sequence.map((email, index) => (
            <div
              key={index}
              className="border rounded-lg p-6 space-y-4 relative dark:border-gray-700"
            >
              <div className="absolute -left-3 top-6 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center">
                {index + 1}
              </div>

              <div className="flex justify-between items-start">
                <h3 className="text-lg font-medium">
                  {index === 0 ? "First Email" : `Follow-up Email ${index}`}
                </h3>
                <button
                  onClick={() => {
                    setFormData((prev) => ({
                      ...prev,
                      sequence: prev.sequence.filter((_, i) => i !== index),
                    }));
                  }}
                  className="text-gray-400 hover:text-red-500"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              {/* Template selection */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Email Template *
                </label>
                <select
                  value={email.templateId}
                  onChange={(e) => {
                    setFormData((prev) => ({
                      ...prev,
                      sequence: prev.sequence.map((seq, i) =>
                        i === index
                          ? { ...seq, templateId: e.target.value }
                          : seq
                      ),
                    }));
                  }}
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                  required
                >
                  <option value="">Select a template</option>
                  {templates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* For the first email, show when it will be sent based on step 1 settings */}
              {index === 0 && (
                <div className="mt-2 text-sm">
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                    <div className="flex items-center text-blue-700 dark:text-blue-300">
                      <Clock className="h-4 w-4 mr-2" />
                      <span className="font-medium">Sending Time:</span>
                    </div>
                    <p className="mt-1 ml-6 text-blue-600 dark:text-blue-400">
                      {formData.sendType === "immediate"
                        ? "Will be sent immediately when campaign is created"
                        : `Will be sent on ${new Date(
                            `${formData.scheduledDate}T${formData.scheduledTime}`
                          ).toLocaleDateString([], {
                            weekday: "long",
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })} at ${new Date(
                            `${formData.scheduledDate}T${formData.scheduledTime}`
                          ).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}`}
                    </p>
                    <p className="mt-1 ml-6 text-xs text-blue-500 dark:text-blue-300">
                      This is based on the settings from step 1
                    </p>
                  </div>
                </div>
              )}

              {/* For subsequent emails, show the date/time picker */}
              {index > 0 && (
                <>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Schedule Date and Time
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">
                          Date
                        </label>
                        <input
                          type="date"
                          value={email.scheduledDate || ""}
                          min={getMinDate()}
                          onChange={(e) => {
                            setFormData((prev) => ({
                              ...prev,
                              sequence: prev.sequence.map((seq, i) =>
                                i === index
                                  ? {
                                      ...seq,
                                      scheduledDate: e.target.value,
                                      // Calculate delayDays and delayHours based on the selected date and time
                                      ...calculateDelay(
                                        e.target.value,
                                        seq.scheduledTime || "",
                                        prev.sequence[index - 1].scheduledDate,
                                        prev.sequence[index - 1].scheduledTime
                                      ),
                                    }
                                  : seq
                              ),
                            }));
                          }}
                          className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">
                          Time
                        </label>
                        <select
                          value={email.scheduledTime || ""}
                          onChange={(e) => {
                            setFormData((prev) => ({
                              ...prev,
                              sequence: prev.sequence.map((seq, i) =>
                                i === index
                                  ? {
                                      ...seq,
                                      scheduledTime: e.target.value,
                                      // Calculate delayDays and delayHours based on the selected date and time
                                      ...calculateDelay(
                                        seq.scheduledDate || "",
                                        e.target.value,
                                        prev.sequence[index - 1].scheduledDate,
                                        prev.sequence[index - 1].scheduledTime
                                      ),
                                    }
                                  : seq
                              ),
                            }));
                          }}
                          className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                        >
                          <option value="">Select a time</option>
                          {generateTimeOptions().map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      This email will be sent {email.delayDays} days and{" "}
                      {email.delayHours} hours after the previous email
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Recipient Filter
                    </label>
                    <select
                      value={email.condition}
                      onChange={(e) => {
                        setFormData((prev) => ({
                          ...prev,
                          sequence: prev.sequence.map((seq, i) =>
                            i === index
                              ? { ...seq, condition: e.target.value }
                              : seq
                          ),
                        }));
                      }}
                      className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                    >
                      {Object.entries(CONDITION_LABELS).map(
                        ([value, label]) => (
                          <option key={value} value={value}>
                            {label}
                          </option>
                        )
                      )}
                    </select>
                  </div>
                </>
              )}

              {/* Display calculated send time for all emails */}
              <div className="mt-2 text-sm text-gray-500">
                {index === 0 ? (
                  formData.sendType === "scheduled" ? (
                    <div>
                      Scheduled to send on{" "}
                      {new Date(
                        `${formData.scheduledDate}T${formData.scheduledTime}`
                      ).toLocaleDateString()}{" "}
                      at{" "}
                      {new Date(
                        `${formData.scheduledDate}T${formData.scheduledTime}`
                      ).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </div>
                  ) : (
                    <div>Will send immediately when campaign is created</div>
                  )
                ) : (
                  <div>
                    Scheduled to send {email.delayDays} days and{" "}
                    {email.delayHours} hours after the previous email
                    {email.condition &&
                      email.condition !== CONDITION_TYPES.NO_CONDITION && (
                        <div className="mt-1 text-blue-600 dark:text-blue-400">
                          <span>⚡ Conditional Send: </span>
                          {CONDITION_LABELS[email.condition]
                            ? CONDITION_LABELS[email.condition].toLowerCase()
                            : "unknown condition"}
                        </div>
                      )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
          Sequence Summary
        </h3>
        <ul className="list-disc list-inside text-sm text-blue-700 dark:text-blue-300">
          <li>
            {formData.sendType === "immediate"
              ? "First email will be sent immediately when campaign is created"
              : `First email will be sent on ${new Date(
                  `${formData.scheduledDate}T${formData.scheduledTime}`
                ).toLocaleDateString()} at ${new Date(
                  `${formData.scheduledDate}T${formData.scheduledTime}`
                ).toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}`}
          </li>
          {formData.sequence?.slice(1).map((email, index) => (
            <li key={index}>
              Email {index + 2} will be sent after{" "}
              {email.delayDays > 0 ? `${email.delayDays} days` : ""}{" "}
              {email.delayDays > 0 && email.delayHours > 0 ? "and " : ""}
              {email.delayHours > 0 ? `${email.delayHours} hours` : ""}
              {email.condition !== CONDITION_TYPES.NO_CONDITION && (
                <span className="text-blue-600 dark:text-blue-400">
                  {" "}
                  (Filter:{" "}
                  {CONDITION_LABELS[email.condition]?.toLowerCase() ||
                    "unknown condition"}
                  )
                </span>
              )}
            </li>
          ))}
        </ul>
        <p className="mt-4 text-sm text-blue-700 dark:text-blue-300">
          💡 Tip: Use conditions to create targeted follow-up sequences based on
          how recipients interact with previous emails.
        </p>
      </div>
    </div>
  );

  const renderReviewStep = () => {
    return (
      <div className="space-y-8">
        {/* Basic Information */}
        <div className="border rounded-lg p-6 dark:border-gray-700">
          <h3 className="text-lg font-medium mb-4">Campaign Details</h3>
          <dl className="grid grid-cols-1 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Name
              </dt>
              <dd className="mt-1 text-sm">{formData.name}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Description
              </dt>
              <dd className="mt-1 text-sm">{formData.description}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Campaign Type
              </dt>
              <dd className="mt-1 text-sm capitalize">
                {formData.campaignType}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Template
              </dt>
              <dd className="mt-1 text-sm">{formData.template?.name}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Distribution List
              </dt>
              <dd className="mt-1 text-sm">
                {formData.distributionList?.name}
                <span className="text-gray-500 ml-2">
                  (
                  {formData.distributionList?.contacts.length +
                    formData.distributionList?.groups.reduce((acc, group) => {
                      return (acc += group.contacts.length);
                    }, 0)}{" "}
                  subscribers)
                </span>
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Send Type
              </dt>
              <dd className="mt-1 text-sm capitalize">{formData.sendType}</dd>
            </div>
            {formData.sendType === "scheduled" && (
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Scheduled Time
                </dt>
                <dd className="mt-1 text-sm">
                  {new Date(
                    `${formData.scheduledDate}T${formData.scheduledTime}`
                  ).toLocaleString([], {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                  {/* {formData.scheduledDate} {formData.scheduledTime} */}
                </dd>
              </div>
            )}
          </dl>
        </div>

        {/* Email Sequence */}
        {formData.campaignType === "drip" && (
          <div className="border rounded-lg p-6 dark:border-gray-700">
            <h3 className="text-lg font-medium mb-4">Email Sequence</h3>
            <div className="space-y-6">
              {formData.sequence.map((email, index) => (
                <div
                  key={index}
                  className="border-l-4 border-primary pl-4 py-2"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium">
                      {index === 0 ? "First Email" : `Email ${index + 1}`}
                    </span>
                    {index === 0 ? (
                      <span className="text-sm text-gray-500">
                        {formData.sendType === "scheduled"
                          ? `(Scheduled for ${new Date(
                              `${formData.scheduledDate}T${formData.scheduledTime}`
                            ).toLocaleDateString()} at ${new Date(
                              `${formData.scheduledDate}T${formData.scheduledTime}`
                            ).toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })})`
                          : "(Sends immediately)"}
                      </span>
                    ) : (
                      <span className="text-sm text-gray-500">
                        ({email.delayDays} days, {email.delayHours} hours after
                        previous)
                      </span>
                    )}
                  </div>

                  <dl className="grid grid-cols-1 gap-2 text-sm">
                    <div>
                      <dt className="font-medium text-gray-500 dark:text-gray-400">
                        Template
                      </dt>
                      <dd>
                        {templates.find((t) => t.id === email.templateId)
                          ?.name || "Unknown template"}
                      </dd>
                    </div>

                    {index > 0 &&
                      email.condition &&
                      email.condition !== CONDITION_TYPES.NO_CONDITION && (
                        <div>
                          <dt className="font-medium text-gray-500 dark:text-gray-400">
                            Sending Condition
                          </dt>
                          <dd className="text-blue-600 dark:text-blue-400">
                            {CONDITION_LABELS[email.condition] ||
                              "Unknown condition"}
                          </dd>
                        </div>
                      )}
                  </dl>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Distribution Lists */}
        <div className="border rounded-lg p-6 dark:border-gray-700">
          <h3 className="text-lg font-medium mb-4">Distribution</h3>
          <dl>
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Selected List
            </dt>
            <dd className="mt-1">
              {formData.distributionList !== null ? (
                <ul className="list-disc list-inside text-sm">
                  <li>
                    {formData.distributionList.name}{" "}
                    <span className="text-gray-500">
                      (
                      {formData.distributionList?.contacts.length +
                        formData.distributionList.groups.reduce(
                          (acc, group) => {
                            return (acc += group.contacts.length);
                          },
                          0
                        )}{" "}
                      subscribers)
                    </span>
                  </li>
                  {/* {distributionLists.map((listId) => {
                    const list = distributionLists.find((l) => l.id === listId);
                    return (
                      <li key={listId}>
                        {list?.name || "Unknown list"}
                        <span className="text-gray-500">
                          (
                          {list?.contacts.length ||
                            0 +
                              list?.groups.reduce((acc, group) => {
                                return (acc += group.contacts.length);
                              }, 0) ||
                            0}{" "}
                          subscribers)
                        </span>
                      </li>
                    );
                  })} */}
                </ul>
              ) : (
                <p className="text-sm text-gray-500">
                  No distribution lists selected
                </p>
              )}
            </dd>
          </dl>
        </div>

        {/* Campaign Summary */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            Campaign Summary
          </h3>
          <ul className="list-disc list-inside text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li>
              {formData.campaignType === "drip" && formData.sequence.length > 1
                ? `This drip campaign will send a sequence of ${formData.sequence.length} emails.`
                : `This one-time campaign will send a single email.`}
            </li>
            {formData.campaignType === "drip" &&
              formData.sequence.length > 1 && (
                <li>
                  The sequence will span approximately{" "}
                  {formData.sequence.reduce(
                    (total, email) => total + (email.delayDays || 0),
                    0
                  )}{" "}
                  days
                </li>
              )}
            <li>
              Total potential recipients:{" "}
              {formData.distributionList?.contacts.length +
                formData.distributionList?.groups.reduce((acc, group) => {
                  return (acc += group?.contacts.length);
                }, 0)}{" "}
              subscribers
            </li>
          </ul>
        </div>
      </div>
    );
  };

  const getStepStatus = (stepIndex) => {
    if (formData.campaignType === "one-time" && stepIndex === 1) {
      return "disabled";
    }
    if (stepIndex < currentStep) return "completed";
    if (stepIndex === currentStep) return "current";
    return "upcoming";
  };

  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Link
            href="/emails"
            className="mr-4 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
          >
            <span>← Back to Emails</span>
          </Link>
          <h1 className="text-2xl font-semibold">Create Email Campaign</h1>
        </div>
      </div>

      <Stepper
        steps={STEPS}
        currentStep={currentStep}
        getStepStatus={getStepStatus}
      />

      {error && (
        <div className="mb-4 p-4 text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}

      <div className="mt-8">
        {currentStep === 0 && renderBasicStep()}
        {currentStep === 1 &&
          formData.campaignType === "drip" &&
          renderAutomationStep()}
        {currentStep === 2 && renderReviewStep()}
      </div>

      <div className="flex justify-end space-x-3 pt-8">
        <Link
          href="/emails"
          className="px-4 py-2 border rounded-md hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
        >
          Cancel
        </Link>

        {currentStep > 0 && (
          <button
            type="button"
            onClick={handleBack}
            className="px-4 py-2 border rounded-md hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
          >
            Back
          </button>
        )}

        <button
          type="button"
          onClick={handleNext}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover disabled:opacity-50"
        >
          {currentStep === STEPS.length - 1 ? "Create Campaign" : "Next"}
        </button>
      </div>
    </div>
  );
}

const calculateDelay = (currentDate, currentTime, prevDate, prevTime) => {
  if (!currentDate || !currentTime || !prevDate || !prevTime) {
    return { delayDays: 0, delayHours: 0 };
  }

  // Create Date objects for current and previous email times
  const currentDateTime = new Date(`${currentDate}T${currentTime}:00`);
  const prevDateTime = new Date(`${prevDate}T${prevTime}:00`);

  // Calculate difference in milliseconds
  const diffMs = currentDateTime - prevDateTime;

  // Convert to days and hours
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const remainingMs = diffMs % (1000 * 60 * 60 * 24);
  const diffHours = Math.floor(remainingMs / (1000 * 60 * 60));

  return { delayDays: diffDays, delayHours: diffHours };
};
