import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Mock data for current and previous periods
    const thisWeekMetrics = [
      { sent: true, opened: true, clicked: true, bounced: false },
      { sent: true, opened: true, clicked: false, bounced: false },
      { sent: true, opened: true, clicked: true, bounced: false },
      { sent: true, opened: false, clicked: false, bounced: true },
      { sent: true, opened: true, clicked: false, bounced: false },
    ];

    const lastWeekMetrics = [
      { sent: true, opened: true, clicked: false, bounced: false },
      { sent: true, opened: false, clicked: false, bounced: true },
      { sent: true, opened: true, clicked: false, bounced: false },
    ];

    // Mock monthly statistics
    const monthlyStats = [
      { month: "Jan", sent: 150, opened: 120, clicked: 80 },
      { month: "Feb", sent: 180, opened: 140, clicked: 95 },
      { month: "Mar", sent: 220, opened: 170, clicked: 110 },
      { month: "Apr", sent: 190, opened: 150, clicked: 90 },
      { month: "May", sent: 210, opened: 165, clicked: 100 },
      { month: "Jun", sent: 240, opened: 190, clicked: 120 },
      { month: "Jul", sent: 260, opened: 200, clicked: 130 },
      { month: "Aug", sent: 280, opened: 220, clicked: 140 },
    ];

    // Mock template performance data
    const templatePerformance = [
      { name: "Welcome Email", openRate: 0.85, clickRate: 0.45 },
      { name: "Newsletter", openRate: 0.75, clickRate: 0.35 },
      { name: "Promotional", openRate: 0.65, clickRate: 0.25 },
      { name: "Follow-up", openRate: 0.7, clickRate: 0.3 },
    ];

    // Mock top engaging templates
    const topEngagingTemplates = [
      {
        name: "Welcome Email",
        engagementScore: 95,
        totalSent: 1200,
        openRate: 0.85,
        clickRate: 0.45,
        lastSent: "2024-01-15",
      },
      {
        name: "Newsletter",
        engagementScore: 88,
        totalSent: 3500,
        openRate: 0.75,
        clickRate: 0.35,
        lastSent: "2024-01-14",
      },
      {
        name: "Promotional",
        engagementScore: 82,
        totalSent: 2800,
        openRate: 0.65,
        clickRate: 0.25,
        lastSent: "2024-01-13",
      },
    ];

    // Calculate rates and trends
    const weeklyTrends = {
      emailsSent: calculateTrend(
        thisWeekMetrics.length,
        lastWeekMetrics.length
      ),
      openRate: calculateTrend(
        calculateRate(thisWeekMetrics, "opened"),
        calculateRate(lastWeekMetrics, "opened")
      ),
      clickRate: calculateTrend(
        calculateRate(thisWeekMetrics, "clicked"),
        calculateRate(lastWeekMetrics, "clicked")
      ),
      bounceRate: calculateTrend(
        calculateRate(thisWeekMetrics, "bounced"),
        calculateRate(lastWeekMetrics, "bounced")
      ),
    };

    const monthlyTrends = {
      emailsSent: 5.25,
      openRate: 2.3,
      clickRate: -1.45,
      bounceRate: -0.5,
    };

    return NextResponse.json({
      emailsSent: thisWeekMetrics.length,
      openRate: calculateRate(thisWeekMetrics, "opened"),
      clickRate: calculateRate(thisWeekMetrics, "clicked"),
      bounceRate: calculateRate(thisWeekMetrics, "bounced"),
      weeklyTrends,
      monthlyTrends,
      monthlyStats,
      templatePerformance,
      topEngagingTemplates,
    });
  } catch (error) {
    console.error("Failed to fetch email metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch email metrics" },
      { status: 500 }
    );
  }
}

function calculateRate(metrics, type) {
  if (!metrics.length) return 0;
  const count = metrics.filter((m) => m[type]).length;
  return count / metrics.length;
}

function calculateTrend(current, previous) {
  if (!previous) return 0;
  return Number((((current - previous) / previous) * 100).toFixed(2));
}
