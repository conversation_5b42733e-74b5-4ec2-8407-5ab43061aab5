import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

export async function GET(request, { params }) {
  try {
    const { id } = await params;
    console.log('Fetching overdue tasks for contact:', id);

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day

    // Get overdue tasks from the database
    const overdueTasks = await prisma.task.findMany({
      where: { 
        contactId: id,
        dueDate: {
          lt: today
        },
        completed: false
      },
      orderBy: { dueDate: 'asc' }
    });

    console.log('Found overdue tasks:', overdueTasks.length);

    return NextResponse.json(overdueTasks);
  } catch (error) {
    console.error('Failed to fetch overdue tasks:', error);
    return NextResponse.json(
      { error: `Failed to fetch overdue tasks: ${error.message}` },
      { status: 500 }
    );
  }
}
