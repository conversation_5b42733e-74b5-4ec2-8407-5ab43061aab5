"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
} from "chart.js";
import ContactsDistributionChart from "@/components/ui/charts/ContactsDistributionChart";
import PipelineDistributionChart from "@/components/ui/charts/PipelineDistributionChart";
import Tooltip from "@/components/ui/Tooltip";
import { InfoIcon } from "lucide-react";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend
);

export default function Dashboard() {
  const [metrics, setMetrics] = useState({
    contactStats: [],
    activitySummary: {
      today: 0,
      yesterday: 0,
      thisWeek: 0,
      lastWeek: 0,
      thisMonth: 0,
      lastMonth: 0,
      total: 0,
      weeklyTrend: 0,
      monthlyTrend: 0,
      activityTypes: [],
    },
    pipelineDistribution: [],
    recentActivities: [],
    upcomingTasks: [],
    overdueTasks: [],
    recentNotes: [],
    groupActivity: [],
    engagementMetrics: {},
    // upcomingMilestones: [],
    dormantContacts: [],
    dormantContactsTotal: 0,
    dormantContactsPercentage: 0,
    tasksDueToday: 0,
    tasksDueThisWeek: 0,
  });

  const [dormancyThreshold, setDormancyThreshold] = useState(30); // days

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const [
          contactStatsRes,
          activitiesRes,
          pipelineRes,
          tasksRes,
          overdueTasksRes,
          recentNotesRes,
          groupActivityRes,
          engagementRes,
          // milestonesRes,
          dormantRes,
        ] = await Promise.all([
          fetch("/api/metrics/contacts"),
          fetch("/api/metrics/activities/summary"),
          fetch("/api/metrics/pipeline"),
          fetch("/api/metrics/tasks"),
          fetch("/api/metrics/tasks/overdue"),
          fetch("/api/metrics/notes/recent"),
          fetch("/api/metrics/groups/activity"),
          fetch("/api/metrics/engagement"),
          // fetch("/api/metrics/milestones"),
          fetch(`/api/metrics/dormant?threshold=${dormancyThreshold}`),
        ]);

        const [
          contactStats,
          activities,
          pipeline,
          tasks,
          overdueTasks,
          recentNotes,
          groupActivity,
          engagement,
          // milestones,
          dormant,
        ] = await Promise.all([
          contactStatsRes.json(),
          activitiesRes.json(),
          pipelineRes.json(),
          tasksRes.json(),
          overdueTasksRes.json(),
          recentNotesRes.json(),
          groupActivityRes.json(),
          engagementRes.json(),
          // milestonesRes.json(),
          dormantRes.json(),
        ]);

        setMetrics({
          contactStats,
          activitySummary: activities,
          pipelineDistribution: pipeline,
          upcomingTasks: tasks.upcomingTasks || [],
          tasksDueToday: tasks.tasksDueToday || 0,
          tasksDueThisWeek: tasks.tasksDueThisWeek || 0,
          overdueTasks: overdueTasks || [],
          recentNotes: recentNotes || [],
          groupActivity: groupActivity || [],
          engagementMetrics: engagement || {},
          // upcomingMilestones: milestones || [],
          dormantContacts: dormant.dormantContacts || [],
          dormantContactsTotal: dormant.dormantContactsTotal || 0,
          dormantContactsPercentage: dormant.dormantContactsPercentage || 0,
        });
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      }
    };

    fetchDashboardData();
  }, [dormancyThreshold]);

  console.log({ metrics });

  return (
    <div className="space-y-4">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        <div className="card-light dark:bg-gray-800 p-3 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-base font-semibold">Contact Distribution</h3>
            <Tooltip content="Shows the distribution of contacts by type (client, prospective, etc.). Data is pulled from your actual contacts.">
              <InfoIcon className="h-4 w-4 text-gray-400" />
            </Tooltip>
          </div>
          <ContactsDistributionChart data={metrics.contactStats} />
        </div>

        <div className="card-light dark:bg-gray-800 p-3 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-base font-semibold">Activity Summary</h3>
            <Tooltip
              content={
                <div className="space-y-2">
                  <p>
                    Summary of your activities across different time periods.
                    Shows your activity volume and trends to help you track your
                    engagement levels.
                  </p>
                  <ul className="list-disc pl-4 space-y-1">
                    <li>
                      <strong>Today's Activities:</strong> Shows today's
                      activity count with comparison to yesterday
                    </li>
                    <li>
                      <strong>This Week's Activities:</strong> Shows this week's
                      activity count with percentage trend vs. last week
                    </li>
                    <li>
                      <strong>This Month's Activities:</strong> Shows this
                      month's activity count with percentage trend vs. last
                      month
                    </li>
                    <li>
                      <strong>Activity Types Breakdown:</strong> Shows the top 3
                      activity types with percentage bars
                    </li>
                  </ul>
                </div>
              }
            >
              {" "}
              <InfoIcon className="h-4 w-4 text-gray-400" />
            </Tooltip>
          </div>

          <div className="grid grid-cols-3 gap-2 mb-2">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg">
              <div className="text-xs text-blue-700 dark:text-blue-300 mb-1">
                Today
              </div>
              <div className="text-lg font-bold text-blue-800 dark:text-blue-200">
                {metrics.activitySummary.today}
              </div>
              <div className="text-xs text-blue-600 dark:text-blue-400">
                {metrics.activitySummary.yesterday > 0 ? (
                  <span>
                    {metrics.activitySummary.today >
                    metrics.activitySummary.yesterday
                      ? "+"
                      : ""}
                    {metrics.activitySummary.today -
                      metrics.activitySummary.yesterday}{" "}
                    vs yesterday
                  </span>
                ) : (
                  "No activities yesterday"
                )}
              </div>
            </div>

            <div className="bg-indigo-50 dark:bg-indigo-900/20 p-2 rounded-lg">
              <div className="text-xs text-indigo-700 dark:text-indigo-300 mb-1">
                This Week
              </div>
              <div className="text-lg font-bold text-indigo-800 dark:text-indigo-200">
                {metrics.activitySummary.thisWeek}
              </div>
              <div className="text-xs text-indigo-600 dark:text-indigo-400">
                <span
                  className={
                    metrics.activitySummary.weeklyTrend >= 0
                      ? "text-green-600 dark:text-green-400"
                      : "text-red-600 dark:text-red-400"
                  }
                >
                  {metrics.activitySummary.weeklyTrend > 0 ? "+" : ""}
                  {metrics.activitySummary.weeklyTrend}%
                </span>
                vs last week
              </div>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 p-2 rounded-lg">
              <div className="text-xs text-purple-700 dark:text-purple-300 mb-1">
                This Month
              </div>
              <div className="text-lg font-bold text-purple-800 dark:text-purple-200">
                {metrics.activitySummary.thisMonth}
              </div>
              <div className="text-xs text-purple-600 dark:text-purple-400">
                <span
                  className={
                    metrics.activitySummary.monthlyTrend >= 0
                      ? "text-green-600 dark:text-green-400"
                      : "text-red-600 dark:text-red-400"
                  }
                >
                  {metrics.activitySummary.monthlyTrend > 0 ? "+" : ""}
                  {metrics.activitySummary.monthlyTrend}%
                </span>
                vs last month
              </div>
            </div>
          </div>

          <div className="mt-2">
            <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">
              Activity Types
            </div>
            <div className="space-y-1">
              {metrics.activitySummary.activityTypes
                .slice(0, 3)
                .map((type, index) => (
                  <div key={index} className="flex items-center text-xs">
                    <div className="w-24 truncate">{type.type}</div>
                    <div className="flex-1 mx-2">
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                        <div
                          className="h-1.5 rounded-full bg-blue-500"
                          style={{ width: `${type.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-gray-500 dark:text-gray-400 w-8 text-right">
                      {type.percentage}%
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>

        <div className="card-light dark:bg-gray-800 p-3 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-base font-semibold">Pipeline Distribution</h3>
            <Tooltip content="Shows the distribution of contacts across pipeline stages. Reflects your actual pipeline data and helps track sales funnel progress.">
              <InfoIcon className="h-4 w-4 text-gray-400" />
            </Tooltip>
          </div>
          <PipelineDistributionChart data={metrics.pipelineDistribution} />
        </div>
      </div>

      {/* Engagement Metrics */}
      <div className="card-light dark:bg-gray-800 p-3 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <h3 className="text-base font-semibold">Engagement Metrics</h3>
          <Tooltip content="Overview of key engagement metrics. Active contacts are based on real data, other metrics are currently simulated. These metrics help you understand how effectively you're engaging with your contacts.">
            <InfoIcon className="h-4 w-4 text-gray-400" />
          </Tooltip>
        </div>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2">
          <div className="p-2 bg-teal-50 dark:bg-teal-900/30 rounded-lg">
            <div className="flex items-center space-x-1 mb-1">
              <h4 className="text-xs font-medium text-teal-800 dark:text-teal-300">
                Average Response Time
              </h4>
              <Tooltip content="Average time it takes you to respond to messages from contacts. Lower is better. (Currently using mock data)">
                <InfoIcon className="h-3 w-3 text-teal-400" />
              </Tooltip>
            </div>
            <div className="text-lg font-bold text-teal-600 dark:text-teal-400">
              {metrics.engagementMetrics.avgResponseTime || "N/A"}
            </div>
            <p className="text-xs text-teal-500 dark:text-teal-500 mt-1">
              {metrics.engagementMetrics.responseTimeTrend > 0 ? "↑" : "↓"}{" "}
              {Math.abs(metrics.engagementMetrics.responseTimeTrend || 0)}% from
              last month
            </p>
          </div>
          <div className="p-2 bg-indigo-50 dark:bg-indigo-900/30 rounded-lg">
            <div className="flex items-center space-x-1 mb-1">
              <h4 className="text-xs font-medium text-indigo-800 dark:text-indigo-300">
                Response Rate
              </h4>
              <Tooltip content="Percentage of messages that received a response. Higher is better. Helps track how consistently you follow up with contacts. (Currently using mock data)">
                <InfoIcon className="h-3 w-3 text-indigo-400" />
              </Tooltip>
            </div>
            <div className="text-lg font-bold text-indigo-600 dark:text-indigo-400">
              {metrics.engagementMetrics.responseRate || "N/A"}
            </div>
            <p className="text-xs text-indigo-500 dark:text-indigo-500 mt-1">
              {metrics.engagementMetrics.responseRateTrend > 0 ? "↑" : "↓"}{" "}
              {Math.abs(metrics.engagementMetrics.responseRateTrend || 0)}% from
              last month
            </p>
          </div>
          <div className="p-2 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
            <div className="flex items-center space-x-1 mb-1">
              <h4 className="text-xs font-medium text-purple-800 dark:text-purple-300">
                Active Contacts
              </h4>
              <Tooltip content="Number of contacts with any activity (notes, tasks, activities) in the last 30 days. Shows how many contacts you're actively engaging with. (Based on your actual data)">
                <InfoIcon className="h-3 w-3 text-purple-400" />
              </Tooltip>
            </div>
            <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
              {metrics.engagementMetrics.activeContactsCount || 0}
            </div>
            <p className="text-xs text-purple-500 dark:text-purple-500 mt-1">
              {metrics.engagementMetrics.activeContactsTrend > 0 ? "↑" : "↓"}{" "}
              {Math.abs(metrics.engagementMetrics.activeContactsTrend || 0)}%
              from last month
            </p>
          </div>
          <div className="p-2 bg-pink-50 dark:bg-pink-900/30 rounded-lg">
            <div className="flex items-center space-x-1 mb-1">
              <h4 className="text-xs font-medium text-pink-800 dark:text-pink-300">
                Conversion Rate
              </h4>
              <Tooltip content="Percentage of leads that converted to clients. Measures sales effectiveness. Higher is better. (Currently using mock data)">
                <InfoIcon className="h-3 w-3 text-pink-400" />
              </Tooltip>
            </div>
            <div className="text-lg font-bold text-pink-600 dark:text-pink-400">
              {metrics.engagementMetrics.conversionRate || "N/A"}
            </div>
            <p className="text-xs text-pink-500 dark:text-pink-500 mt-1">
              {metrics.engagementMetrics.conversionRateTrend > 0 ? "↑" : "↓"}{" "}
              {Math.abs(metrics.engagementMetrics.conversionRateTrend || 0)}%
              from last month
            </p>
          </div>
        </div>
      </div>

      {/* Tasks and Notes Cards in a Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {/* Upcoming Tasks */}
        <div className="card-light dark:bg-gray-800 p-3 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <h3 className="text-base font-semibold">Upcoming Tasks</h3>
              <Tooltip content="Tasks that are due soon. Shows the most urgent tasks that need your attention. All data is pulled from your actual tasks.">
                <InfoIcon className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <Link
              href="/tasks"
              className="text-blue-600 hover:text-blue-800 text-xs"
            >
              View All
            </Link>
          </div>

          <div className="mb-2">
            <div className="flex items-center justify-between text-xs">
              <Tooltip content="Tasks that need to be completed today. These are your highest priority.">
                <span className="text-gray-500 flex items-center">
                  <span className="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
                  Today: {metrics.tasksDueToday || 0}
                </span>
              </Tooltip>
              <Tooltip content="Tasks due within the next 7 days. Plan ahead to complete these on time.">
                <span className="text-gray-500 flex items-center">
                  <span className="w-2 h-2 rounded-full bg-blue-500 mr-1"></span>
                  This week: {metrics.tasksDueThisWeek || 0}
                </span>
              </Tooltip>
            </div>
          </div>

          <div className="space-y-1.5">
            {metrics.upcomingTasks.map((item) => (
              <div
                key={item.id}
                className={`flex items-center justify-between p-2 rounded-lg ${
                  item.dueDate && new Date(item.dueDate).toDateString() === new Date().toDateString()
                    ? "bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500"
                    : "bg-gray-50 dark:bg-gray-700"
                }`}
              >
                <div>
                  <div className="flex items-center">
                    <p className="font-medium text-sm">{item.title}</p>
                    {item.priority === "high" && (
                      <span className="ml-2 px-1.5 py-0.5 text-xs rounded-full bg-red-100 text-red-800">
                        High
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-gray-500">
                    For: {item.contact?.firstName} {item.contact?.lastName}
                  </p>
                </div>
                <div className="flex flex-col items-end">
                  <span className="text-xs text-gray-500">
                    {item.dueDate ? new Date(item.dueDate).toLocaleDateString() : "No due date"}
                  </span>
                  {item.dueDate && (
                    <span className="text-xs text-gray-400">
                      {Math.ceil(
                        (new Date(item.dueDate).getTime() - Date.now()) /
                          (1000 * 60 * 60 * 24)
                      )}{" "}
                      days left
                    </span>
                  )}
                </div>
              </div>
            ))}
            {metrics.upcomingTasks.length === 0 && (
              <div className="text-center p-2 text-gray-500 dark:text-gray-400 text-xs">
                No upcoming tasks
              </div>
            )}
          </div>
        </div>

        {/* Overdue Tasks */}
        <div className="card-light dark:bg-gray-800 p-3 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-base font-semibold">Overdue Tasks</h3>
            <Tooltip content="Tasks that are past their due date and not completed. These require immediate attention to maintain good client relationships.">
              <InfoIcon className="h-4 w-4 text-gray-400" />
            </Tooltip>
          </div>
          <div className="space-y-1.5">
            {metrics.overdueTasks.map((item) => (
              <div
                key={item.id}
                className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-900/30 rounded-lg border-l-4 border-red-500"
              >
                <div>
                  <p className="font-medium text-sm">{item.title}</p>
                  <p className="text-xs text-gray-500">
                    For: {item.contact?.firstName} {item.contact?.lastName}
                  </p>
                </div>
                <div className="flex flex-col items-end">
                  <span className="text-xs text-red-600 dark:text-red-400">
                    Due: {item.dueDate ? new Date(item.dueDate).toLocaleDateString() : "No due date"}
                  </span>
                  {item.dueDate && (
                    <span className="text-xs text-red-500 dark:text-red-300">
                      {Math.floor(
                        (Date.now() - new Date(item.dueDate).getTime()) /
                          (1000 * 60 * 60 * 24)
                      )}{" "}
                      days overdue
                    </span>
                  )}
                </div>
              </div>
            ))}
            {metrics.overdueTasks.length === 0 && (
              <div className="text-center p-2 text-gray-500 dark:text-gray-400 text-xs">
                No overdue tasks. Great job!
              </div>
            )}
          </div>
        </div>

        {/* Recent Notes */}
        <div className="card-light dark:bg-gray-800 p-3 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-base font-semibold">Recent Notes</h3>
            <Tooltip content="Most recent notes added across all contacts. Helps you stay updated on the latest information about your contacts.">
              <InfoIcon className="h-4 w-4 text-gray-400" />
            </Tooltip>
          </div>
          <div className="space-y-1.5">
            {metrics.recentNotes.map((note) => (
              <div
                key={note.id}
                className="p-2 bg-amber-50 dark:bg-amber-900/30 rounded-lg border-l-4 border-amber-400"
              >
                <div className="flex justify-between items-start">
                  <h4 className="font-medium text-sm">{note.title}</h4>
                  <span className="text-xs text-gray-500 ml-2">
                    {new Date(note.createdAt).toLocaleDateString()}
                  </span>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-2 mt-1">
                  {note.content}
                </p>
                <p className="text-xs text-amber-600 dark:text-amber-400 mt-1">
                  Contact: {note.contact.firstName} {note.contact.lastName}
                </p>
              </div>
            ))}
            {metrics.recentNotes.length === 0 && (
              <div className="text-center p-2 text-gray-500 dark:text-gray-400 text-xs">
                No recent notes
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Group Activity */}
      <div className="card-light dark:bg-gray-800 p-3 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <h3 className="text-base font-semibold">Group Activity</h3>
          <Tooltip content="Activity metrics for contact groups. Shows engagement levels for each group to help you identify which groups need more attention.">
            <InfoIcon className="h-4 w-4 text-gray-400" />
          </Tooltip>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2">
          {metrics.groupActivity && metrics.groupActivity.length > 0 ? (
            metrics.groupActivity.map((group) => (
              <div
                key={group.id}
                className="p-2 rounded-lg"
                style={{
                  backgroundColor: `${group.color}10`,
                  borderLeft: `4px solid ${group.color}`,
                }}
              >
                <div className="text-sm font-medium mb-1">{group.name}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                  {group.memberCount} contacts
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span>Engagement</span>
                    <span>{group.engagementScore}%</span>
                  </div>
                  <Tooltip
                    content={`${group.recentActivityCount} activities in the last 30 days`}
                  >
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                      <div
                        className="h-1.5 rounded-full"
                        style={{
                          width: `${group.engagementScore}%`,
                          backgroundColor: group.color,
                        }}
                      ></div>
                    </div>
                  </Tooltip>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-5 text-center p-2 text-gray-500 dark:text-gray-400 text-xs">
              No group activity data available
            </div>
          )}
        </div>
      </div>

      {/* Dormant Contacts Section */}
      <div className="card-light dark:bg-gray-800 p-3 rounded-lg">
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center space-x-2">
            <h3 className="text-base font-semibold">Dormant Contacts</h3>
            <Tooltip content="Contacts that haven't had any activity in the specified time period. These contacts may need follow-up to re-engage them. All data is pulled from your actual contact activity.">
              <InfoIcon className="h-4 w-4 text-gray-400" />
            </Tooltip>
          </div>
          <div className="flex items-center space-x-2">
            <label
              htmlFor="dormancyThreshold"
              className="text-xs text-gray-700 dark:text-gray-300"
            >
              Dormancy Threshold:
            </label>
            <select
              id="dormancyThreshold"
              value={dormancyThreshold}
              onChange={(e) => setDormancyThreshold(Number(e.target.value))}
              className="text-xs rounded-md border-gray-300 dark:border-gray-600 shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-blue-500 focus:ring-blue-500 py-1"
            >
              <option value={7}>7 days</option>
              <option value={14}>14 days</option>
              <option value={30}>30 days</option>
              <option value={60}>60 days</option>
              <option value={90}>90 days</option>
            </select>
          </div>
        </div>

        <div className="mb-3">
          <div className="flex items-center justify-between text-xs">
            <Tooltip content="Total number of contacts with no activity in the specified time period. Higher numbers may indicate a need for more proactive outreach.">
              <span className="text-gray-500 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3 w-3 mr-0.5 text-yellow-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Total dormant: {metrics.dormantContactsTotal || 0}
              </span>
            </Tooltip>
            <Tooltip content="Percentage of your total contacts that are dormant. A high percentage may indicate a need to review your engagement strategy.">
              <span className="text-gray-500 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3 w-3 mr-0.5 text-yellow-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"
                  />
                </svg>
                {metrics.dormantContactsPercentage || 0}% of all contacts
              </span>
            </Tooltip>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1.5">
            <div
              className="bg-yellow-500 h-1.5 rounded-full"
              style={{ width: `${metrics.dormantContactsPercentage || 0}%` }}
            ></div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-sm">
            <thead>
              <tr>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Contact
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Days Inactive
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {metrics.dormantContacts.map((contact) => (
                <tr
                  key={contact.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-750"
                >
                  <td className="px-3 py-2 whitespace-nowrap">
                    <Link
                      href={`/contacts/${contact.id}`}
                      className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                    >
                      {contact.firstName} {contact.lastName}
                    </Link>
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap">
                    <span
                      className={`px-1.5 py-0.5 text-xs rounded-full ${
                        contact.type === "prospective"
                          ? "bg-yellow-100 text-yellow-800"
                          : contact.type === "client"
                          ? "bg-green-100 text-green-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {contact.type}
                    </span>
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs">
                    {new Date(contact.lastContactDate).toLocaleDateString()}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap">
                    <Tooltip
                      content={`Last activity was ${Math.floor(
                        (Date.now() -
                          new Date(contact.lastContactDate).getTime()) /
                          (1000 * 60 * 60 * 24)
                      )} days ago. Consider reaching out soon.`}
                    >
                      <span className="text-yellow-600 dark:text-yellow-400 font-medium text-xs">
                        {Math.floor(
                          (Date.now() -
                            new Date(contact.lastContactDate).getTime()) /
                            (1000 * 60 * 60 * 24)
                        )}{" "}
                        days
                      </span>
                    </Tooltip>
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap">
                    <div className="flex space-x-2">
                      <Tooltip content="Send an email to re-engage this contact">
                        <Link
                          href={`/contacts/${contact.id}?tab=email`}
                          className="text-blue-600 hover:text-blue-800 text-xs flex items-center"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-3 w-3 mr-0.5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                            />
                          </svg>
                          Email
                        </Link>
                      </Tooltip>
                      <Tooltip content="Schedule a new activity with this contact">
                        <Link
                          href={`/contacts/${contact.id}?tab=activities&action=add`}
                          className="text-green-600 hover:text-green-800 text-xs flex items-center"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-3 w-3 mr-0.5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                          Schedule
                        </Link>
                      </Tooltip>
                    </div>
                  </td>
                </tr>
              ))}
              {metrics.dormantContacts.length === 0 && (
                <tr>
                  <td
                    colSpan="5"
                    className="px-3 py-2 text-center text-gray-500 dark:text-gray-400 text-xs"
                  >
                    No dormant contacts found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
