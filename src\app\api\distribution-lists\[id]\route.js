import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";

export async function GET(request, { params }) {
  let { id: listId } = await params;

  try {
    const list = await prisma.distributionList.findUnique({
      where: { id: listId },
      include: {
        contacts: true,
        groups: {
          include: {
            contacts: {
              include: {
                contact: true,
              },
            },
            // contacts: true,
            // members: {
            //   include: {
            //     contact: true,
            //   },
            // },
          },
        },
      },
    });

    // const transformedList = {
    //   ...list,
    //   groups: list.groups.map((group) => ({
    //     ...group,
    //     contacts: group.members,
    //   })),
    // };

    // console.log("The transformed list: ", transformedList);

    return NextResponse.json(list);
  } catch (error) {
    console.error("Error fetching distribution list:", error);
    return NextResponse.json(
      { error: "Failed to fetch distribution list" },
      { status: 500 }
    );
  }
}

export async function PATCH(request, { params }) {
  try {
    const { id: listId } = await params;
    const data = await request.json();

    // If only toggling favorite status, update just that field
    if (data.hasOwnProperty("isFavorite") && Object.keys(data).length === 1) {
      const list = await prisma.distributionList.update({
        where: { id: listId },
        data: { isFavorite: data.isFavorite },
      });
      return NextResponse.json(list);
    }
    // Check if this distribution list is currently being used in any campaigns
    const campaignsUsingList = await prisma.emailCampaign.findMany({
      where: {
        distributionListId: listId,
        status: {
          in: ["paused", "scheduled", "sending", "in-progress"],
        },
      },
    });
    if (campaignsUsingList.length > 0) {
      console.log(
        "Distribution list is being used in campaigns:",
        campaignsUsingList
      );
      return NextResponse.json(
        { error: "Distribution list is being used in campaigns", status: 409 },
        { status: 409 }
      );
    }

    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: "List name is required" },
        { status: 400 }
      );
    }

    // Update the distribution list
    const updatedList = await prisma.distributionList.update({
      where: { id: listId },
      data: {
        name: data.name,
        description: data.description,
        contacts: {
          set: [], // First disconnect all contacts
          connect: data.contactIds.map((id) => ({ id })), // Then connect the new ones
        },
        groups: {
          set: [], // First disconnect all groups
          connect: data.groups.map((group) => ({ id: group.id })), // Then connect the new ones
        },
      },
      include: {
        contacts: true,
      },
    });

    return NextResponse.json(updatedList);
  } catch (error) {
    console.error("Error updating distribution list:", error);
    return NextResponse.json(
      { error: "Failed to update distribution list" },
      { status: 500 }
    );
  }
}

export async function DELETE(request, { params }) {
  try {
    console.log(9);
    console.log(9);
    console.log(9);
    console.log(9);
    console.log(9);
    console.log(9);
    console.log(9);
    console.log(9);
    console.log(9);
    const { id: listId } = await params;

    // check if the list is being used in any campaigns

    const campaignsUsingList = await prisma.emailCampaign.findMany({
      where: {
        distributionListId: listId,
        status: {
          in: ["paused", "scheduled", "sending", "in-progress"],
        },
      },
    });

    console.log(1);
    console.log(1);
    console.log(1);
    console.log(1);
    console.log(1);
    console.log(1);
    console.log(1);
    console.log("CAMPAIGNS USING LIST: ", campaignsUsingList);

    if (campaignsUsingList.length > 0) {
      console.log(
        "Distribution list is being used in campaigns:",
        campaignsUsingList
      );

      console.log(8);
      console.log(8);
      console.log(8);
      console.log(8);
      console.log(8);
      console.log(8);
      console.log(8);
      console.log(8);
      return NextResponse.json(
        { error: "Distribution list is being used in campaigns", status: 409 },
        { status: 409 }
      );
    }

    console.log(7);
    console.log(7);
    console.log(7);
    console.log(7);
    console.log(7);
    console.log(7);
    console.log(7);

    await prisma.distributionList.delete({
      where: { id: listId },
    });

    // Fetch the updated list of distribution lists
    const lists = await prisma.distributionList.findMany({
      include: {
        contacts: true,
        _count: {
          select: { contacts: true },
        },
      },
    });

    return NextResponse.json(lists);
  } catch (error) {
    console.error("Error deleting distribution list:", error);
    return NextResponse.json(
      { error: "Failed to delete distribution list" },
      { status: 500 }
    );
  }
}
