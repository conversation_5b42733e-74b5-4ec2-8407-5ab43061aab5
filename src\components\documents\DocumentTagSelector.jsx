'use client';

import { useState, useEffect } from 'react';
import TagSelector from '@/components/ui/TagSelector';

const DocumentTagSelector = ({ value = [], onChange, disabled = false, className = '' }) => {
  // Ensure value is always an array
  const safeValue = Array.isArray(value) ? value : [];
  const [availableTags, setAvailableTags] = useState([]);
  const [loading, setLoading] = useState(false);

  // Safe onChange handler
  const handleChange = (newValue) => {
    if (onChange && typeof onChange === 'function') {
      onChange(Array.isArray(newValue) ? newValue : []);
    }
  };

  useEffect(() => {
    fetchDocumentTags();
  }, []);

  const fetchDocumentTags = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/documents/tags');

      if (!response.ok) {
        throw new Error(`Failed to fetch document tags: ${response.status}`);
      }

      const data = await response.json();
      const tags = data.tags || [];
      setAvailableTags(tags);
    } catch (error) {
      console.error('DocumentTagSelector: Error fetching document tags:', error);
      setAvailableTags([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={className}>
      <TagSelector
        value={safeValue}
        onChange={handleChange}
        availableTags={availableTags}
        loading={loading}
        disabled={disabled}
      />
    </div>
  );
};

export default DocumentTagSelector;
