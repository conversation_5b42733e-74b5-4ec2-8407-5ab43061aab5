/* Pipeline Drag and Drop Styles */
:root {
  --color-primary: #3b82f6; /* Blue-500 */
  --color-primary-rgb: 59, 130, 246;
  --color-border: #e5e7eb; /* Gray-200 */
  --color-border-dark: #4b5563; /* Gray-600 */
  --color-bg-hover: #f3f4f6; /* Gray-100 */
  --color-bg-hover-dark: #374151; /* Gray-700 */
  --color-bg-dragging: #eff6ff; /* Blue-50 */
  --color-bg-dragging-dark: rgba(59, 130, 246, 0.1); /* Blue-500 at 10% */
  --color-shadow: rgba(0, 0, 0, 0.1);
  --border-radius: 0.375rem;
  --transition-duration: 150ms;
}

/* Styles for the draggable item */
.pipeline-card-dragging {
  background-color: var(--color-bg-dragging) !important;
  border: 1px solid var(--color-primary) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  transform: translateZ(0) !important;
  z-index: 50 !important;
  opacity: 0.9;
}

/* Dark mode styles for the dragged item */
.dark .pipeline-card-dragging {
  background-color: var(--color-bg-dragging-dark) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
}

/* Drag handle styles */
.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: #f3f4f6; /* Gray-100 */
  color: #6b7280; /* Gray-500 */
  cursor: grab;
  transition: all var(--transition-duration);
}

.dark .drag-handle {
  background-color: #374151; /* Gray-700 */
  color: #9ca3af; /* Gray-400 */
}

.drag-handle:hover {
  background-color: #e5e7eb; /* Gray-200 */
  color: #4b5563; /* Gray-600 */
  transform: scale(1.1);
}

.dark .drag-handle:hover {
  background-color: #4b5563; /* Gray-600 */
  color: #d1d5db; /* Gray-300 */
}

.drag-handle:active {
  cursor: grabbing;
  transform: scale(0.95);
}

/* Make the drag handle icon look more like a proper drag handle */
.drag-handle svg {
  fill: currentColor;
  stroke-width: 1.5;
}

/* Dragging state */
body.is-dragging-contact {
  cursor: grabbing;
}

body.is-dragging-contact * {
  cursor: grabbing !important;
}

/* Drag preview */
.contact-drag-preview {
  padding: 8px 12px;
  background-color: var(--color-primary);
  color: white;
  border-radius: 4px;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Enhance the natural gap that appears during dragging */
[data-rbd-placeholder-context-id] {
  background-color: rgba(59, 130, 246, 0.15) !important;
  border: 2px dashed #3B82F6 !important;
  border-radius: 0.5rem !important;
  margin: 0.5rem 0 !important;
  opacity: 1 !important;
  height: 80px !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.1) !important;
}

/* Add a "Drop here" message to the gap */
[data-rbd-placeholder-context-id]::after {
  content: 'Drop contact here';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #3B82F6;
  font-weight: 500;
  font-size: 0.875rem;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Add a drop icon before the text */
[data-rbd-placeholder-context-id]::before {
  content: '';
  position: absolute;
  top: 50%;
  left: calc(50% - 4.5rem);
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%233B82F6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.9;
}

/* Dark mode gap styles */
.dark [data-rbd-placeholder-context-id] {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.6) !important;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.05) !important;
}

/* Dark mode styles for the gap text */
.dark [data-rbd-placeholder-context-id]::after {
  color: rgba(96, 165, 250, 0.9) !important;
}

/* Fix for the dragged item to ensure it's visible */
[data-rbd-draggable-id][data-rbd-dragging="true"] {
  opacity: 1 !important;
  pointer-events: auto !important;
  z-index: 9999 !important;
}

/* Additional styles for the dragged item */
.pipeline-card[data-is-dragging="true"] {
  background-color: #EFF6FF !important;
  border: 1px solid #BFDBFE !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  transform: scale(1.02) !important;
}

/* Dark mode styles for the dragged item */
.dark .pipeline-card[data-is-dragging="true"] {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

/* Column hover effect */
.pipeline-column {
  transition: all 0.2s ease-in-out;
}

.pipeline-column:hover {
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgba(59, 130, 246, 0.3);
}

.dark .pipeline-column:hover {
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.25), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.4);
}

/* Scrollbar styling */
.pipeline-scroll::-webkit-scrollbar {
  width: 6px;
}

.pipeline-scroll::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.5);
  border-radius: 4px;
  margin: 4px 0;
}

.pipeline-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

.pipeline-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.7);
}

/* Dark mode scrollbar */
.dark .pipeline-scroll::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
}

.dark .pipeline-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark .pipeline-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.7);
}

/* Hide scrollbar when not hovering */
.pipeline-scroll {
  scrollbar-width: thin;
}

.pipeline-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
}

.pipeline-column:hover .pipeline-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
}

.dark .pipeline-column:hover .pipeline-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}
