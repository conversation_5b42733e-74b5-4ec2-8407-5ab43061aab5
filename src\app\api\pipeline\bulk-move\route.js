import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

export async function POST(request) {
  try {
    const { contactIds, targetStageId } = await request.json();
    
    if (!contactIds || !Array.isArray(contactIds) || contactIds.length === 0) {
      return NextResponse.json(
        { error: 'Invalid contact IDs provided' },
        { status: 400 }
      );
    }
    
    // Handle "unassigned" special case
    const updateData = targetStageId === 'unassigned' 
      ? { pipelineStage: null }
      : { pipelineStage: targetStageId };
    
    // Update all contacts in a single transaction
    const result = await prisma.contact.updateMany({
      where: {
        id: {
          in: contactIds
        }
      },
      data: updateData
    });
    
    return NextResponse.json({
      success: true,
      updatedCount: result.count
    });
  } catch (error) {
    console.error('Failed to move contacts in bulk:', error);
    return NextResponse.json(
      { error: 'Failed to move contacts in bulk' },
      { status: 500 }
    );
  }
}
