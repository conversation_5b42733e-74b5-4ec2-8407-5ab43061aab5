import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

// GET /api/checklists/[id] - Get a specific checklist
export async function GET(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const checklistId = params.id;
    
    const checklist = await prisma.checklist.findUnique({
      where: { id: checklistId },
      include: {
        items: {
          orderBy: {
            order: 'asc'
          }
        }
      }
    });
    
    if (!checklist) {
      return NextResponse.json(
        { error: 'Checklist not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(checklist);
  } catch (error) {
    console.error('Failed to fetch checklist:', error);
    return NextResponse.json(
      { error: 'Failed to fetch checklist: ' + error.message },
      { status: 500 }
    );
  }
}

// PATCH /api/checklists/[id] - Update a checklist
export async function PATCH(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const checklistId = params.id;
    
    const body = await request.json();
    
    // Update the checklist
    const updatedChecklist = await prisma.checklist.update({
      where: { id: checklistId },
      data: {
        ...(body.title !== undefined && { title: body.title }),
        ...(body.description !== undefined && { description: body.description }),
        ...(body.category !== undefined && { category: body.category }),
        ...(body.status !== undefined && { status: body.status }),
        updatedAt: new Date()
      },
      include: {
        items: {
          orderBy: {
            order: 'asc'
          }
        }
      }
    });
    
    return NextResponse.json(updatedChecklist);
  } catch (error) {
    console.error('Failed to update checklist:', error);
    return NextResponse.json(
      { error: 'Failed to update checklist: ' + error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/checklists/[id] - Delete a checklist
export async function DELETE(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const checklistId = params.id;
    
    // Delete the checklist (items will be deleted automatically due to onDelete: Cascade)
    await prisma.checklist.delete({
      where: { id: checklistId }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete checklist:', error);
    return NextResponse.json(
      { error: 'Failed to delete checklist: ' + error.message },
      { status: 500 }
    );
  }
}
