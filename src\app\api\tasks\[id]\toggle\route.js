import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

// Toggle task completion status
export async function PATCH(request, { params }) {
  try {
    // Get the current task
    const { id } = await params;
    const task = await prisma.task.findUnique({
      where: { id }
    });

    if (!task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    // Toggle the completed status
    const newCompletedStatus = !task.completed;

    // Create a data object with only the basic fields we're updating
    const updateData = {
      completed: newCompletedStatus,
    };

    // Check if the schema has been updated with the new fields
    const hasNewFields = Object.keys(task).includes('completedAt');

    // Only add the new fields if they exist in the schema
    if (hasNewFields) {
      console.log('Schema has new fields, adding completedAt and updatedAt');
      // Set completedAt to current date if completed, or null if uncompleted
      updateData.completedAt = newCompletedStatus ? new Date() : null;
      updateData.updatedAt = new Date();
    } else {
      console.log('Schema does not have new fields, skipping completedAt and updatedAt');
    }

    console.log('Updating task with data:', updateData);

    const updatedTask = await prisma.task.update({
      where: { id },
      data: updateData
    });

    return NextResponse.json(updatedTask);
  } catch (error) {
    console.error('Failed to toggle task completion:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: `Failed to toggle task completion: ${error.message}` },
      { status: 500 }
    );
  }
}
