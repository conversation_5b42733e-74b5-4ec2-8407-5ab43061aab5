
import { useMemo, useState } from "react";
import InsuranceProcessDiagram from "./InsuranceProcessDiagram";
import { saveAs } from "file-saver";
import { toast } from "react-hot-toast";
import { CONTACT_FIELDS, getFieldById } from '@/lib/constants/contactFields';


// Insurance process stages and their corresponding contact field IDs for dates
const INSURANCE_STAGES = [
  {
    key: "gathering_client_info",
    label: "Gathering Client Info",
    dateField: null, // No date for this stage
  },
  {
    key: "submitted_client_info",
    label: "Client Info Submitted",
    dateField: "insuranceClientInfoSubmittedDate",
  },
  {
    key: "received_quote",
    label: "Quote Received",
    dateField: "insuranceQuoteReceivedDate",
  },
  {
    key: "sent_questionnaire",
    label: "Questionnaire Sent",
    dateField: "insuranceQuestionnaireSentToClientDate",
  },
  {
    key: "submitted_to_carrier",
    label: "Documents Submitted to Carrier",
    dateField: "insuranceDocumentsSubmittedToCarrierDate",
  },
  {
    key: "completed",
    label: "Completed",
    dateField: "insuranceCompletionDate",
  },
];

const STATUS_TO_STAGE = {
  "gathering client info": "gathering_client_info",
  "submitted client info": "submitted_client_info",
  "received quote": "received_quote",
  "sent questionnaire to client": "sent_questionnaire",
  "sent questionnaire": "sent_questionnaire",
  "submitted to carrier": "submitted_to_carrier",
  "completed": "completed",
  "paused": null,
  "canceled": null,
};


function formatDate(date) {
  if (!date) return "-";
  const d = new Date(date);
  if (isNaN(d)) return "-";
  return d.toLocaleDateString();
}


export default function InsuranceTab({ contact }) {
  // Insurance info fields (from DB, see constants/contactFields.js)
  // Order: Insurance Type, Estimated Annual Premium Budget, then the rest (excluding insuranceStatus)
  const insuranceInfoFieldIds = [
    'insuranceType',
    'insuranceEstimatedAnnualPremiumBudget',
    'firstName',
    'lastName',
    'dateOfBirth',
    'gender',
    'maritalStatus',
    'addressLine1',
    'addressLine2',
    'city',
    'state',
    'postalCode',
    'country',
    'company',
    'jobTitle',
    'weightLb',
    'heightInches',
    'netWorth',
    'annualIncome',
    'occupation',
  ];

  // For export, use the same fields as in the grid
  const insuranceInfoFields = insuranceInfoFieldIds
    .map(id => getFieldById(id))
    .filter(Boolean);

  // Export menu state
  const [exportOpen, setExportOpen] = useState(false);

  // For export
  const exportFields = insuranceInfoFields.map(field => [field.label, field.render(contact)]);


  function handleExport(type) {
    if (type === 'csv') {
      const csv = exportFields.map(row => row.map(String).join(",")).join("\n");
      const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
      saveAs(blob, `insurance-info-${contact.id}.csv`);
      toast.success("Exported as CSV");
    } else if (type === 'pdf') {
      import("jspdf").then(jsPDF => {
        const doc = new jsPDF.jsPDF();
        exportFields.forEach(([label, value], i) => {
          doc.text(`${label}: ${value ?? "-"}`, 10, 10 + i * 10);
        });
        doc.save(`insurance-info-${contact.id}.pdf`);
        toast.success("Exported as PDF");
      });
    }
    setExportOpen(false);
  }


  // Determine current stage
  const status = contact.insuranceStatus || '';
  const currentStageKey = STATUS_TO_STAGE[status.toLowerCase()] || null;

  // Map stage keys to dates (from contact fields)
  const stageDates = {};
  INSURANCE_STAGES.forEach(stage => {
    stageDates[stage.key] = stage.dateField ? contact[stage.dateField] : null;
  });

  // Determine completed stages
  const completedStages = useMemo(() => {
    const stages = [];
    for (const stage of INSURANCE_STAGES) {
      if (stage.key === currentStageKey) break;
      stages.push(stage.key);
    }
    if (currentStageKey) stages.push(currentStageKey);
    return stages;
  }, [currentStageKey]);


  // --- Render ---
  return (
    <div className="p-4 space-y-6">
      {/* Insurance Info Table/Grid (2-row grid, no insuranceStatus, export button styled like ExportMenu) */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Insurance Information</h2>
          {/* Export button styled like ExportMenu */}
          <div className="relative">
            <button
              onClick={() => setExportOpen((v) => !v)}
              disabled={false}
              title="Export insurance info to CSV or PDF"
              className={`px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 flex items-center text-sm min-w-[100px] justify-center`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            {exportOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 border border-gray-200 dark:border-gray-700">
                <div className="py-1">
                  <button
                    onClick={() => handleExport('csv')}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Export to CSV
                  </button>
                  <button
                    onClick={() => handleExport('pdf')}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    Export to PDF
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
        {/* 2-row grid: each cell is label + value, wrap to next row after half the fields */}
        <div className="overflow-x-auto">
          <table className="min-w-full border border-gray-300 dark:border-gray-700 rounded">
            <tbody>
              <tr>
                {insuranceInfoFields.slice(0, Math.ceil(insuranceInfoFields.length / 2)).map(field => (
                  <td key={field.id} className="align-top w-1/5 border border-gray-200 dark:border-gray-700 px-4 py-3 text-center">
                    <div className="text-xs font-bold text-blue-700 dark:text-blue-300 tracking-wide uppercase mb-1 text-center">{field.label}</div>
                    <div className="text-base font-normal text-gray-900 dark:text-white whitespace-pre-line text-center">{field.render(contact)}</div>
                  </td>
                ))}
              </tr>
              <tr>
                {insuranceInfoFields.slice(Math.ceil(insuranceInfoFields.length / 2)).map(field => (
                  <td key={field.id} className="align-top w-1/5 border border-gray-200 dark:border-gray-700 px-4 py-3 text-center">
                    <div className="text-xs font-bold text-blue-700 dark:text-blue-300 tracking-wide uppercase mb-1 text-center">{field.label}</div>
                    <div className="text-base font-normal text-gray-900 dark:text-white whitespace-pre-line text-center">{field.render(contact)}</div>
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Insurance Process & Details Card */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-2">
          <div className="flex flex-col md:flex-row md:items-center gap-2">
            <span className="text-lg font-semibold">Insurance Type:</span>
            <span className="font-medium text-primary">{contact.insuranceType || '-'}</span>
          </div>
          <div className="flex flex-col md:flex-row md:items-center gap-2">
            <span className="text-lg font-semibold">Status:</span>
            <span className="font-medium text-primary">{contact.insuranceStatus || '-'}</span>
          </div>
          <div className="flex flex-col md:flex-row md:items-center gap-2">
            <span className="text-lg font-semibold">Estimated Annual Premium Budget:</span>
            <span className="font-medium text-primary">{contact.insuranceEstimatedAnnualPremiumBudget || '-'}</span>
          </div>
        </div>

        {/* Insurance Process Diagram - fatter pills, label+date inside, chevrons, no dates below */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Insurance Process</h3>
          <InsuranceProcessDiagram contact={contact} />
        </div>
      </div>
    </div>
  );
}
