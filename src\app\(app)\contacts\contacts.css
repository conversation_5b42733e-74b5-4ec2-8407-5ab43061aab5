/* Vertical text for collapsed filters panel */
.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  transform: rotate(180deg);
  white-space: nowrap;
  letter-spacing: 1px;
  padding: 10px 0;
}

/* CSS Variables for theme colors */
:root {
  --border-color: #d1d5db;
}

.dark {
  --border-color: #4b5563;
}

/* Table styles */
.table-row-border {
  border-bottom: 1px solid var(--border-color);
}

/* Always show scrollbar */
.show-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.show-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.show-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.show-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 20px;
}

.dark .show-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}
