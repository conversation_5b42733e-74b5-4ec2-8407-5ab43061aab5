import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";
import { auditLogger } from "@/lib/services/auditLogger";

export async function GET(request, { params }) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);
    const { id } = await params;
    // First verify the contact exists and is accessible
    let whereClause = { id: String(id) };
    if (user.role !== "admin") {
      whereClause.userId = String(user.id);
    }
    const contact = await prisma.contact.findFirst({
      where: whereClause,
    });

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    const notes = await prisma.note.findMany({
      where: { contactId: id },
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(notes);
  } catch (error) {
    console.error("Failed to fetch notes:", error);
    return NextResponse.json(
      { error: "Failed to fetch notes" },
      { status: 500 }
    );
  }
}

export async function POST(request, { params }) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);

    const { id } = await params;
    let whereClause = { id: String(id) };

    if (user.role !== "admin") {
      whereClause.userId = String(user.id);
    }

    const contact = await prisma.contact.findFirst({
      where: whereClause,
    });


    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    const body = await request.json();

    // Ensure required fields are present
    if (!body.title) {
      return NextResponse.json({ error: "Title is required" }, { status: 400 });
    }

    if (!body.content) {
      return NextResponse.json(
        { error: "Content is required" },
        { status: 400 }
      );
    }

    const note = await prisma.note.create({
      data: {
        title: body.title,
        content: body.content,
        contactId: id,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // Log the note creation
    await auditLogger.logNoteCreate({
      userId: user.id,
      note,
      request
    });

    return NextResponse.json(note);
  } catch (error) {
    console.error("Failed to create note:", error);
    return NextResponse.json(
      { error: "Failed to create note: " + error.message },
      { status: 500 }
    );
  }
}
