import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { JSDOM } from "jsdom";
import { getSignedInUser } from "@/lib/auth";
import { auditLogger } from "@/lib/services/auditLogger";

export async function GET(request, { params }) {
  try {
    const { id: templateId } = await params;
    const template = await prisma.emailTemplate.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(template);
  } catch (error) {
    console.error("Failed to fetch template:", error);
    return NextResponse.json(
      { error: "Failed to fetch template" },
      { status: 500 }
    );
  }
}

export async function PATCH(request, { params }) {
  try {
    const { id: templateId } = await params;
    const data = await request.json();

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Get the existing template for audit log
    const existingTemplate = await prisma.emailTemplate.findUnique({
      where: { id: templateId }
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    // If only toggling favorite status, update just that field
    if (data.hasOwnProperty("isFavorite") && Object.keys(data).length === 1) {
      const template = await prisma.emailTemplate.update({
        where: { id: templateId },
        data: { isFavorite: data.isFavorite },
      });

      // Log the template update
      await auditLogger.logEmailTemplateUpdate({
        userId: user?.id,
        templateId,
        oldValues: existingTemplate,
        newValues: template,
        request
      });

      return NextResponse.json(template);
    }

    // Check if this email template is currently being used in any campaigns
    const campaignsUsingTemplate = await prisma.emailCampaign.findMany({
      where: {
        templateId: templateId,
        status: {
          in: ["paused", "scheduled", "sending", "in-progress"],
        },
      },
    });

    if (campaignsUsingTemplate.length > 0) {
      console.log(9);
      console.log(9);
      console.log(9);
      console.log(9);
      console.log(9);
      console.log(9);
      console.log(9);
      console.log("templte is being used in campaigns");
      console.log("The campaigns: ", campaignsUsingTemplate);

      return NextResponse.json(
        { error: "Template is being used in campaigns", status: 409 },
        { status: 409 }
      );
    }

    // Otherwise, handle full template update
    const template = await prisma.emailTemplate.update({
      where: { id: templateId },
      data: {
        ...data,
        content: data.content,
      },
    });

    // Log the template update
    await auditLogger.logEmailTemplateUpdate({
      userId: user?.id,
      templateId,
      oldValues: existingTemplate,
      newValues: template,
      request
    });

    return NextResponse.json(template);
  } catch (error) {
    console.error("Failed to update template:", error);
    return NextResponse.json(
      { error: "Failed to update template" },
      { status: 500 }
    );
  }
}

export async function DELETE(request, { params }) {
  try {
    const { id: templateId } = await params;
    console.log("Deleting template with ID:", templateId);

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Get the existing template for audit log
    const existingTemplate = await prisma.emailTemplate.findUnique({
      where: { id: templateId }
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    // check if the template is being used in any campaigns
    const campaignsUsingTemplate = await prisma.emailCampaign.findMany({
      where: {
        templateId: templateId,
        status: {
          in: ["paused", "scheduled", "sending", "in-progress"],
        },
      },
    });
    // check if there are any email sequence steps using this template
    const emailSequenceStepsUsingTemplate =
      await prisma.emailSequenceStep.findMany({
        where: {
          templateId: templateId,
        },
      });
    if (emailSequenceStepsUsingTemplate.length > 0) {
      console.log(
        "Template is being used in email sequence steps:",
        emailSequenceStepsUsingTemplate
      );
      return NextResponse.json(
        { error: "Template is being used in email sequence steps" },
        { status: 409 }
      );
    }

    if (campaignsUsingTemplate.length > 0) {
      console.log(
        "Template is being used in campaigns:",
        campaignsUsingTemplate
      );
      return NextResponse.json(
        { error: "Template is being used in campaigns", status: 409 },
        { status: 409 }
      );
    }
    console.log("No campaigns using this template, proceeding with deletion!");

    // Log the template deletion
    await auditLogger.logEmailTemplateDelete({
      userId: user?.id,
      templateId,
      oldValues: existingTemplate,
      request
    });

    await prisma.emailTemplate.delete({
      where: { id: templateId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete template:", error);
    return NextResponse.json(
      { error: "Failed to delete template" },
      { status: 500 }
    );
  }
}
