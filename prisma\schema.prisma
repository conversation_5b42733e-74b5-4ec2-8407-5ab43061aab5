generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model User {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  email       String    @unique
  name        String?
  azureId     String    @unique
  session     Session[]
  role        String    @default("user") // Possible values: "user", "admin", "manager"
  permissions String[] // Array of permission strings like "read:contacts", "write:contacts", etc.
  // campaigns          EmailCampaign[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  dialogFieldPreferences DialogFieldPreferences[]
  contacts               Contact[]
  contactGroups          ContactGroup[]
  workflows              Workflow[]
  emailTemplates         EmailTemplate[]
  auditLogs              AuditLog[]
  stageHistoryEntries    ContactStageHistory[]
  lifecycleMetricsEntries ContactLifecycleMetrics[]
}

model DialogFieldPreferences {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  userId        String   @db.ObjectId
  user          User     @relation(fields: [userId], references: [id])
  dialogKey     String // e.g. "contact-add-edit"
  visibleFields String[] // Array of field IDs (string)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@unique([userId, dialogKey])
  @@index([userId])
}

model Session {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionID   String   @unique
  userId      String   @db.ObjectId
  user        User     @relation(fields: [userId], references: [id])
  accessToken String? // Microsoft access token
  data        String?  @db.String // JSON string for additional session data
  expiresAt   DateTime
  createdAt   DateTime @default(now())
}

model Contact {
  id     String @id @default(auto()) @map("_id") @db.ObjectId
  userId String @db.ObjectId
  user   User   @relation(fields: [userId], references: [id])

  // --- Essential ---
  firstName     String
  middleName    String?
  lastName      String
  preferredName String?
  email         String?
  phone         String?
  type          String   @default("prospective")
  tags          String[]
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // --- Contact Info ---
  addressLine1 String?
  addressLine2 String?
  city         String?
  state        String?
  postalCode   String?
  country      String?
  company      String?
  jobTitle     String?
  department   String?
  linkedinUrl  String?
  website      String?

  // --- Personal ---
  gender            String?
  dateOfBirth       DateTime?
  maritalStatus     String?
  spouseName        String?
  childrenNames     String[]
  preferredLanguage String?
  timeZone          String?

  // --- CRM ---
  pipelineStage            String?
  lastContactDate          DateTime?
  notesInternal            String?
  communicationPreferences String?
  preferredContactMethod   String?
  doNotContact             Boolean?
  leadSource               String?
  leadScore                Int?
  lastActivityDate         DateTime?
  referredBy               String?
  referralSource           String?
  marketingSource          String?

  // --- Investment Advisory ---
  clientType            String?
  clientStatus          String?
  riskTolerance         String?
  investmentObjective   String[]
  investmentExperience  String?
  annualIncome          Float?
  netWorth              Float?
  liquidNetWorth        Float?
  sourceOfFunds         String?
  employmentStatus      String?
  employerName          String?
  employerAddress       String?
  occupation            String?
  yearsWithEmployer     Int?
  taxBracket            String?
  citizenship           String?
  ssnOrTaxId            String?
  regulatoryStatus      String?
  complianceFlags       String[]
  accountNumbers        String[]
  custodian             String?
  accountType           String?
  accountOpenDate       DateTime?
  accountCloseDate      DateTime?
  accountStatus         String?
  beneficiaryNames      String[]
  trustedContactName    String?
  trustedContactPhone   String?
  trustedContactEmail   String?
  estatePlanOnFile      Boolean?
  attorneyName          String?
  attorneyEmail         String?
  cpaName               String?
  cpaEmail              String?
  financialAdvisorName  String?
  financialAdvisorEmail String?

  // --- Compliance ---
  privacyPolicySent       Boolean?
  privacyPolicyDate       DateTime?
  formAdvSent             Boolean?
  formAdvDate             DateTime?
  schwabEnvelopeStatus    String?
  schwabEnvelopeDate      DateTime?
  advisoryAgreementStatus String?
  advisoryAgreementDate   DateTime?
  governmentIssuedIdType  String?
  governmentIssuedId      String?

  onboardingStatus String?
  onboardingDate   DateTime?
  lastReviewDate   DateTime?
  nextReviewDate   DateTime?

  // --- Insurance ---
  weightLb                                 Float?
  heightInches                             Float?
  insuranceType                            String?
  insuranceEstimatedAnnualPremiumBudget    Float?
  insuranceClientInfoSubmittedDate         DateTime?
  insuranceQuoteReceivedDate               DateTime?
  insuranceQuestionnaireSentToClientDate   DateTime?
  insuranceDocumentsSubmittedToCarrierDate DateTime?
  insuranceCompletionDate                  DateTime?
  insuranceStatus                          String?

  // --- Existing Relations ---
  activities           Activity[]
  tasklists            Tasklist[]
  notes                Note[]
  documents            Document[]
  groups               ContactGroupMembership[]
  distributionLists    DistributionList[]       @relation(fields: [distributionListIds], references: [id])
  distributionListIds  String[]                 @db.ObjectId
  emailSequenceSteps   EmailSequenceStep[]      @relation(fields: [emailSequenceStepIds], references: [id])
  emailSequenceStepIds String[]                 @db.ObjectId

  // --- New Analytics Relations ---
  stageHistory         ContactStageHistory[]
  lifecycleMetrics     ContactLifecycleMetrics?
}

model ActivityType {
  id          String     @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  icon        String
  color       String
  description String?
  isSystem    Boolean    @default(false) // True for Email, Note, Task
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  activities  Activity[]
}

model Activity {
  id           String        @id @default(auto()) @map("_id") @db.ObjectId
  type         String // This will store the ActivityType id
  description  String
  date         DateTime      @default(now())
  createdAt    DateTime      @default(now())
  contact      Contact       @relation(fields: [contactId], references: [id], onDelete: Cascade)
  contactId    String        @db.ObjectId
  activityType ActivityType? @relation(fields: [type], references: [id])
}

model Tasklist {
  id          String         @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  status      String         @default("in_progress") // in_progress, completed, archived
  items       TasklistItem[]
  contact     Contact        @relation(fields: [contactId], references: [id], onDelete: Cascade)
  contactId   String         @db.ObjectId
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  @@index([contactId])
}

model TasklistItem {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  dueDate     DateTime?
  priority    String    @default("medium")
  completed   Boolean   @default(false)
  completedAt DateTime?
  order       Int       @default(0)
  tasklist    Tasklist  @relation(fields: [tasklistId], references: [id], onDelete: Cascade)
  tasklistId  String    @db.ObjectId
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([tasklistId])
}

model Note {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  title     String
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  contact   Contact  @relation(fields: [contactId], references: [id], onDelete: Cascade)
  contactId String   @db.ObjectId
}

model Document {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  name       String
  fileType   String // pdf, docx, xlsx, etc.
  fileSize   String // Formatted size (e.g., "2.4 MB")
  tags       String[]
  uploadedAt DateTime @default(now())
  uploadedBy String? // User who uploaded the document
  contact    Contact  @relation(fields: [contactId], references: [id], onDelete: Cascade)
  contactId  String   @db.ObjectId
  source     String   @default("local") // local, onedrive, sharepoint
  folder     String? // Folder path in the source
  url        String? // URL to access the document
  externalId String? // ID in the external system (OneDrive, SharePoint)
  driveId    String? // Drive ID for SharePoint documents

  @@index([contactId])
  @@index([source])
}

model Tag {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  name      String
  type      String // Contact, Document, Email
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([name, type])
  @@index([type])
}

model PipelineStage {
  id           String                @id @default(auto()) @map("_id") @db.ObjectId
  name         String
  order        Int
  actions      StageAction[]
  stageHistory ContactStageHistory[]
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
}

model StageAction {
  id            String        @id @default(auto()) @map("_id") @db.ObjectId
  actionType    String // activity, task, note, email
  actionDetails Json // Stores the details of the action (type, title, description, etc.)
  stage         PipelineStage @relation(fields: [stageId], references: [id], onDelete: Cascade)
  stageId       String        @db.ObjectId
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@index([stageId])
}

// model ContactGroup {
//   id          String   @id @default(auto()) @map("_id") @db.ObjectId
//   name        String
//   description String?
//   color       String?  // For UI display purposes
//   userId      String   @db.ObjectId
//   user        User     @relation(fields: [userId], references: [id])
//   members     ContactGroupMembership[]
//   distributionLists DistributionList[]
//   createdAt   DateTime @default(now())
//   updatedAt   DateTime @updatedAt

//   @@index([userId])
// }

model ContactGroup {
  id                  String                   @id @default(auto()) @map("_id") @db.ObjectId
  name                String
  description         String?
  color               String? // For UI display purposes
  userId              String                   @db.ObjectId
  user                User                     @relation(fields: [userId], references: [id])
  contacts            ContactGroupMembership[]
  // members     ContactGroupMembership[]
  distributionLists   DistributionList[]       @relation(fields: [distributionListIds], references: [id])
  distributionListIds String[]                 @db.ObjectId
  createdAt           DateTime                 @default(now())
  updatedAt           DateTime                 @updatedAt

  @@index([userId])
}

model ContactGroupMembership {
  id        String       @id @default(auto()) @map("_id") @db.ObjectId
  group     ContactGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  groupId   String       @db.ObjectId
  contact   Contact      @relation(fields: [contactId], references: [id], onDelete: Cascade)
  contactId String       @db.ObjectId
  addedAt   DateTime     @default(now())

  @@unique([groupId, contactId])
  @@index([groupId])
  @@index([contactId])
}

// model DistributionList {
//   id          String    @id @default(auto()) @map("_id") @db.ObjectId
//   name        String
//   description String?
//   userId      String    @db.ObjectId
//   contacts    Contact[] @relation(fields: [contactIds], references: [id])
//   contactIds  String[]  @db.ObjectId
//   groups      ContactGroup[]  // Remove the @relation and fields/references
//   // groupIds    String[]  @db.ObjectId
//   createdAt   DateTime  @default(now())
//   updatedAt   DateTime  @updatedAt
// }

model EmailTemplate {
  id             String              @id @default(auto()) @map("_id") @db.ObjectId
  userId         String              @db.ObjectId
  user           User                @relation(fields: [userId], references: [id])
  name           String
  description    String?
  subject        String
  content        String              @db.String // HTML content with embedded base64 images
  isFavorite     Boolean             @default(false)
  isGlobal       Boolean             @default(false) // Whether the template is available to all users
  isActiveGlobal Boolean             @default(false) // Whether the global template is active
  campaigns      EmailCampaign[]
  sequenceSteps  EmailSequenceStep[] // Added this line for the relation
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
}

model DistributionList {
  id          String          @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  userId      String          @db.ObjectId
  contacts    Contact[]       @relation(fields: [contactIds], references: [id])
  contactIds  String[]        @db.ObjectId
  groups      ContactGroup[]  @relation(fields: [groupIds], references: [id])
  groupIds    String[]        @db.ObjectId
  campaigns   EmailCampaign[]
  isFavorite  Boolean         @default(false)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
}

model EmailCampaign {
  id                 String           @id @default(auto()) @map("_id") @db.ObjectId
  name               String
  description        String
  tags               String[]
  sequence           EmailSequence?
  status             String // Scheduled, Sending, In Progress (automated drip), Paused, Completed, Failed, Cancelled
  campaignType       String // one-time or drip
  sendType           String // immediate or scheduled
  template           EmailTemplate    @relation(fields: [templateId], references: [id])
  templateId         String           @db.ObjectId
  distributionList   DistributionList @relation(fields: [distributionListId], references: [id])
  distributionListId String           @db.ObjectId
  userId             String           @db.ObjectId
  scheduledAt        DateTime?
  scheduledDate      DateTime?
  scheduledTime      String?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt

  @@index([userId])
  @@index([templateId])
  @@index([distributionListId])
}

model EmailSequenceStep {
  id                   String        @id @default(auto()) @map("_id") @db.ObjectId
  sequence             EmailSequence @relation(fields: [sequenceId], references: [id], onDelete: Cascade)
  sequenceId           String        @db.ObjectId
  order                Int
  delayDays            Int
  delayHours           Int
  scheduledDate        DateTime?
  scheduledTime        String?
  completed            Boolean       @default(false)
  template             EmailTemplate @relation(fields: [templateId], references: [id])
  templateId           String        @db.ObjectId
  condition            String?
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt
  error                String?
  filteredRecipients   Contact[]     @relation(fields: [filteredRecipientIds], references: [id])
  filteredRecipientIds String[]      @db.ObjectId

  @@index([sequenceId])
  @@index([templateId])
}

model EmailSequence {
  id         String              @id @default(auto()) @map("_id") @db.ObjectId
  name       String
  userId     String              @db.ObjectId
  status     String
  steps      EmailSequenceStep[]
  campaign   EmailCampaign       @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  campaignId String              @unique @db.ObjectId
  createdAt  DateTime            @default(now())
  updatedAt  DateTime            @updatedAt
  // @@index([campaignId])

  @@index([userId])
}

// Workflow models for automation
model Workflow {
  id          String              @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  isActive    Boolean             @default(true)
  userId      String              @db.ObjectId
  user        User                @relation(fields: [userId], references: [id])
  source      String              @default("user_created") // user_created, pipeline_action
  sourceId    String?             @db.ObjectId // ID of the source object (e.g., StageAction ID)
  isEditable  Boolean             @default(true) // Whether the workflow can be edited in the UI
  trigger     WorkflowTrigger?
  conditions  WorkflowCondition[]
  actions     WorkflowAction[]
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  @@index([userId])
  @@index([source])
  @@index([sourceId])
}

model WorkflowTrigger {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  workflow    Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  workflowId  String   @unique @db.ObjectId
  triggerType String // contact_created, contact_updated, stage_changed, group_added, etc.
  config      Json // Configuration specific to the trigger type
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model WorkflowCondition {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  workflow   Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  workflowId String   @db.ObjectId
  field      String // The field to check (e.g., "email", "type", "company")
  operator   String // equals, not_equals, contains, greater_than, etc.
  value      String // The value to compare against
  order      Int // Order of evaluation
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([workflowId])
}

model WorkflowAction {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  workflow   Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  workflowId String   @db.ObjectId
  actionType String // send_email, create_task, add_to_group, change_stage, etc.
  config     Json // Configuration specific to the action type
  order      Int // Order of execution
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([workflowId])
}

model AuditLog {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  eventType   String // CREATE, UPDATE, DELETE, LOGIN, LOGOUT, etc.
  userId      String?  @db.ObjectId // User who performed the action
  user        User?    @relation(fields: [userId], references: [id])
  entityType  String? // Contact, Activity, Note, Document, etc.
  entityId    String? @db.ObjectId // ID of the affected entity
  oldValues   Json? // Previous values (for updates)
  newValues   Json? // New values (for creates/updates)
  metadata    Json? // Additional context (IP, user agent, etc.)
  ip          String?
  userAgent   String?
  createdAt   DateTime @default(now())

  @@index([eventType])
  @@index([userId])
  @@index([entityType])
  @@index([entityId])
  @@index([createdAt])
}

// Contact Stage History - Tracks when contacts move between pipeline stages
model ContactStageHistory {
  id          String         @id @default(auto()) @map("_id") @db.ObjectId
  contactId   String         @db.ObjectId
  contact     Contact        @relation(fields: [contactId], references: [id], onDelete: Cascade)
  stageId     String?        @db.ObjectId // null for "unassigned" or when contact exits pipeline
  stage       PipelineStage? @relation(fields: [stageId], references: [id])
  enteredAt   DateTime       @default(now())
  exitedAt    DateTime? // null if currently in this stage
  duration    Int? // Duration in days (calculated when exitedAt is set)
  userId      String?        @db.ObjectId // User who moved the contact
  user        User?          @relation(fields: [userId], references: [id])
  createdAt   DateTime       @default(now())

  @@index([contactId])
  @@index([stageId])
  @@index([enteredAt])
  @@index([exitedAt])
}

// Contact Lifecycle Metrics - Comprehensive analytics for each contact
model ContactLifecycleMetrics {
  id                    String   @id @default(auto()) @map("_id") @db.ObjectId
  contactId             String   @unique @db.ObjectId
  contact               Contact  @relation(fields: [contactId], references: [id], onDelete: Cascade)

  // Lifecycle tracking
  firstContactDate      DateTime?
  lastActivityDate      DateTime?
  totalActivities       Int      @default(0)
  totalStageChanges     Int      @default(0)
  daysInPipeline        Int? // Total days since first stage entry
  currentStageDays      Int? // Days in current stage
  averageResponseTime   Float? // Average response time in hours

  // Engagement scoring
  engagementScore       Float? // 0-100 calculated score
  activityFrequency     Float? // Activities per week average
  responseRate          Float? // Email/call response rate
  lastEngagementDate    DateTime?

  // Status flags
  isDormant             Boolean  @default(false)
  isHighValue           Boolean  @default(false)
  riskLevel             String   @default("low") // low, medium, high
  conversionProbability Float? // 0-1 probability of conversion

  // Business metrics
  estimatedValue        Float? // Estimated deal value
  actualValue           Float? // Actual deal value (when closed)
  leadSource            String? // Where this contact came from
  acquisitionCost       Float? // Cost to acquire this contact

  // Calculated fields
  lastCalculated        DateTime @default(now())
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  calculatedBy          String?  @db.ObjectId // User who triggered calculation
  calculatedByUser      User?    @relation(fields: [calculatedBy], references: [id])

  @@index([isDormant])
  @@index([riskLevel])
  @@index([engagementScore])
  @@index([lastActivityDate])
  @@index([conversionProbability])
}
