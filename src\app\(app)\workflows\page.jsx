'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Plus, Play, Pause, Edit, Trash2, AlertCircle, Info, X } from 'lucide-react';
import Tooltip from '@/components/ui/Tooltip';

export default function WorkflowsPage() {
  const [workflows, setWorkflows] = useState([]);
  const [filteredWorkflows, setFilteredWorkflows] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showHelpBanner, setShowHelpBanner] = useState(true);
  const [sourceFilter, setSourceFilter] = useState('all'); // 'all', 'user_created', or 'pipeline_action'
  const [sortField, setSortField] = useState('updatedAt'); // Default sort by last updated
  const [sortDirection, setSortDirection] = useState('desc'); // 'asc' or 'desc'
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'active', or 'inactive'
  const [windowHeight, setWindowHeight] = useState(typeof window !== 'undefined' ? window.innerHeight : 0);

  // Fetch workflows and pipeline actions when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch workflows from the API
        const workflowsResponse = await fetch('/api/workflows');
        if (!workflowsResponse.ok) throw new Error('Failed to fetch workflows');
        const workflowsData = await workflowsResponse.json();

        // Fetch pipeline actions
        const pipelineActionsResponse = await fetch('/api/pipeline/actions');
        if (!pipelineActionsResponse.ok) throw new Error('Failed to fetch pipeline actions');
        const pipelineActionsData = await pipelineActionsResponse.json();

        // Check if any pipeline actions need to be synced to workflows
        const pipelineActionIds = pipelineActionsData.map(action => action.id);
        const workflowSourceIds = workflowsData
          .filter(w => w.source === 'pipeline_action')
          .map(w => w.sourceId);

        // Find pipeline actions that don't have corresponding workflows
        const missingActionIds = pipelineActionIds.filter(id => !workflowSourceIds.includes(id));

        if (missingActionIds.length > 0) {
          console.log(`Found ${missingActionIds.length} pipeline actions without workflows. Syncing...`);

          // Trigger sync for each missing action
          for (const actionId of missingActionIds) {
            try {
              const action = pipelineActionsData.find(a => a.id === actionId);
              // Call the API to sync this action (this will create a workflow)
              await fetch(`/api/pipeline/actions/${actionId}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  actionType: action.actionType,
                  actionDetails: action.actionDetails
                })
              });
            } catch (syncError) {
              console.error(`Error syncing action ${actionId}:`, syncError);
            }
          }

          // Fetch workflows again to get the newly created ones
          const updatedWorkflowsResponse = await fetch('/api/workflows');
          if (updatedWorkflowsResponse.ok) {
            const updatedWorkflowsData = await updatedWorkflowsResponse.json();
            setWorkflows(updatedWorkflowsData);
          } else {
            setWorkflows(workflowsData);
          }
        } else {
          setWorkflows(workflowsData);
        }

        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load workflows. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Update window height on resize
  useEffect(() => {
    const handleResize = () => {
      setWindowHeight(window.innerHeight);
    };

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Initial height set
    handleResize();

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Filter and sort workflows
  useEffect(() => {
    if (workflows.length > 0) {
      // Step 1: Apply source filter
      let filtered = [...workflows];
      if (sourceFilter !== 'all') {
        filtered = filtered.filter(workflow => workflow.source === sourceFilter);
      }

      // Step 2: Apply status filter
      if (statusFilter !== 'all') {
        filtered = filtered.filter(workflow => {
          if (statusFilter === 'active') return workflow.isActive;
          if (statusFilter === 'inactive') return !workflow.isActive;
          return true;
        });
      }

      // Step 3: Apply search term
      if (searchTerm.trim()) {
        const term = searchTerm.toLowerCase().trim();
        filtered = filtered.filter(workflow =>
          workflow.name.toLowerCase().includes(term) ||
          (workflow.description && workflow.description.toLowerCase().includes(term))
        );
      }

      // Step 4: Apply sorting
      filtered.sort((a, b) => {
        let aValue = a[sortField];
        let bValue = b[sortField];

        // Handle special cases for sorting
        if (sortField === 'createdAt' || sortField === 'updatedAt') {
          aValue = new Date(aValue).getTime();
          bValue = new Date(bValue).getTime();
        } else if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        // Apply sort direction
        if (sortDirection === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      setFilteredWorkflows(filtered);
    } else {
      setFilteredWorkflows([]);
    }
  }, [workflows, sourceFilter, statusFilter, searchTerm, sortField, sortDirection]);

  // Handle sort toggle
  const handleSort = (field) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to descending
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const handleToggleActive = async (id, currentStatus) => {
    try {
      // Optimistic update
      setWorkflows(workflows.map(workflow =>
        workflow.id === id ? { ...workflow, isActive: !currentStatus } : workflow
      ));

      // Call the API to update the workflow
      const response = await fetch(`/api/workflows/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !currentStatus })
      });

      if (!response.ok) {
        // Revert the optimistic update if the API call fails
        setWorkflows(workflows);
        throw new Error('Failed to update workflow');
      }
    } catch (err) {
      console.error('Error updating workflow:', err);
      alert('Failed to update workflow status. Please try again.');
    }
  };

  const handleDeleteWorkflow = async (id) => {
    const workflow = workflows.find(w => w.id === id);

    if (workflow.source === 'pipeline_action') {
      alert('This workflow is linked to a pipeline stage action. To delete it, remove the action from the pipeline stage in the Pipeline settings.');
      return;
    }

    if (!confirm('Are you sure you want to delete this workflow?')) return;

    try {
      // Optimistic update
      const originalWorkflows = [...workflows];
      setWorkflows(workflows.filter(workflow => workflow.id !== id));

      // Call the API to delete the workflow
      const response = await fetch(`/api/workflows/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        // Revert the optimistic update if the API call fails
        setWorkflows(originalWorkflows);
        throw new Error('Failed to delete workflow');
      }
    } catch (err) {
      console.error('Error deleting workflow:', err);
      alert('Failed to delete workflow. Please try again.');
    }
  };

  // Helper function to get trigger description
  const getTriggerDescription = (trigger) => {
    const triggerTypes = {
      contact_created: 'Contact is created',
      contact_updated: 'Contact is updated',
      stage_changed: 'Pipeline stage changes',
      group_added: 'Contact is added to a group'
    };

    return triggerTypes[trigger.triggerType] || 'Unknown trigger';
  };

  // Helper function to get condition description
  const getConditionDescription = (condition) => {
    const operators = {
      equals: 'equals',
      not_equals: 'does not equal',
      contains: 'contains',
      greater_than: 'is greater than',
      less_than: 'is less than'
    };

    return `${condition.field} ${operators[condition.operator] || condition.operator} "${condition.value}"`;
  };

  // Helper function to get action description
  const getActionDescription = (action) => {
    const actionTypes = {
      send_email: 'Send email',
      create_task: 'Create task',
      add_to_group: 'Add to group',
      change_stage: 'Change pipeline stage'
    };

    let description = actionTypes[action.actionType] || 'Unknown action';

    if (action.actionType === 'send_email' && action.config.templateId) {
      description += ` (Template ID: ${action.config.templateId})`;
    } else if (action.actionType === 'create_task' && action.config.title) {
      description += `: "${action.config.title}"`;
    }

    return description;
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex justify-between items-center">
        <div>
          <div className="flex items-center">
            <h1 className="text-2xl font-semibold">Workflows</h1>
            <Tooltip content="Show help information">
              <button
                onClick={() => setShowHelpBanner(true)}
                className="ml-2 text-gray-400 hover:text-primary dark:text-gray-500 dark:hover:text-primary-light focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-900 rounded-full p-1"
              >
                <Info className="h-4 w-4" />
              </button>
            </Tooltip>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Create automated workflows to streamline your processes
          </p>
        </div>
        <Link
          href="/workflows/new"
          className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover flex items-center gap-2"
        >
          <Plus size={16} /> Create Workflow
        </Link>
      </div>

      {/* Help banner */}
      {showHelpBanner && (
        <div className="mb-6 bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-start space-x-3">
            <div className="text-primary dark:text-primary-light mt-0.5">
              <Info className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Workflow Automation</h3>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                <p>
                  Workflows allow you to automate repetitive tasks and processes in your CRM. Create workflows that trigger based on specific events,
                  such as when a contact is created or when a pipeline stage changes.
                </p>
                <p className="mt-1">
                  Each workflow consists of a trigger, optional conditions, and one or more actions. When the trigger event occurs and all conditions are met,
                  the actions will be executed automatically in the specified order.
                </p>
                <p className="mt-1">
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Pipeline Integration:</span> Automated actions from pipeline stages appear here as workflows with a "Pipeline" badge.
                  These workflows are managed through the Pipeline settings and will be automatically updated when pipeline actions change.
                </p>
                <p className="mt-1">
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Infinite Loop Protection:</span> Our system includes an automatic infinite loop detection feature that prevents the creation of workflows that could cause infinite loops. If a potential loop is detected during workflow creation, you'll see detailed information about the issue and will need to modify the workflow before saving.
                </p>
              </div>
            </div>
            <button
              className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              onClick={() => setShowHelpBanner(false)}
            >
              <span className="sr-only">Close</span>
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 p-4 rounded-md flex items-center gap-2">
          <AlertCircle size={18} />
          {error}
        </div>
      )}

      {/* Filter Section */}
      {!isLoading && workflows.length > 0 && (
        <div className="mb-4">
          {/* Search and Filters on same line */}
          <div className="flex items-center justify-between gap-6 flex-wrap">
            {/* Left side - Search */}
            <div className="relative w-80">
              <input
                type="text"
                placeholder="Search workflows..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Center - Filters */}
            <div className="flex items-center space-x-4 flex-1 justify-center">
              <div className="flex items-center space-x-2">
                <label htmlFor="source-filter" className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">
                  Source:
                </label>
                <select
                  id="source-filter"
                  value={sourceFilter}
                  onChange={(e) => setSourceFilter(e.target.value)}
                  className="block w-36 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
                >
                  <option value="all">All Sources</option>
                  <option value="user_created">User Created</option>
                  <option value="pipeline_action">Pipeline Actions</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label htmlFor="status-filter" className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">
                  Status:
                </label>
                <select
                  id="status-filter"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="block w-32 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
                >
                  <option value="all">All</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label htmlFor="sort-by" className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">
                  Sort:
                </label>
                <select
                  id="sort-by"
                  value={sortField}
                  onChange={(e) => setSortField(e.target.value)}
                  className="block w-32 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
                >
                  <option value="name">Name</option>
                  <option value="createdAt">Created</option>
                  <option value="updatedAt">Updated</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <select
                  id="sort-direction"
                  value={sortDirection}
                  onChange={(e) => setSortDirection(e.target.value)}
                  className="block w-24 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
                >
                  <option value="asc">↑</option>
                  <option value="desc">↓</option>
                </select>
              </div>
            </div>

            {/* Right side - Count and Clear */}
            <div className="text-sm text-gray-500 dark:text-gray-400 whitespace-nowrap">
              {filteredWorkflows.length} of {workflows.length}
              {(sourceFilter !== 'all' || statusFilter !== 'all' || searchTerm) && (
                <button
                  onClick={() => {
                    setSourceFilter('all');
                    setStatusFilter('all');
                    setSearchTerm('');
                  }}
                  className="ml-2 text-primary dark:text-primary-light hover:underline focus:outline-none"
                >
                  Clear
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Workflows list */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden border border-gray-200 dark:border-gray-700">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent">
              <span className="sr-only">Loading...</span>
            </div>
          </div>
        ) : workflows.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 dark:text-gray-400">
              No workflows found. Create your first workflow to get started.
            </p>
            <Link
              href="/workflows/new"
              className="mt-4 inline-block px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
            >
              Create Workflow
            </Link>
          </div>
        ) : filteredWorkflows.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500 dark:text-gray-400">
              No workflows match the current filter.
            </p>
            <button
              onClick={() => setSourceFilter('all')}
              className="mt-4 inline-block px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
            >
              Show All Workflows
            </button>
          </div>
        ) : (
          <div
            className="overflow-x-auto overflow-y-auto"
            style={{
              scrollbarWidth: 'thin',
              height: windowHeight ?
                `${windowHeight - (showHelpBanner ? 400 : 250)}px` :
                (showHelpBanner ? 'calc(100vh - 480px)' : 'calc(100vh - 320px)')
            }}
          >
            <table className="w-full table-fixed divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0 z-10">
                <tr>
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 w-[22%]"
                    onClick={() => handleSort('name')}
                  >
                    <div className="flex items-center">
                      Name
                      {sortField === 'name' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[11%]">
                    Trigger
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[15%]">
                    Conditions
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[17%]">
                    Actions
                  </th>
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 w-[8%]"
                    onClick={() => handleSort('isActive')}
                  >
                    <div className="flex items-center">
                      Status
                      {sortField === 'isActive' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 w-[9%]"
                    onClick={() => handleSort('createdAt')}
                  >
                    <div className="flex items-center">
                      Created
                      {sortField === 'createdAt' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 w-[9%]"
                    onClick={() => handleSort('updatedAt')}
                  >
                    <div className="flex items-center">
                      Updated
                      {sortField === 'updatedAt' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[9%]">
                    Controls
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredWorkflows.map((workflow) => (
                  <tr key={workflow.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <Tooltip content={workflow.name}>
                            <span className="font-medium text-gray-900 dark:text-white block truncate max-w-[280px]">
                              {workflow.name}
                            </span>
                          </Tooltip>
                          {workflow.source === 'pipeline_action' && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 whitespace-nowrap">
                              Pipeline
                            </span>
                          )}
                        </div>
                        {workflow.description && (
                          <Tooltip content={workflow.description}>
                            <span className="text-sm text-gray-500 dark:text-gray-400 block truncate max-w-[330px]">
                              {workflow.description}
                            </span>
                          </Tooltip>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      {workflow.trigger ? (
                        <Tooltip content={getTriggerDescription(workflow.trigger)}>
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                            {getTriggerDescription(workflow.trigger)}
                          </span>
                        </Tooltip>
                      ) : (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                          No trigger
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {workflow.conditions && workflow.conditions.length > 0 ? (
                          <ul className="list-disc list-inside">
                            {workflow.conditions.map((condition, index) => {
                              const description = getConditionDescription(condition);
                              return (
                                <li key={index}>
                                  <Tooltip content={description}>
                                    <span className="block truncate max-w-[200px]">{description}</span>
                                  </Tooltip>
                                </li>
                              );
                            })}
                          </ul>
                        ) : (
                          <span className="text-gray-500 dark:text-gray-400">No conditions</span>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {workflow.actions && workflow.actions.length > 0 ? (
                          <ul className="list-disc list-inside">
                            {workflow.actions.map((action, index) => {
                              const description = getActionDescription(action);
                              return (
                                <li key={index}>
                                  <Tooltip content={description}>
                                    <span className="block truncate max-w-[210px]">{description}</span>
                                  </Tooltip>
                                </li>
                              );
                            })}
                          </ul>
                        ) : (
                          <span className="text-gray-500 dark:text-gray-400">No actions</span>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        workflow.isActive
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                      }`}>
                        {workflow.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <Tooltip content={new Date(workflow.createdAt).toLocaleString()}>
                        <span>{new Date(workflow.createdAt).toLocaleDateString()}</span>
                      </Tooltip>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <Tooltip content={new Date(workflow.updatedAt).toLocaleString()}>
                        <span>{new Date(workflow.updatedAt).toLocaleDateString()}</span>
                      </Tooltip>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end items-center space-x-3">
                        <Tooltip content={workflow.isActive ? "Pause workflow" : "Activate workflow"}>
                          <button
                            onClick={() => handleToggleActive(workflow.id, workflow.isActive)}
                            className={`p-1.5 rounded-md ${
                              workflow.isActive
                                ? 'text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300'
                                : 'text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300'
                            }`}
                            aria-label={workflow.isActive ? "Pause workflow" : "Activate workflow"}
                          >
                            {workflow.isActive ? <Pause size={18} /> : <Play size={18} />}
                          </button>
                        </Tooltip>
                        <Tooltip content={workflow.source === 'pipeline_action' ? "Edit in Pipeline Settings" : "Edit workflow"}>
                          {workflow.source === 'pipeline_action' ? (
                            <Link
                              href="/pipeline"
                              className="p-1.5 rounded-md text-gray-400 dark:text-gray-600 cursor-not-allowed"
                              aria-label="Edit in Pipeline Settings"
                            >
                              <Edit size={18} />
                            </Link>
                          ) : (
                            <Link
                              href={`/workflows/${workflow.id}`}
                              className="p-1.5 rounded-md text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                              aria-label="Edit workflow"
                            >
                              <Edit size={18} />
                            </Link>
                          )}
                        </Tooltip>
                        <Tooltip content="Delete workflow">
                          <button
                            onClick={() => handleDeleteWorkflow(workflow.id)}
                            className="p-1.5 rounded-md text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                            aria-label="Delete workflow"
                          >
                            <Trash2 size={18} />
                          </button>
                        </Tooltip>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
