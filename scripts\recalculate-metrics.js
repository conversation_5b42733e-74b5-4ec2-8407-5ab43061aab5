#!/usr/bin/env node

/**
 * Utility script to recalculate ContactLifecycleMetrics
 * Can be run periodically or on-demand to update analytics
 * 
 * Run with: node scripts/recalculate-metrics.js [contactId]
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const contactId = process.argv[2];
  
  if (contactId) {
    console.log(`🔄 Recalculating metrics for contact: ${contactId}`);
    await recalculateContactMetrics(contactId);
  } else {
    console.log('🔄 Recalculating metrics for all contacts...');
    await recalculateAllMetrics();
  }
  
  console.log('✅ Metrics recalculation completed!');
  await prisma.$disconnect();
}

async function recalculateAllMetrics() {
  const contacts = await prisma.contact.findMany({
    select: { id: true }
  });
  
  console.log(`Processing ${contacts.length} contacts...`);
  
  for (const contact of contacts) {
    await recalculateContactMetrics(contact.id);
  }
}

async function recalculateContactMetrics(contactId) {
  try {
    const contact = await prisma.contact.findUnique({
      where: { id: contactId },
      include: {
        activities: {
          orderBy: { createdAt: 'desc' }
        },
        stageHistory: {
          orderBy: { enteredAt: 'asc' }
        },
        notes: true,
        lifecycleMetrics: true
      }
    });
    
    if (!contact) {
      console.log(`❌ Contact ${contactId} not found`);
      return;
    }
    
    // Calculate metrics
    const metrics = await calculateContactMetrics(contact);
    
    // Update or create lifecycle metrics
    await prisma.contactLifecycleMetrics.upsert({
      where: { contactId },
      update: {
        ...metrics,
        lastCalculated: new Date(),
        updatedAt: new Date()
      },
      create: {
        contactId,
        ...metrics,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
    
    console.log(`✅ Updated metrics for contact ${contactId}`);
  } catch (error) {
    console.error(`❌ Error calculating metrics for contact ${contactId}:`, error);
  }
}

async function calculateContactMetrics(contact) {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  
  // Basic counts
  const totalActivities = contact.activities.length;
  const totalStageChanges = contact.stageHistory.length;
  
  // Date calculations
  const firstContactDate = contact.createdAt;
  const lastActivity = contact.activities[0]; // Already ordered by createdAt desc
  const lastActivityDate = lastActivity?.createdAt || null;
  
  // Pipeline calculations
  const firstStageEntry = contact.stageHistory[0]; // Already ordered by enteredAt asc
  const daysInPipeline = firstStageEntry 
    ? Math.ceil((now - firstStageEntry.enteredAt) / (1000 * 60 * 60 * 24))
    : null;
  
  const currentStageEntry = contact.stageHistory.find(h => h.exitedAt === null);
  const currentStageDays = currentStageEntry
    ? Math.ceil((now - currentStageEntry.enteredAt) / (1000 * 60 * 60 * 24))
    : null;
  
  // Activity frequency (activities per week over last 30 days)
  const recentActivities = contact.activities.filter(a => a.createdAt >= thirtyDaysAgo);
  const activityFrequency = recentActivities.length / 4.3; // 30 days ≈ 4.3 weeks
  
  // Engagement score calculation (0-100)
  const recentActivityScore = Math.min(40, recentActivities.length * 4);
  const totalActivityScore = Math.min(30, totalActivities * 0.5);
  const recencyScore = lastActivityDate 
    ? Math.max(0, 30 - Math.ceil((now - lastActivityDate) / (1000 * 60 * 60 * 24)))
    : 0;
  const engagementScore = recentActivityScore + totalActivityScore + recencyScore;
  
  // Status flags
  const isDormant = !lastActivityDate || (now - lastActivityDate) > (30 * 24 * 60 * 60 * 1000);
  const isHighValue = engagementScore > 70 && totalActivities > 10;
  
  // Risk level calculation
  let riskLevel = 'low';
  if (isDormant) {
    riskLevel = 'high';
  } else if (currentStageDays > 60 || engagementScore < 30) {
    riskLevel = 'medium';
  }
  
  // Conversion probability (simple algorithm)
  let conversionProbability = 0.5; // Base probability
  
  if (engagementScore > 70) conversionProbability += 0.3;
  else if (engagementScore < 30) conversionProbability -= 0.3;
  
  if (currentStageDays > 90) conversionProbability -= 0.2;
  else if (currentStageDays < 30) conversionProbability += 0.1;
  
  if (totalStageChanges > 3) conversionProbability += 0.1;
  
  conversionProbability = Math.max(0, Math.min(1, conversionProbability));
  
  // Average response time (mock calculation - would need actual response data)
  const averageResponseTime = totalActivities > 0 ? 24 + Math.random() * 48 : null;
  
  // Response rate (mock calculation - would need actual response data)
  const responseRate = totalActivities > 5 ? 0.6 + Math.random() * 0.3 : null;
  
  return {
    firstContactDate,
    lastActivityDate,
    totalActivities,
    totalStageChanges,
    daysInPipeline,
    currentStageDays,
    averageResponseTime,
    engagementScore,
    activityFrequency,
    responseRate,
    lastEngagementDate: lastActivityDate,
    isDormant,
    isHighValue,
    riskLevel,
    conversionProbability,
    leadSource: contact.leadSource
  };
}

// Run the script
main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
