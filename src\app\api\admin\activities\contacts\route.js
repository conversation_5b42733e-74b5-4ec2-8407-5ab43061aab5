import { NextResponse } from 'next/server';
import { getSignedInUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma/client';

// GET /api/admin/activities/contacts - Get all contacts for activity management dropdown
export async function GET(request) {
  try {
    // Check authentication and authorization
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Get query parameters for search
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';

    // Build where clause for search
    const where = {};
    if (search) {
      where.OR = [
        {
          firstName: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          lastName: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          email: {
            contains: search,
            mode: 'insensitive'
          }
        }
      ];
    }

    // Get contacts
    const contacts = await prisma.contact.findMany({
      where,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true
      },
      orderBy: [
        { firstName: 'asc' },
        { lastName: 'asc' }
      ],
      take: 100 // Limit to prevent too many results
    });

    return NextResponse.json(contacts);

  } catch (error) {
    console.error('Error fetching contacts for activity management:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contacts' },
      { status: 500 }
    );
  }
}
