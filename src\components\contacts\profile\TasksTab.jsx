"use client";

import { useState, useEffect } from "react";
import TasklistCard from "./TasklistCard";
import TasklistForm from "./TasklistForm";
import TaskFilter from "./TaskFilter";

export default function TasksTab({ contactId }) {
  const [filters, setFilters] = useState({
    status: "all",
    priority: "all",
    sortBy: "dueDate",
    sortOrder: "asc",
    searchTerm: "",
  });
  const [tasklists, setTasklists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [expandedTasklistId, setExpandedTasklistId] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [editingTasklist, setEditingTasklist] = useState(null);

  const fetchTasklists = async () => {
    setLoading(true);
    setError("");
    try {
      const response = await fetch(`/api/contacts/${contactId}/tasklists`);
      if (!response.ok) throw new Error("Failed to fetch tasklists");
      const data = await response.json();
      setTasklists(data);
    } catch (error) {
      setError("Failed to load tasklists. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTasklists();
  }, [contactId]);

  // Tasklist CRUD
  const handleAddTasklist = () => {
    setEditingTasklist(null);
    setShowForm(true);
  };

  const handleEditTasklist = (tasklist) => {
    setEditingTasklist(tasklist);
    setShowForm(true);
  };

  const handleDeleteTasklist = async (tasklistId) => {
    if (!window.confirm("Delete this tasklist and all its items?")) return;
    try {
      const response = await fetch(`/api/tasklists/${tasklistId}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error("Failed to delete tasklist");
      setTasklists(tasklists.filter((tl) => tl.id !== tasklistId));
    } catch (err) {
      setError("Failed to delete tasklist.");
    }
  };

  const handleTasklistFormSuccess = (newOrUpdated) => {
    setShowForm(false);
    setEditingTasklist(null);
    fetchTasklists();
  };

  // Tasklist Item CRUD
  const handleToggleItem = async (tasklistId, itemId, completed) => {
    try {
      const response = await fetch(`/api/tasklist-items/${itemId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ completed }),
      });
      if (!response.ok) throw new Error("Failed to update item");
      fetchTasklists();
    } catch (err) {
      setError("Failed to update item.");
    }
  };

  const handleEditItem = (tasklistId, item) => {
    // Open the form in edit mode for the parent tasklist, pre-filling the item
    const tasklist = tasklists.find((tl) => tl.id === tasklistId);
    if (tasklist) {
      setEditingTasklist({ ...tasklist, items: [item] });
      setShowForm(true);
    }
  };

  const handleDeleteItem = async (tasklistId, itemId) => {
    if (!window.confirm("Delete this task?")) return;
    try {
      const response = await fetch(`/api/tasklist-items/${itemId}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error("Failed to delete item");
      fetchTasklists();
    } catch (err) {
      setError("Failed to delete item.");
    }
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleSearch = (searchTerm) => {
    setFilters((prev) => ({
      ...prev,
      searchTerm,
    }));
  };

  // Helper function to check if task matches filters
  const taskMatchesFilters = (task) => {
    // Filter by status
    if (filters.status === "completed" && !task.completed) return false;
    if (filters.status === "pending" && task.completed) return false;

    // Filter by priority
    if (filters.priority !== "all" && task.priority !== filters.priority)
      return false;

    // Filter by search term
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const titleMatch = task.title?.toLowerCase().includes(searchLower);
      const descriptionMatch = task.description
        ?.toLowerCase()
        .includes(searchLower);
      if (!titleMatch && !descriptionMatch) return false;
    }

    return true;
  };

  // Apply filters to tasklists and their items
  const filteredTasklists = tasklists
    .map((tasklist) => {
      // Filter items within each tasklist
      const filteredItems = (tasklist.items || []).filter(taskMatchesFilters);

      // Only include tasklist if it has filtered items or if there's no filter applied
      const hasMatchingItems = filteredItems.length > 0;
      const hasNoFilters =
        filters.status === "all" &&
        filters.priority === "all" &&
        !filters.searchTerm;

      if (hasMatchingItems || hasNoFilters) {
        return {
          ...tasklist,
          items: filteredItems,
        };
      }

      return null;
    })
    .filter(Boolean);

  // Sort items within each tasklist
  const sortedTasklists = filteredTasklists.map((tasklist) => ({
    ...tasklist,
    items: [...(tasklist.items || [])].sort((a, b) => {
      const sortOrder = filters.sortOrder === "asc" ? 1 : -1;

      if (filters.sortBy === "dueDate") {
        const aDate = a.dueDate ? new Date(a.dueDate) : new Date(0);
        const bDate = b.dueDate ? new Date(b.dueDate) : new Date(0);
        return (aDate - bDate) * sortOrder;
      }

      if (filters.sortBy === "priority") {
        const priorityValues = { high: 3, medium: 2, low: 1 };
        return (
          (priorityValues[a.priority || "low"] -
            priorityValues[b.priority || "low"]) *
          sortOrder
        );
      }

      if (filters.sortBy === "title") {
        return (a.title || "").localeCompare(b.title || "") * sortOrder;
      }

      return 0;
    }),
  }));

  if (loading)
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow h-full overflow-auto">
        <div className="p-4 flex items-center justify-center h-full">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent">
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      </div>
    );

  if (error && tasklists.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow h-full overflow-auto">
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Tasks
            </h3>
          </div>
          <div className="p-6 text-center">
            <div className="p-3 mb-4 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
              {error}
            </div>
            <p className="mb-4 text-gray-600 dark:text-gray-400">
              We couldn't load your tasklists. This might be due to a temporary
              issue.
            </p>
            <button
              onClick={fetchTasklists}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow h-full overflow-auto">
      <div className="p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-4">
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Tasks
            </h3>
            {/* Search input */}
            <input
              type="text"
              placeholder="Search tasks..."
              value={filters.searchTerm}
              onChange={(e) =>
                setFilters((f) => ({ ...f, searchTerm: e.target.value }))
              }
              className="rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 py-1 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              style={{ minWidth: 180 }}
            />
            {/* Filter/sort UI */}
            <TaskFilter filters={filters} onFilterChange={setFilters} />
          </div>
          <button
            className="px-3 py-1 bg-primary text-white rounded hover:bg-primary-hover"
            onClick={handleAddTasklist}
          >
            Add Tasklist
          </button>
        </div>
        {error && (
          <div className="p-3 mb-4 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
            {error}
          </div>
        )}
        <div className="space-y-8">
          {sortedTasklists.map((tasklist) => (
            <TasklistCard
              key={tasklist.id}
              tasklist={tasklist}
              isExpanded={expandedTasklistId === tasklist.id}
              onToggleExpand={() =>
                setExpandedTasklistId(
                  expandedTasklistId === tasklist.id ? null : tasklist.id
                )
              }
              onEdit={() => handleEditTasklist(tasklist)}
              onDelete={() => handleDeleteTasklist(tasklist.id)}
              onItemToggle={handleToggleItem}
              onItemEdit={handleEditItem}
              onItemDelete={handleDeleteItem}
            />
          ))}
          {sortedTasklists.length === 0 && tasklists.length > 0 && (
            <div className="p-6 text-center text-gray-500 dark:text-gray-400 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              No tasks match your current filters
            </div>
          )}
          {tasklists.length === 0 && (
            <div className="p-6 text-center text-gray-500 dark:text-gray-400 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              No tasklists yet
            </div>
          )}
        </div>
        <TasklistForm
          contactId={contactId}
          tasklist={editingTasklist}
          isOpen={showForm}
          onClose={() => {
            setShowForm(false);
            setEditingTasklist(null);
          }}
          onSuccess={handleTasklistFormSuccess}
        />
      </div>
    </div>
  );
}
