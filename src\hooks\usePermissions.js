import { useEffect, useState } from 'react';

/**
 * Hook to check if the current user has the required permissions
 * @param {string|string[]} requiredPermissions - The permission(s) to check for
 * @returns {Object} - Object containing permission check results
 */
export function usePermissions(requiredPermissions) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    async function fetchUser() {
      try {
        // Fetch the current user from the API
        const response = await fetch('/api/auth/me');
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
          
          // Check if the user has the required permissions
          if (userData.role === 'admin') {
            // Admin role has all permissions
            setHasPermission(true);
          } else if (Array.isArray(requiredPermissions)) {
            // Check if the user has any of the required permissions
            const hasAny = requiredPermissions.some(permission => 
              userData.permissions && userData.permissions.includes(permission)
            );
            setHasPermission(hasAny);
          } else {
            // Check if the user has the specific permission
            setHasPermission(
              userData.permissions && userData.permissions.includes(requiredPermissions)
            );
          }
        } else {
          setHasPermission(false);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        setHasPermission(false);
      } finally {
        setLoading(false);
      }
    }

    fetchUser();
  }, [requiredPermissions]);

  return {
    user,
    loading,
    hasPermission,
    isAdmin: user?.role === 'admin',
    role: user?.role
  };
}

/**
 * Hook to check if the current user has the required role
 * @param {string|string[]} requiredRoles - The role(s) to check for
 * @returns {Object} - Object containing role check results
 */
export function useRole(requiredRoles) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [hasRole, setHasRole] = useState(false);

  useEffect(() => {
    async function fetchUser() {
      try {
        // Fetch the current user from the API
        const response = await fetch('/api/auth/me');
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
          
          if (Array.isArray(requiredRoles)) {
            // Check if the user has any of the required roles
            setHasRole(requiredRoles.includes(userData.role));
          } else {
            // Check if the user has the specific role
            setHasRole(userData.role === requiredRoles);
          }
        } else {
          setHasRole(false);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        setHasRole(false);
      } finally {
        setLoading(false);
      }
    }

    fetchUser();
  }, [requiredRoles]);

  return {
    user,
    loading,
    hasRole,
    isAdmin: user?.role === 'admin',
    role: user?.role
  };
}
