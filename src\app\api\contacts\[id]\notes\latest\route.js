import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

export async function GET(request, { params }) {
  try {
    const { id } = await params;
    console.log('Fetching latest note for contact:', id);

    // Get the latest note from the database
    const latestNote = await prisma.note.findFirst({
      where: { contactId: id },
      orderBy: { createdAt: 'desc' }
    });

    console.log('Found latest note:', latestNote ? 'Yes' : 'No');

    return NextResponse.json(latestNote || null);
  } catch (error) {
    console.error('Failed to fetch latest note:', error);
    return NextResponse.json(
      { error: `Failed to fetch latest note: ${error.message}` },
      { status: 500 }
    );
  }
}
