# MongoDB Database URL
DATABASE_URL="***************************************************"

# Next Auth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# Microsoft Azure AD Configuration
AZURE_AD_CLIENT_ID="your-azure-client-id"
AZURE_AD_CLIENT_SECRET="your-azure-client-secret"
AZURE_AD_TENANT_ID="your-tenant-id"

# Charles Schwab API Configuration
SCHWAB_CLIENT_ID="your-schwab-client-id"
SCHWAB_CLIENT_SECRET="your-schwab-client-secret"
SCHWAB_API_URL="https://api.schwab.com"

# Interactive Brokers API Configuration
IB_CLIENT_ID="your-ib-client-id"
IB_CLIENT_SECRET="your-ib-client-secret"
IB_API_URL="https://api.interactivebrokers.com"

# Security Configuration
ENCRYPTION_KEY="your-32-byte-encryption-key" # Must be 32 bytes for AES-256