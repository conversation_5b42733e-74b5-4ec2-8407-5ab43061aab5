import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

// Update a task
export async function PATCH(request, { params }) {
  try {
    const body = await request.json();
    const { title, description, dueDate, priority, completed } = body;

    // Get current task to check if completion status is changing
    const { id } = await params;
    const currentTask = await prisma.task.findUnique({
      where: { id }
    });

    if (!currentTask) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    // Determine if completion status is changing
    const completionStatusChanging =
      completed !== undefined && completed !== currentTask.completed;

    // Create a data object with only the basic fields we're updating
    const updateData = {
      ...(title !== undefined && { title }),
      ...(description !== undefined && { description }),
      ...(dueDate !== undefined && { dueDate: new Date(`${dueDate}T12:00:00Z`) }),
      ...(priority !== undefined && { priority }),
      ...(completed !== undefined && { completed }),
    };

    // Check if the schema has been updated with the new fields
    const hasNewFields = Object.keys(currentTask).includes('completedAt');

    // Only add the new fields if they exist in the schema
    if (hasNewFields) {
      console.log('Schema has new fields, adding completedAt and updatedAt');
      // Only add completedAt if the completion status is changing
      if (completionStatusChanging) {
        updateData.completedAt = completed ? new Date() : null;
      }

      // Add updatedAt field
      updateData.updatedAt = new Date();
    } else {
      console.log('Schema does not have new fields, skipping completedAt and updatedAt');
    }

    console.log('Updating task with data:', updateData);

    const updatedTask = await prisma.task.update({
      where: { id },
      data: updateData
    });

    return NextResponse.json(updatedTask);
  } catch (error) {
    console.error('Failed to update task:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      { error: `Failed to update task: ${error.message}` },
      { status: 500 }
    );
  }
}

// Delete a task
export async function DELETE(request, { params }) {
  try {
    const { id } = await params;
    await prisma.task.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete task:', error);
    return NextResponse.json(
      { error: 'Failed to delete task' },
      { status: 500 }
    );
  }
}
