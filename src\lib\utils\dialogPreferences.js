/**
 * Utility functions for working with dialog field preferences
 */

/**
 * Fetches dialog field preferences from the API
 * @param {string} dialogKey - The key for the dialog
 * @returns {Promise<string[]>} - Array of visible field IDs
 */
export async function fetchDialogFieldPreferences(dialogKey) {
  try {
    const response = await fetch(`/api/dialog-preferences?dialogKey=${dialogKey}`);
    
    if (response.ok) {
      const data = await response.json();
      
      // If visibleFields is empty, it means all fields are visible (default)
      if (data.visibleFields && data.visibleFields.length > 0) {
        return data.visibleFields;
      }
    }
    
    // Return null if no preferences found or error occurred
    return null;
  } catch (error) {
    console.error('Failed to fetch dialog preferences:', error);
    return null;
  }
}

/**
 * Saves dialog field preferences to the API
 * @param {string} dialogKey - The key for the dialog
 * @param {string[]} visibleFields - Array of visible field IDs
 * @returns {Promise<boolean>} - Whether the save was successful
 */
export async function saveDialogFieldPreferences(dialog<PERSON><PERSON>, visibleFields) {
  try {
    const response = await fetch('/api/dialog-preferences', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        dialogKey,
        visibleFields,
      }),
    });
    
    return response.ok;
  } catch (error) {
    console.error('Failed to save dialog preferences:', error);
    return false;
  }
}
