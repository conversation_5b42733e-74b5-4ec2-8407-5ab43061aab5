## Known Bugs and UI Issues

### 1. Sticky Actions Column Header Overlap (Contacts Table)

**Description:**
When both the Actions column `<th>` (header) and `<td>` (cells) are set to `position: sticky` (with `top: 0` for the header and `right: 0` for both), the action icons in the sticky `<td>` cells scroll over the sticky Actions `<th>` header when scrolling vertically. This causes the icons to appear above the header, making the UI look broken.

**Why it happens:**
This is a browser stacking context quirk/limitation. Sticky elements on different axes (top/right) in a table create separate stacking contexts, and the DOM order means the `<td>`s (rendered after the `<th>`) can appear above the header, even with z-index adjustments. This is a well-known issue in Chromium-based browsers and is difficult to resolve with pure CSS.

**Attempts to fix:**

- Adjusted z-index on `<th>` and `<td>` (no effect).
- Added a pseudo-element overlay to the `<th>` (did not work reliably).
- Removing sticky from `<th>` makes the header scroll horizontally, which is not desired.

**Current workaround:**

- The bug is still present. The Actions header and cells both remain sticky for usability, but the overlap issue is unresolved.

**References:**

- [MDN: Stacking context and sticky elements](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_positioned_layout/Understanding_z_index/Stacking_context)
- [Chromium bug: Sticky table headers and columns stacking](https://bugs.chromium.org/p/chromium/issues/detail?id=1029279)

**Priority:** Medium (UI polish, but not a functional blocker)

**Potential future solutions:**

- Wait for browser fixes.
- Use a JavaScript solution to force stacking order.
- Redesign table layout to avoid intersecting sticky axes.

### 2. Clicking back on email campaign creation if it is not an automated drip campaign will still bring you to an empty step 2
