import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

// GET /api/activity-types - Get all activity types
export async function GET(request) {
  try {
    // Check authentication
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get('isActive');
    
    // Build query
    const where = {};
    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }

    // Fetch activity types
    const activityTypes = await prisma.activityType.findMany({
      where,
      orderBy: { name: 'asc' }
    });

    return NextResponse.json(activityTypes);
  } catch (error) {
    console.error('Failed to fetch activity types:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activity types' },
      { status: 500 }
    );
  }
}

// POST /api/activity-types - Create a new activity type
export async function POST(request) {
  try {
    // Check authentication and authorization
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Only admins can create activity types' },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.icon || !body.color) {
      return NextResponse.json(
        { error: 'Name, icon, and color are required' },
        { status: 400 }
      );
    }

    // Create the activity type
    const activityType = await prisma.activityType.create({
      data: {
        name: body.name,
        icon: body.icon,
        color: body.color,
        description: body.description || null,
        isSystem: false, // New types created by users are never system types
        isActive: body.isActive !== undefined ? body.isActive : true
      }
    });

    // Log the activity type creation
    await auditLogger.logActivityTypeCreate({
      userId: user.id,
      activityType,
      request
    });

    return NextResponse.json(activityType, { status: 201 });
  } catch (error) {
    console.error('Failed to create activity type:', error);
    return NextResponse.json(
      { error: 'Failed to create activity type' },
      { status: 500 }
    );
  }
}
