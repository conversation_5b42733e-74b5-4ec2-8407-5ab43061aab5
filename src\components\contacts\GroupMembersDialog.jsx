'use client';

import { useState, useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';

export default function GroupMembersDialog({ group, contacts, onClose, onMembersUpdated }) {
  const [members, setMembers] = useState([]);
  const [availableContacts, setAvailableContacts] = useState([]);
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (group) {
      fetchGroupMembers();
    }
  }, [group]);

  const fetchGroupMembers = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/groups/${group.id}/members`);

      if (!response.ok) {
        throw new Error('Failed to fetch group members');
      }

      const data = await response.json();
      setMembers(data);

      // Filter out contacts that are already members
      const memberContactIds = data.map(member => member.contactId);
      const available = contacts.filter(contact => !memberContactIds.includes(contact.id));
      setAvailableContacts(available);

      setError('');
    } catch (err) {
      console.error('Error fetching group members:', err);
      setError('Failed to load group members');
    } finally {
      setLoading(false);
    }
  };

  const handleAddMembers = async () => {
    if (selectedContacts.length === 0) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/groups/${group.id}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contactIds: selectedContacts
        })
      });

      if (!response.ok) {
        throw new Error('Failed to add contacts to group');
      }

      // Reset selected contacts
      setSelectedContacts([]);

      // Refresh members list
      await fetchGroupMembers();

      if (onMembersUpdated) {
        onMembersUpdated();
      }

      setError('');
    } catch (err) {
      console.error('Error adding contacts to group:', err);
      setError('Failed to add contacts to group');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveMember = async (contactId) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/groups/${group.id}/members?contactId=${contactId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to remove contact from group');
      }

      // Refresh members list
      await fetchGroupMembers();

      if (onMembersUpdated) {
        onMembersUpdated();
      }

      setError('');
    } catch (err) {
      console.error('Error removing contact from group:', err);
      setError('Failed to remove contact from group');
    } finally {
      setLoading(false);
    }
  };

  const handleContactSelect = (contactId) => {
    setSelectedContacts(prev => {
      if (prev.includes(contactId)) {
        return prev.filter(id => id !== contactId);
      } else {
        return [...prev, contactId];
      }
    });
  };

  const filteredContacts = availableContacts.filter(contact => {
    const fullName = `${contact.firstName} ${contact.lastName}`.toLowerCase();
    return fullName.includes(searchTerm.toLowerCase()) ||
           (contact.email && contact.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
           (contact.company && contact.company.toLowerCase().includes(searchTerm.toLowerCase()));
  });

  return (
    <Dialog.Portal>
      <Dialog.Overlay className="fixed inset-0 bg-black/50 z-[100]" />
      <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-4xl max-h-[75vh] overflow-hidden flex flex-col z-[110] border border-gray-300 dark:border-gray-600 shadow-xl">
        <Dialog.Title className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          Manage Group Contacts - {group?.name}
        </Dialog.Title>

        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
            {error}
          </div>
        )}

        <div className="flex flex-1 space-x-3 overflow-hidden">
          {/* Current members */}
          <div className="w-1/2 flex flex-col">
            <h3 className="text-base font-medium mb-1 text-gray-800 dark:text-gray-200">
              Current Contacts ({members.length})
            </h3>

            {loading ? (
              <div className="flex justify-center items-center py-4">
                <svg className="animate-spin h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            ) : members.length === 0 ? (
              <div className="text-gray-500 dark:text-gray-400 text-center py-4 flex-1 border border-dashed border-gray-300 dark:border-gray-600 rounded-md">
                No contacts in this group
              </div>
            ) : (
              <div className="flex-1 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md">
                {members.map(member => (
                  <div key={member.id} className="flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                    <div>
                      <div className="font-medium text-sm text-gray-800 dark:text-gray-200">
                        {member.contact.firstName} {member.contact.lastName}
                      </div>
                      {member.contact.email && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {member.contact.email}
                        </div>
                      )}
                      {member.contact.company && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {member.contact.company}
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => handleRemoveMember(member.contactId)}
                      disabled={loading}
                      className="text-red-500 hover:text-red-700 dark:hover:text-red-400 disabled:opacity-50"
                      title="Remove from group"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Available contacts */}
          <div className="w-1/2 flex flex-col">
            <h3 className="text-base font-medium mb-1 text-gray-800 dark:text-gray-200">
              Add Contacts
            </h3>

            <div className="mb-1">
              <input
                type="text"
                placeholder="Search contacts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>

            {loading ? (
              <div className="flex justify-center items-center py-4">
                <svg className="animate-spin h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            ) : filteredContacts.length === 0 ? (
              <div className="text-gray-500 dark:text-gray-400 text-center py-4 flex-1 border border-dashed border-gray-300 dark:border-gray-600 rounded-md">
                {searchTerm ? 'No contacts match your search' : 'No contacts available to add'}
              </div>
            ) : (
              <div className="flex-1 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md">
                {filteredContacts.map(contact => (
                  <div key={contact.id} className="flex items-center p-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                    <input
                      type="checkbox"
                      checked={selectedContacts.includes(contact.id)}
                      onChange={() => handleContactSelect(contact.id)}
                      className="mr-3 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                    <div>
                      <div className="font-medium text-sm text-gray-800 dark:text-gray-200">
                        {contact.firstName} {contact.lastName}
                      </div>
                      {contact.email && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {contact.email}
                        </div>
                      )}
                      {contact.company && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {contact.company}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="mt-1 flex justify-end">
              <button
                onClick={handleAddMembers}
                disabled={loading || selectedContacts.length === 0}
                className="px-3 py-1 text-sm bg-primary text-white rounded-md hover:bg-primary-hover disabled:opacity-50"
              >
                Add Selected ({selectedContacts.length})
              </button>
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-3">
          <Dialog.Close asChild>
            <button
              type="button"
              className="px-3 py-1 text-sm bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600"
            >
              Close
            </button>
          </Dialog.Close>
        </div>
      </Dialog.Content>
    </Dialog.Portal>
  );
}
