'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import {
  Download,
  File,
  FileText,
  Grid,
  Info,
  List,
  Search,
  SortAsc,
  SortDesc,
  Tag,
  Trash2,
  Upload,
  Users,
  Image,
  FileType,
  Table
} from 'lucide-react';
import Tooltip from '@/components/ui/Tooltip';
import { useMicrosoftGraph } from '@/hooks/useMicrosoftGraph';
import { useAuth } from '@/components/providers/AuthContext';
import UploadDocumentModal from '@/components/documents/UploadDocumentModal';
import { usePermissions } from '@/hooks/usePermissions';
import DeleteDocumentModal from '@/components/documents/DeleteDocumentModal';
import ManageContactsModal from '@/components/documents/ManageContactsModal';
import EditDocumentTagsModal from '@/components/documents/EditDocumentTagsModal';
import DocumentTagSelector from '@/components/documents/DocumentTagSelector';

export default function DocumentsPage() {
  const { user } = useAuth();
  const {
    isAuthenticated: msGraphAuthenticated,
    error: authError,
    oneDrive,
    sharePoint,
    login: msGraphLogin
  } = useMicrosoftGraph('documents');

  // Permission check for contacts
  const { hasPermission: canViewContacts, loading: permissionsLoading } = usePermissions('contacts:read');
  const [contactsError, setContactsError] = useState(null);

  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [sortConfig, setSortConfig] = useState({ key: 'uploadedAt', direction: 'desc' });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedContact, setSelectedContact] = useState('all');
  const [selectedFolder, setSelectedFolder] = useState('all');
  const [selectedSource, setSelectedSource] = useState('all'); // 'all', 'onedrive', 'sharepoint'
  const [selectedTags, setSelectedTags] = useState([]);
  const [availableTags, setAvailableTags] = useState([]);
  const [contacts, setContacts] = useState([]);
  const [contactSearchTerm, setContactSearchTerm] = useState('');
  const [showHelpBanner, setShowHelpBanner] = useState(true);
  const [folders] = useState([
    { id: 'all', name: 'All Documents', source: 'all' },
    { id: 'recent', name: 'Recent Documents', source: 'all' },
    { id: 'onedrive', name: 'OneDrive', source: 'onedrive' },
    { id: 'sharepoint', name: 'SharePoint', source: 'sharepoint' }
  ]);
  const [oneDriveFolders, setOneDriveFolders] = useState([]);
  const [sharePointSites, setSharePointSites] = useState([]);
  const [sharePointLibraries, setSharePointLibraries] = useState([]);
  const [selectedSite, setSelectedSite] = useState(null);
  const [selectedLibrary, setSelectedLibrary] = useState(null);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);
  const [manageContactsModalOpen, setManageContactsModalOpen] = useState(false);
  const [documentToManage, setDocumentToManage] = useState(null);
  const [editTagsModalOpen, setEditTagsModalOpen] = useState(false);
  const [documentToEditTags, setDocumentToEditTags] = useState(null);

  // Refs to track fetch status
  const oneDriveFetched = useRef(false);
  const sharePointFetched = useRef(false);
  const sharePointLibrariesFetched = useRef(false);
  const sharePointDocumentsFetched = useRef(false);

  // Fetch documents from API and Microsoft Graph
  useEffect(() => {
    // Reset all fetch statuses when the component mounts
    oneDriveFetched.current = false;
    sharePointFetched.current = false;
    sharePointLibrariesFetched.current = false;
    sharePointDocumentsFetched.current = false;

    const fetchDocuments = async () => {
      setLoading(true);

      try {
        // Fetch documents from API (includes document associations)
        const response = await fetch('/api/documents');
        let apiDocuments = [];

        if (response.ok) {
          apiDocuments = await response.json();
        } else {
          console.warn('Failed to fetch documents from API, using mock data');
          apiDocuments = mockDocuments;
        }

        // Fetch contacts if permitted
        if (canViewContacts && !permissionsLoading) {
          setContactsError(null);
          const contactsResponse = await fetch('/api/contacts');
          if (contactsResponse.ok) {
            const contactsData = await contactsResponse.json();
            // Format contacts for the dropdown
            const formattedContacts = contactsData.map(contact => ({
              id: contact.id,
              name: `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || contact.name || ''
            }));
            setContacts(formattedContacts);
          } else if (contactsResponse.status === 403 || contactsResponse.status === 401) {
            setContactsError('You do not have permission to view contacts.');
            setContacts([]);
          } else {
            setContactsError('Failed to load contacts.');
            setContacts([]);
          }
        } else if (!canViewContacts && !permissionsLoading) {
          setContactsError('You do not have permission to view contacts.');
          setContacts([]);
        }

        // Set documents from API
        setDocuments(apiDocuments);

        // Fetch available document tags
        try {
          const tagsResponse = await fetch('/api/documents/tags');
          if (tagsResponse.ok) {
            const tagsData = await tagsResponse.json();
            setAvailableTags(tagsData.tags || []);
          }
        } catch (error) {
          console.error('Failed to fetch document tags:', error);
        }
      } catch (error) {
        console.error('Failed to fetch documents from API:', error);
        // Fallback to mock data
        setDocuments(mockDocuments);
        setContacts(mockContacts);
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, [canViewContacts, permissionsLoading]);

  // Fetch OneDrive documents when authenticated and folder is selected
  useEffect(() => {
    const fetchOneDriveDocuments = async () => {
      // Only fetch if authenticated and folder is selected or we're in "all" or "recent" view
      if (!msGraphAuthenticated) return;
      if (selectedFolder !== 'onedrive' && selectedFolder !== 'all' && selectedFolder !== 'recent') return;

      // Use a ref to prevent duplicate fetches
      if (oneDriveFetched.current) return;

      console.log(`DOCUMENTS PAGE: Fetching OneDrive documents for ${selectedFolder} view`);

      setLoading(true);
      console.log('DOCUMENTS PAGE: Fetching OneDrive documents');

      try {
        // Get OneDrive root items
        const oneDriveItems = await oneDrive.getRootItems();

        if (!oneDriveItems) {
          console.error('DOCUMENTS PAGE: Failed to get OneDrive items - null response');
          setLoading(false);
          return;
        }

        console.log('DOCUMENTS PAGE: Retrieved OneDrive items:', oneDriveItems.length);

        // Map OneDrive items to document format
        const oneDriveDocuments = oneDriveItems.map(item => {
          // Check if this OneDrive item has an association in our documents array
          // We'll use a function parameter in setDocuments instead of a dependency
          const existingAssociation = documents.find(
            doc => doc.source === 'onedrive' && doc.externalId === item.id
          );

          // Get file extension safely
          const fileExtension = item.name.includes('.')
            ? item.name.split('.').pop().toLowerCase()
            : '';

          // Format size safely
          const fileSize = item.size ? formatFileSize(item.size) : '0 Bytes';

          // Create a download URL if it's not included in the response
          let downloadUrl = item["@microsoft.graph.downloadUrl"];

          // If no download URL is available, create a URL that will trigger a download when clicked
          if (!downloadUrl && !item.folder) {
            downloadUrl = `/api/documents/download?itemId=${item.id}&source=onedrive&name=${encodeURIComponent(item.name)}`;
          }

          return {
            id: existingAssociation?.id || item.id,
            name: item.name,
            type: fileExtension,
            size: fileSize,
            sizeBytes: item.size || 0,
            uploadedAt: item.lastModifiedDateTime || new Date().toISOString(),
            uploadedBy: user?.name || 'You',
            source: 'onedrive',
            folder: item.parentReference?.path?.replace("/drive/root:", "") || "/",
            url: item.webUrl,
            downloadUrl: downloadUrl,
            externalId: item.id,
            contactId: existingAssociation?.contactId || null,
            contactName: existingAssociation?.contactName || null,
            tags: existingAssociation?.tags || [], // Preserve existing tags
            isFolder: item.folder ? true : false
          };
        });

        console.log('DOCUMENTS PAGE: Mapped OneDrive documents:', oneDriveDocuments.length);

        // Filter out folders
        const files = oneDriveDocuments.filter(doc => !doc.isFolder);
        console.log('DOCUMENTS PAGE: OneDrive files (excluding folders):', files.length);

        // Add OneDrive documents to state
        setDocuments(prevDocs => {
          // Filter out existing OneDrive documents
          const filteredDocs = prevDocs.filter(doc => doc.source !== 'onedrive');

          // Preserve contact data for existing documents
          const filesWithContactData = files.map(file => {
            // Check if this file exists in previous documents with contact data
            const existingDoc = prevDocs.find(
              doc => doc.source === 'onedrive' && doc.externalId === file.externalId
            );

            if (existingDoc && existingDoc.contactsData) {
              // Preserve the contact data and tags
              return {
                ...file,
                contactId: existingDoc.contactId || file.contactId,
                contactName: existingDoc.contactName || file.contactName,
                contactsData: existingDoc.contactsData,
                tags: existingDoc.tags || file.tags || []
              };
            }

            return file;
          });

          return [...filteredDocs, ...filesWithContactData];
        });

        // Extract folders
        const folders = oneDriveDocuments.filter(doc => doc.isFolder).map(folder => ({
          id: folder.id,
          name: folder.name,
          source: 'onedrive'
        }));

        console.log('DOCUMENTS PAGE: OneDrive folders:', folders.length);
        setOneDriveFolders(folders);

        // Mark as fetched to prevent duplicate fetches
        oneDriveFetched.current = true;
      } catch (error) {
        console.error('DOCUMENTS PAGE: Failed to fetch OneDrive documents:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOneDriveDocuments();
  }, [msGraphAuthenticated, selectedFolder, oneDrive, user]);

  // Fetch SharePoint sites when authenticated
  useEffect(() => {
    const fetchSharePointSites = async () => {
      if (!msGraphAuthenticated || selectedFolder !== 'sharepoint') return;

      // Use a ref to prevent duplicate fetches
      if (sharePointFetched.current) return;

      console.log('DOCUMENTS PAGE: Fetching SharePoint sites');
      setLoading(true);

      try {
        // Get SharePoint sites
        const sites = await sharePoint.getSites();

        if (sites) {
          setSharePointSites(sites.map(site => ({
            id: site.id,
            name: site.displayName,
            url: site.webUrl
          })));

          // Select first site by default
          if (sites.length > 0 && !selectedSite) {
            setSelectedSite(sites[0].id);
          }

          // Mark as fetched to prevent duplicate fetches
          sharePointFetched.current = true;
        }
      } catch (error) {
        console.error('Failed to fetch SharePoint sites:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSharePointSites();
  }, [msGraphAuthenticated, selectedFolder, sharePoint]);

  // Fetch SharePoint libraries when site is selected
  useEffect(() => {
    const fetchSharePointLibraries = async () => {
      if (!msGraphAuthenticated || !selectedSite) return;

      // Use a ref to prevent duplicate fetches
      if (sharePointLibrariesFetched.current) return;

      console.log('DOCUMENTS PAGE: Fetching SharePoint libraries for site:', selectedSite);
      setLoading(true);

      try {
        // Get SharePoint libraries
        const libraries = await sharePoint.getLibraries(selectedSite);

        if (libraries) {
          setSharePointLibraries(libraries.map(library => ({
            id: library.id,
            name: library.name,
            driveType: library.driveType
          })));

          // Select first library by default
          if (libraries.length > 0 && !selectedLibrary) {
            setSelectedLibrary(libraries[0].id);
          }

          // Mark as fetched to prevent duplicate fetches
          sharePointLibrariesFetched.current = true;
        }
      } catch (error) {
        console.error('Failed to fetch SharePoint libraries:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSharePointLibraries();
  }, [msGraphAuthenticated, selectedSite, sharePoint]);

  // Fetch SharePoint documents when library is selected
  useEffect(() => {
    const fetchSharePointDocuments = async () => {
      if (!msGraphAuthenticated || !selectedLibrary) return;

      // Use a ref to prevent duplicate fetches
      if (sharePointDocumentsFetched.current) return;

      console.log('DOCUMENTS PAGE: Fetching SharePoint documents for library:', selectedLibrary);
      setLoading(true);

      try {
        // Get SharePoint library items
        const libraryItems = await sharePoint.getLibraryItems(selectedLibrary);

        if (libraryItems) {
          // Map SharePoint items to document format
          const sharePointDocuments = libraryItems.map(item => {
            // Check if this SharePoint item has an association in our documents array
            // We'll use the current documents state directly in the function
            const existingAssociation = documents.find(
              doc => doc.source === 'sharepoint' && doc.externalId === item.id && doc.driveId === selectedLibrary
            );

            // Create a download URL if it's not included in the response
            let downloadUrl = item["@microsoft.graph.downloadUrl"];

            // If no download URL is available, create a URL that will trigger a download when clicked
            if (!downloadUrl && !item.isFolder) {
              downloadUrl = `/api/documents/download?driveId=${selectedLibrary}&itemId=${item.id}&source=sharepoint&name=${encodeURIComponent(item.name)}`;
            }

            return {
              id: existingAssociation?.id || item.id,
              name: item.name,
              type: item.name.split('.').pop().toLowerCase(),
              size: formatFileSize(item.size),
              sizeBytes: item.size || 0,
              uploadedAt: item.lastModifiedDateTime,
              uploadedBy: user?.name || 'SharePoint',
              source: 'sharepoint',
              folder: '/',
              url: item.webUrl,
              downloadUrl: downloadUrl,
              externalId: item.id,
              driveId: selectedLibrary,
              contactId: existingAssociation?.contactId || null,
              contactName: existingAssociation?.contactName || null,
              tags: existingAssociation?.tags || [], // Preserve existing tags
              isFolder: item.folder ? true : false
            };
          });

          // Filter out folders
          const files = sharePointDocuments.filter(doc => !doc.isFolder);

          // Add SharePoint documents to state
          setDocuments(prevDocs => {
            // Filter out existing SharePoint documents
            const filteredDocs = prevDocs.filter(doc => doc.source !== 'sharepoint');

            // Preserve contact data for existing documents
            const filesWithContactData = files.map(file => {
              // Check if this file exists in previous documents with contact data
              const existingDoc = prevDocs.find(
                doc => doc.source === 'sharepoint' && doc.externalId === file.externalId
              );

              if (existingDoc && existingDoc.contactsData) {
                // Preserve the contact data and tags
                return {
                  ...file,
                  contactId: existingDoc.contactId || file.contactId,
                  contactName: existingDoc.contactName || file.contactName,
                  contactsData: existingDoc.contactsData,
                  tags: existingDoc.tags || file.tags || []
                };
              }

              return file;
            });

            return [...filteredDocs, ...filesWithContactData];
          });

          // Mark as fetched to prevent duplicate fetches
          sharePointDocumentsFetched.current = true;
        }
      } catch (error) {
        console.error('Failed to fetch SharePoint documents:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSharePointDocuments();
  }, [msGraphAuthenticated, selectedLibrary, sharePoint]);

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Handle document tag updates
  const handleDocumentTagsUpdated = (updatedDocument) => {
    setDocuments(prevDocs =>
      prevDocs.map(doc =>
        doc.id === updatedDocument.id
          ? { ...doc, tags: updatedDocument.tags }
          : doc
      )
    );
  };

  // Debounce function to prevent multiple rapid calls
  const debounce = (func, delay) => {
    let timeoutId;
    return (...args) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };

  // Handle folder selection with debounce
  const handleFolderSelectImpl = useCallback((folderId) => {
    // If we're switching to a different folder, reset the fetch status
    if (folderId !== selectedFolder) {
      // Reset fetch status based on the folder being selected
      if (folderId === 'onedrive') {
        oneDriveFetched.current = false;
      } else if (folderId === 'sharepoint') {
        sharePointFetched.current = false;
        sharePointLibrariesFetched.current = false;
        sharePointDocumentsFetched.current = false;
      } else {
        // For "all" and "recent", reset all
        oneDriveFetched.current = false;
        sharePointFetched.current = false;
        sharePointLibrariesFetched.current = false;
        sharePointDocumentsFetched.current = false;
      }
      console.log(`DOCUMENTS PAGE: Switching to folder: ${folderId}`);
    }

    setSelectedFolder(folderId);

    // Reset site and library selection when changing folders
    if (folderId !== 'sharepoint') {
      setSelectedSite(null);
      setSelectedLibrary(null);
    }
  }, [selectedFolder]);

  // Debounced version of the folder selection handler (300ms delay)
  const handleFolderSelect = useCallback(
    debounce((folderId) => handleFolderSelectImpl(folderId), 300),
    [handleFolderSelectImpl]
  );



  // Filter documents based on search term, selected contact, selected folder, and source
  const filteredDocuments = (documents || []).filter(doc => {
    const matchesSearch = searchTerm === '' ||
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doc.contactName && doc.contactName.toLowerCase().includes(searchTerm.toLowerCase()));

    // Improved contact matching for multiple contacts
    const matchesContact = selectedContact === 'all' ||
      (doc.contactId && (
        doc.contactId === selectedContact ||
        (doc.contactId.includes(',') && doc.contactId.split(',').includes(selectedContact))
      ));

    const matchesFolder = selectedFolder === 'all' ||
      (selectedFolder === 'onedrive' && doc.source === 'onedrive') ||
      (selectedFolder === 'sharepoint' && doc.source === 'sharepoint') ||
      (selectedFolder === 'recent' && new Date(doc.uploadedAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000));

    const matchesSource = selectedSource === 'all' || doc.source === selectedSource;

    // Tag filtering - document must have all selected tags
    const safeSelectedTags = Array.isArray(selectedTags) ? selectedTags : [];
    const matchesTags = safeSelectedTags.length === 0 ||
      (doc.tags && Array.isArray(doc.tags) && safeSelectedTags.every(tag => doc.tags.includes(tag)));

    return matchesSearch && matchesContact && matchesFolder && matchesSource && matchesTags;
  });

  // Sort documents
  const sortedDocuments = [...filteredDocuments].sort((a, b) => {
    const direction = sortConfig.direction === 'asc' ? 1 : -1;

    if (sortConfig.key === 'uploadedAt') {
      return direction * (new Date(a.uploadedAt) - new Date(b.uploadedAt));
    }

    if (sortConfig.key === 'name') {
      return direction * a.name.localeCompare(b.name);
    }

    if (sortConfig.key === 'size') {
      const sizeA = parseFloat(a.size.split(' ')[0]);
      const sizeB = parseFloat(b.size.split(' ')[0]);
      return direction * (sizeA - sizeB);
    }

    return 0;
  });

  // Get file icon based on file type
  const getFileIcon = (fileType) => {
    // Use Lucide React icons for a more modern look
    switch (fileType) {
      case 'pdf':
        return <FileText className="h-6 w-6 text-red-500 dark:text-red-400" />;
      case 'doc':
      case 'docx':
        return <FileText className="h-6 w-6 text-blue-500 dark:text-blue-400" />;
      case 'xls':
      case 'xlsx':
        return <Table className="h-6 w-6 text-green-500 dark:text-green-400" />;
      case 'ppt':
      case 'pptx':
        return <FileType className="h-6 w-6 text-orange-500 dark:text-orange-400" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
        return <Image className="h-6 w-6 text-purple-500 dark:text-purple-400" />;
      case 'txt':
        return <FileText className="h-6 w-6 text-gray-500 dark:text-gray-400" />;
      default:
        return <File className="h-6 w-6 text-gray-500 dark:text-gray-400" />;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Toggle sort direction
  const toggleSort = (key) => {
    if (sortConfig.key === key) {
      setSortConfig({
        key,
        direction: sortConfig.direction === 'asc' ? 'desc' : 'asc'
      });
    } else {
      setSortConfig({
        key,
        direction: 'asc'
      });
    }
  };

  // Handle upload completion
  const handleUploadComplete = async () => {
    // Reset all fetch statuses to force a refresh
    oneDriveFetched.current = false;
    sharePointFetched.current = false;
    sharePointLibrariesFetched.current = false;
    sharePointDocumentsFetched.current = false;

    // Refresh documents from API
    setLoading(true);
    try {
      const response = await fetch('/api/documents');
      if (response.ok) {
        const apiDocuments = await response.json();

        // Preserve contact data for existing documents
        setDocuments(prevDocs => {
          // Map through new documents and preserve contact data where possible
          return apiDocuments.map(newDoc => {
            // Check if this document exists in previous documents with contact data
            const existingDoc = prevDocs.find(
              doc => doc.externalId === newDoc.externalId && doc.source === newDoc.source
            );

            if (existingDoc && existingDoc.contactsData && (!newDoc.contactsData || newDoc.contactsData.length === 0)) {
              // Preserve the contact data and tags
              return {
                ...newDoc,
                contactId: existingDoc.contactId || newDoc.contactId,
                contactName: existingDoc.contactName || newDoc.contactName,
                contactsData: existingDoc.contactsData,
                tags: newDoc.tags || existingDoc.tags || []
              };
            }

            return newDoc;
          });
        });
      }
    } catch (error) {
      console.error('Failed to refresh documents:', error);
    } finally {
      setLoading(false);
    }
  };

  // Check if token is expired and handle token refresh
  useEffect(() => {
    if (authError && authError.message && authError.message.includes("token")) {
      console.log("Token expired, showing alert");

      // Automatically try to refresh the token once
      const refreshToken = async () => {
        try {
          console.log("Attempting to refresh token automatically");
          // Use msGraphLogin to refresh the token
          msGraphLogin();
        } catch (error) {
          console.error("Failed to refresh token automatically:", error);
        }
      };

      refreshToken();
    }
  }, [authError, msGraphLogin]);

  // Handle updating document contacts
  const handleUpdateDocumentContacts = async (contactIds) => {
    if (!documentToManage) return;

    // Check if Microsoft Graph is authenticated before proceeding
    if (!msGraphAuthenticated || authError) {
      console.error('Microsoft Graph not authenticated or has error:', authError);
      alert('Microsoft authentication is required. Please reconnect to Microsoft and try again.');
      return;
    }

    try {
      // We've already checked authentication status above, so we can proceed
      // Determine which ID to use - prefer database ID if available, fall back to externalId
      const documentId = documentToManage.id;
      const externalId = documentToManage.externalId;

      console.log("Document to manage:", {
        id: documentId,
        externalId: externalId,
        name: documentToManage.name,
        source: documentToManage.source
      });

      console.log("Sending contact update request for document:", documentId, "with contacts:", contactIds);

      // Use the database ID for the API call
      const response = await fetch(`/api/documents/${documentId}/contacts`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contactIds,
          // Include all document information needed to create new associations
          externalId: externalId,
          source: documentToManage.source,
          name: documentToManage.name,
          fileType: documentToManage.fileType,
          fileSize: documentToManage.fileSize,
          uploadedAt: documentToManage.uploadedAt,
          uploadedBy: documentToManage.uploadedBy,
          folder: documentToManage.folder,
          url: documentToManage.url,
          driveId: documentToManage.driveId
        }),
      });

      if (!response.ok) {
        // Check if it's an authentication error
        if (response.status === 401) {
          throw new Error('Your session has expired. Please reconnect to Microsoft and try again.');
        }

        // Try to get more detailed error information
        let errorMessage = 'Failed to update document contacts';
        try {
          const errorData = await response.json();
          if (errorData && errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (e) {
          console.error("Could not parse error response:", e);
        }

        throw new Error(errorMessage);
      }

      // Log the successful response
      try {
        const responseData = await response.json();
        console.log("Contact update successful:", responseData);
      } catch (e) {
        console.error("Could not parse success response:", e);
      }

      // Refresh documents to show updated associations
      await handleUploadComplete();
    } catch (error) {
      console.error('Error updating document contacts:', error);
      alert(`Failed to update contact associations: ${error.message}`);
      throw error;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Loading Indicator */}
      {loading && (
        <div className="fixed top-0 left-0 w-full h-1 bg-primary-light overflow-hidden z-50">
          <div className="animate-progress-bar h-full bg-primary"></div>
        </div>
      )}

      {/* Microsoft Authentication Alert */}
      {!msGraphAuthenticated && authError && (
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700 dark:text-yellow-200">
                Microsoft authentication is required to access OneDrive and SharePoint documents.
              </p>
              <div className="mt-2">
                <button
                  onClick={msGraphLogin}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                >
                  Connect to Microsoft
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Token Expiration Alert */}
      {msGraphAuthenticated && authError && authError.message && authError.message.includes("token") && (
        <div className="bg-orange-50 dark:bg-orange-900/30 border-l-4 border-orange-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-orange-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                Microsoft Session Expired
              </p>
              <p className="text-sm text-orange-700 dark:text-orange-300 mt-1">
                Your Microsoft access token has expired. Please reconnect to continue managing documents.
              </p>
              <div className="mt-2">
                <button
                  onClick={() => msGraphLogin()}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                >
                  Reconnect to Microsoft
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Upload Document Modal */}
      <UploadDocumentModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        oneDrive={oneDrive}
        sharePoint={sharePoint}
        contacts={contacts}
        selectedFolder={selectedFolder}
        selectedSite={selectedSite}
        selectedLibrary={selectedLibrary}
        onUploadComplete={handleUploadComplete}
        user={user}
      />

      {/* Delete Document Confirmation Modal */}
      <DeleteDocumentModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        documentName={documentToDelete?.name || ''}
        onConfirm={async () => {
          if (!documentToDelete) return;

          try {
            // First delete from OneDrive/SharePoint if applicable
            if (documentToDelete.source === 'onedrive') {
              await oneDrive.deleteFile(documentToDelete.externalId || documentToDelete.id);
            } else if (documentToDelete.source === 'sharepoint') {
              await sharePoint.deleteFile(documentToDelete.driveId, documentToDelete.externalId || documentToDelete.id);
            }

            // Then delete all instances from the database
            // This handles the case where a document is associated with multiple contacts
            const response = await fetch(`/api/documents/external/${documentToDelete.externalId || documentToDelete.id}?source=${documentToDelete.source}`, {
              method: 'DELETE',
            });

            if (!response.ok) {
              throw new Error('Failed to delete document from database');
            }

            // Remove all instances of this document from the UI
            // This removes all documents with the same externalId (same file with different contact associations)
            setDocuments(prevDocs => prevDocs.filter(d =>
              d.externalId !== documentToDelete.externalId ||
              d.source !== documentToDelete.source
            ));
          } catch (error) {
            console.error('Failed to delete document:', error);
            alert('Failed to delete document. Please try again.');
          }
        }}
      />

      {/* Manage Contacts Modal */}
      <ManageContactsModal
        isOpen={manageContactsModalOpen}
        onClose={() => setManageContactsModalOpen(false)}
        document={documentToManage}
        allContacts={contacts}
        onSave={handleUpdateDocumentContacts}
      />

      {/* Header Section */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <h1 className="text-2xl font-semibold">Document Management</h1>
          <Tooltip content="Show help information">
            <button
              onClick={() => setShowHelpBanner(true)}
              className="ml-2 text-gray-400 hover:text-primary dark:text-gray-500 dark:hover:text-primary-light focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-900 rounded-full p-1"
            >
              <Info className="h-4 w-4" />
            </button>
          </Tooltip>
        </div>
        <div className="flex items-center space-x-3">
          <Tooltip content="Upload documents to OneDrive or SharePoint">
            <button
              onClick={() => setIsUploadModalOpen(true)}
              className="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover text-sm"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Document
            </button>
          </Tooltip>
        </div>
      </div>

      {/* Help banner */}
      {showHelpBanner && (
        <div className="mb-6 bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-start space-x-3">
            <div className="text-primary dark:text-primary-light mt-0.5">
              <Info className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Document Management</h3>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                <p>
                  This document management system integrates with Microsoft OneDrive and SharePoint.
                  You can upload, download, and manage documents directly from your account.
                </p>
                <p className="text-green-600 dark:text-green-400 mt-1">
                  ✓ Connected as: {user?.name || 'Authenticated User'}
                </p>

                <div className="mt-2 flex items-center">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {msGraphAuthenticated ?
                      '✓ Microsoft services connected' :
                      'Connecting to Microsoft services...'}
                  </span>
                </div>
              </div>
              {authError && (
                <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                  Error: {authError.message || 'Failed to connect to Microsoft services'}
                </p>
              )}
            </div>
            <button
              className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              onClick={() => setShowHelpBanner(false)}
            >
              <span className="sr-only">Close</span>
              <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col lg:flex-row gap-6 min-h-0">
        {/* Left Sidebar - Folders */}
        <div className="lg:w-64 overflow-y-auto flex flex-col">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex-1 flex flex-col">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Folders</h3>
            <ul className="space-y-1">
              {folders.map(folder => (
                <li key={folder.id}>
                  <Tooltip content={`View ${folder.name}`}>
                    <button
                      className={`w-full text-left px-3 py-2 rounded-md text-sm ${
                        selectedFolder === folder.id
                          ? 'bg-primary/10 text-primary dark:bg-primary/20'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                      onClick={() => handleFolderSelect(folder.id)}
                    >
                      {folder.name}
                      {loading && selectedFolder === folder.id && (
                        <span className="ml-2 inline-block animate-pulse">•••</span>
                      )}
                    </button>
                  </Tooltip>
                </li>
              ))}
            </ul>

            {/* OneDrive Folders */}
            {selectedFolder === 'onedrive' && (
              <div className="mt-4">
                <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <span className="inline-block w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                  OneDrive Folders
                </h4>
                {!msGraphAuthenticated ? (
                  <div>
                    {authError ? (
                      <div className="mb-2">
                        <p className="text-xs text-red-500 dark:text-red-400 mb-1">
                          {authError.message || "Failed to connect to Microsoft services"}
                        </p>
                        <button
                          onClick={msGraphLogin}
                          className="text-xs bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded"
                        >
                          Connect to Microsoft
                        </button>
                      </div>
                    ) : (
                      <p className="text-xs text-gray-500 dark:text-gray-400 italic mb-2">
                        Connecting to Microsoft services...
                      </p>
                    )}
                  </div>
                ) : oneDriveFolders.length === 0 ? (
                  <p className="text-xs text-gray-500 dark:text-gray-400 italic">
                    No folders found in OneDrive
                  </p>
                ) : (
                  <ul className="space-y-1 pl-5">
                    {oneDriveFolders.map(folder => (
                      <li key={folder.id}>
                        <button
                          className="w-full text-left px-2 py-1 rounded-md text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                          onClick={() => {
                            // In a future implementation, this would navigate to the folder
                            console.log('Navigate to folder:', folder.id);
                            // For now, just show a message
                            alert('Folder navigation will be implemented in a future update.');
                          }}
                        >
                          {folder.name}
                        </button>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            )}

            {/* SharePoint Sites */}
            {selectedFolder === 'sharepoint' && (
              <div className="mt-4">
                <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                  SharePoint Sites
                </h4>
                {!msGraphAuthenticated ? (
                  <div>
                    {authError ? (
                      <div className="mb-2">
                        <p className="text-xs text-red-500 dark:text-red-400 mb-1">
                          {authError.message || "Failed to connect to Microsoft services"}
                        </p>
                        <button
                          onClick={msGraphLogin}
                          className="text-xs bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded"
                        >
                          Connect to Microsoft
                        </button>
                      </div>
                    ) : (
                      <p className="text-xs text-gray-500 dark:text-gray-400 italic mb-2">
                        Connecting to Microsoft services...
                      </p>
                    )}
                  </div>
                ) : sharePointSites.length === 0 ? (
                  <p className="text-xs text-gray-500 dark:text-gray-400 italic">
                    No sites found in SharePoint
                  </p>
                ) : (
                  <ul className="space-y-1 pl-2">
                    {sharePointSites.map(site => (
                      <li key={site.id}>
                        <button
                          className={`w-full text-left px-2 py-1 rounded-md text-xs ${
                            selectedSite === site.id
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                          }`}
                          onClick={() => {
                            // Reset the libraries fetch status when selecting a different site
                            if (site.id !== selectedSite) {
                              sharePointLibrariesFetched.current = false;
                            }
                            setSelectedSite(site.id);
                          }}
                        >
                          {site.name}
                          {loading && selectedSite === site.id && (
                            <span className="ml-2 inline-block animate-pulse">•••</span>
                          )}
                        </button>

                        {/* Libraries for selected site */}
                        {selectedSite === site.id && sharePointLibraries.length > 0 && (
                          <ul className="mt-1 space-y-1 pl-4">
                            {sharePointLibraries.map(library => (
                              <li key={library.id}>
                                <button
                                  className={`w-full text-left px-2 py-1 rounded-md text-xs ${
                                    selectedLibrary === library.id
                                      ? 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300'
                                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                                  }`}
                                  onClick={() => {
                                    // Reset the documents fetch status when selecting a different library
                                    if (library.id !== selectedLibrary) {
                                      sharePointDocumentsFetched.current = false;
                                    }
                                    setSelectedLibrary(library.id);
                                  }}
                                >
                                  {library.name}
                                  {loading && selectedLibrary === library.id && (
                                    <span className="ml-2 inline-block animate-pulse">•••</span>
                                  )}
                                </button>
                              </li>
                            ))}
                          </ul>
                        )}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            )}

            <h3 className="text-sm font-medium text-gray-900 dark:text-white mt-6 mb-3">Filter by Contact</h3>
            <div className="mb-3">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search contacts..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  value={contactSearchTerm}
                  onChange={(e) => setContactSearchTerm(e.target.value)}
                />
                <Search className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>
            <ul className="space-y-1 max-h-96 overflow-y-auto flex-1">
              <li>
                <button
                  className={`w-full text-left px-3 py-2 rounded-md text-sm ${
                    selectedContact === 'all'
                      ? 'bg-primary/10 text-primary dark:bg-primary/20'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                  onClick={() => setSelectedContact('all')}
                >
                  All Contacts
                </button>
              </li>
              {contacts
                .filter(contact =>
                  contact.name.toLowerCase().includes(contactSearchTerm.toLowerCase())
                )
                .map(contact => (
                  <li key={contact.id}>
                    <button
                      className={`w-full text-left px-3 py-2 rounded-md text-sm ${
                        selectedContact === contact.id
                          ? 'bg-primary/10 text-primary dark:bg-primary/20'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                      onClick={() => setSelectedContact(contact.id)}
                    >
                      {contact.name}
                    </button>
                  </li>
                ))}
            </ul>
          </div>
        </div>

        {/* Right Content - Documents */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* Toolbar */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="relative w-full sm:w-80">
                <input
                  type="text"
                  placeholder="Search documents..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <Search className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>

              <div className="flex items-center space-x-3">
                <Tooltip content="Filter documents by source">
                  <select
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    value={selectedSource}
                    onChange={(e) => setSelectedSource(e.target.value)}
                  >
                    <option value="all">All Sources</option>
                    <option value="onedrive">OneDrive</option>
                    <option value="sharepoint">SharePoint</option>
                  </select>
                </Tooltip>

                {/* Tag Filter */}
                <div className="relative">
                  <Tooltip content="Filter documents by tags">
                    <div className="min-w-48">
                      <DocumentTagSelector
                        value={selectedTags}
                        onChange={setSelectedTags}
                        className="text-sm"
                      />
                    </div>
                  </Tooltip>
                </div>

                <div className="flex items-center space-x-1">
                  <Tooltip content="Sort by name">
                    <button
                      className="flex items-center px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
                      onClick={() => toggleSort('name')}
                    >
                      Name
                      {sortConfig.key === 'name' && (
                        sortConfig.direction === 'asc' ?
                          <SortAsc className="h-3 w-3 ml-1 text-primary" /> :
                          <SortDesc className="h-3 w-3 ml-1 text-primary" />
                      )}
                    </button>
                  </Tooltip>
                  <Tooltip content="Sort by date">
                    <button
                      className="flex items-center px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
                      onClick={() => toggleSort('uploadedAt')}
                    >
                      Date
                      {sortConfig.key === 'uploadedAt' && (
                        sortConfig.direction === 'asc' ?
                          <SortAsc className="h-3 w-3 ml-1 text-primary" /> :
                          <SortDesc className="h-3 w-3 ml-1 text-primary" />
                      )}
                    </button>
                  </Tooltip>
                </div>

                <div className="flex items-center space-x-1 border border-gray-300 dark:border-gray-600 rounded-md">
                  <Tooltip content="Grid view">
                    <button
                      className={`p-2 ${viewMode === 'grid' ? 'bg-gray-100 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'}`}
                      onClick={() => setViewMode('grid')}
                    >
                      <Grid className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                    </button>
                  </Tooltip>
                  <Tooltip content="List view">
                    <button
                      className={`p-2 ${viewMode === 'list' ? 'bg-gray-100 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'}`}
                      onClick={() => setViewMode('list')}
                    >
                      <List className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                    </button>
                  </Tooltip>
                </div>

                
              </div>
            </div>
          </div>

          {/* Documents List/Grid */}
          <div className="flex-1 overflow-auto bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            {loading ? (
              <div className="flex justify-center items-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <>
                {sortedDocuments.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
                    <div className="text-5xl mb-4">📄</div>
                    <p className="text-lg font-medium">No documents found</p>
                    <p className="text-sm mt-1">Upload a document or change your search criteria</p>
                  </div>
                ) : (
                  viewMode === 'grid' ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                      {sortedDocuments.map((doc) => (
                        <div
                          key={doc.id}
                          className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200 flex flex-col h-full"
                        >
                          <div className="p-4 flex-1">
                            <div className="flex items-center space-x-3">
                              <div className="flex-shrink-0">
                                {getFileIcon(doc.type)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <Tooltip content={doc.name}>
                                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    {doc.name}
                                  </p>
                                </Tooltip>
                                <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                  {doc.contactsData && doc.contactsData.length > 0 ? (
                                    <Tooltip content={
                                      <div className="text-xs">
                                        <div className="font-medium mb-1">Associated with:</div>
                                        <ul className="list-disc pl-4">
                                          {doc.contactsData.map(contact => (
                                            <li key={contact.id}>{contact.name || `Contact ID: ${contact.id}`}</li>
                                          ))}
                                        </ul>
                                      </div>
                                    }>
                                      <span className="text-primary dark:text-primary-light">
                                        {doc.contactsData.map(c => c.name).filter(Boolean).join(', ') || 'No contact'}
                                      </span>
                                    </Tooltip>
                                  ) : 'No contact'} • {doc.size}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {new Date(doc.uploadedAt).toLocaleDateString()}
                                </div>
                                {/* Document Tags */}
                                {doc.tags && Array.isArray(doc.tags) && doc.tags.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mt-2">
                                    {doc.tags.slice(0, 2).map((tag, index) => (
                                      <span
                                        key={index}
                                        className="inline-flex px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 rounded-full"
                                      >
                                        {tag}
                                      </span>
                                    ))}
                                    {doc.tags.length > 2 && (
                                      <Tooltip content={
                                        <div className="text-xs">
                                          <div className="font-medium mb-1">All tags:</div>
                                          <div className="flex flex-wrap gap-1">
                                            {doc.tags.map((tag, index) => (
                                              <span key={index} className="inline-block px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                                                {tag}
                                              </span>
                                            ))}
                                          </div>
                                        </div>
                                      }>
                                        <span className="inline-flex px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-full cursor-help">
                                          +{doc.tags.length - 2}
                                        </span>
                                      </Tooltip>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 grid grid-cols-4 gap-1 mt-auto">
                            <div className="flex justify-start">
                              <Tooltip content="Download this document">
                                <button
                                  type="button"
                                  className="text-xs text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white flex items-center"
                                  onClick={() => {
                                    try {
                                      const a = document.createElement('a');
                                      a.href = doc.downloadUrl;
                                      a.style.display = 'none';
                                      try {
                                        const urlObj = new URL(doc.downloadUrl, window.location.origin);
                                        if (urlObj.origin === window.location.origin) {
                                          a.setAttribute('download', doc.name || 'document');
                                        }
                                      } catch {}
                                      document.body.appendChild(a);
                                      a.click();
                                      setTimeout(() => document.body.removeChild(a), 100);
                                    } catch {
                                      window.open(doc.downloadUrl, '_blank', 'noopener,noreferrer');
                                    }
                                  }}
                                  aria-label={`Download ${doc.name}`}
                                >
                                  <Download className="h-3 w-3 mr-1" />
                                  Download
                                </button>
                              </Tooltip>
                            </div>
                            <div className="flex justify-center">
                              <Tooltip content="Manage contact associations">
                                <button
                                  className="text-xs text-primary hover:text-primary-hover flex items-center"
                                  onClick={() => {
                                    setDocumentToManage(doc);
                                    setManageContactsModalOpen(true);
                                  }}
                                >
                                  <Users className="h-3 w-3 mr-1" />
                                  Contacts
                                </button>
                              </Tooltip>
                            </div>
                            <div className="flex justify-center">
                              <Tooltip content="Edit document tags">
                                <button
                                  className="text-xs text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 flex items-center"
                                  onClick={() => {
                                    setDocumentToEditTags(doc);
                                    setEditTagsModalOpen(true);
                                  }}
                                >
                                  <Tag className="h-3 w-3 mr-1" />
                                  Tags
                                </button>
                              </Tooltip>
                            </div>
                            <div className="flex justify-end">
                              <Tooltip content="Delete this document">
                                <button
                                  className="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 flex items-center"
                                  onClick={() => {
                                    setDocumentToDelete(doc);
                                    setDeleteModalOpen(true);
                                  }}
                                >
                                  <Trash2 className="h-3 w-3 mr-1" />
                                  Delete
                                </button>
                              </Tooltip>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              <button
                                className="flex items-center"
                                onClick={() => toggleSort('name')}
                              >
                                Name
                                {sortConfig.key === 'name' && (
                                  sortConfig.direction === 'asc' ?
                                    <SortAsc className="h-3 w-3 ml-1 text-primary" /> :
                                    <SortDesc className="h-3 w-3 ml-1 text-primary" />
                                )}
                              </button>
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Contact
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              <button
                                className="flex items-center"
                                onClick={() => toggleSort('uploadedAt')}
                              >
                                Date
                                {sortConfig.key === 'uploadedAt' && (
                                  sortConfig.direction === 'asc' ?
                                    <SortAsc className="h-3 w-3 ml-1 text-primary" /> :
                                    <SortDesc className="h-3 w-3 ml-1 text-primary" />
                                )}
                              </button>
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Size
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Source
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Tags
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                          {sortedDocuments.map((doc) => (
                            <tr key={doc.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div className="flex-shrink-0 mr-3">
                                    {getFileIcon(doc.type)}
                                  </div>
                                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                                    {doc.name}
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {doc.contactsData && doc.contactsData.length > 0 ? (
                                  <Tooltip content={
                                    <div className="text-xs">
                                      <div className="font-medium mb-1">Associated with:</div>
                                      <ul className="list-disc pl-4">
                                        {doc.contactsData.map(contact => (
                                          <li key={contact.id}>{contact.name || `Contact ID: ${contact.id}`}</li>
                                        ))}
                                      </ul>
                                    </div>
                                  }>
                                    <div className="text-sm text-primary dark:text-primary-light">
                                      {doc.contactsData.map(c => c.name).filter(Boolean).join(', ') || 'No contact'}
                                    </div>
                                  </Tooltip>
                                ) : (
                                  <div className="text-sm text-gray-500 dark:text-gray-400">No contact</div>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500 dark:text-gray-400">{formatDate(doc.uploadedAt)}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500 dark:text-gray-400">{doc.size}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  doc.source === 'onedrive'
                                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                                    : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                }`}>
                                  {doc.source === 'onedrive' ? 'OneDrive' : 'SharePoint'}
                                </span>
                              </td>
                              <td className="px-6 py-4">
                                {doc.tags && Array.isArray(doc.tags) && doc.tags.length > 0 ? (
                                  <div className="flex flex-wrap gap-1">
                                    {doc.tags.slice(0, 3).map((tag, index) => (
                                      <span
                                        key={index}
                                        className="inline-flex px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 rounded-full"
                                      >
                                        {tag}
                                      </span>
                                    ))}
                                    {doc.tags.length > 3 && (
                                      <Tooltip content={
                                        <div className="text-xs">
                                          <div className="font-medium mb-1">All tags:</div>
                                          <div className="flex flex-wrap gap-1">
                                            {doc.tags.map((tag, index) => (
                                              <span key={index} className="inline-block px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                                                {tag}
                                              </span>
                                            ))}
                                          </div>
                                        </div>
                                      }>
                                        <span className="inline-flex px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-full cursor-help">
                                          +{doc.tags.length - 3}
                                        </span>
                                      </Tooltip>
                                    )}
                                  </div>
                                ) : (
                                  <span className="text-xs text-gray-400 dark:text-gray-500">No tags</span>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <Tooltip content="Download this document">
                                  <button
                                    type="button"
                                    className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white mr-3 flex items-center"
                                    onClick={() => {
                                      try {
                                        const a = document.createElement('a');
                                        a.href = doc.downloadUrl;
                                        a.style.display = 'none';
                                        try {
                                          const urlObj = new URL(doc.downloadUrl, window.location.origin);
                                          if (urlObj.origin === window.location.origin) {
                                            a.setAttribute('download', doc.name || 'document');
                                          }
                                        } catch {}
                                        document.body.appendChild(a);
                                        a.click();
                                        setTimeout(() => document.body.removeChild(a), 100);
                                      } catch {
                                        window.open(doc.downloadUrl, '_blank', 'noopener,noreferrer');
                                      }
                                    }}
                                    aria-label={`Download ${doc.name}`}
                                  >
                                    <Download className="h-3.5 w-3.5 mr-1" />
                                    Download
                                  </button>
                                </Tooltip>
                                <Tooltip content="Manage contact associations">
                                  <button
                                    className="text-primary hover:text-primary-hover mr-3 flex items-center"
                                    onClick={() => {
                                      setDocumentToManage(doc);
                                      setManageContactsModalOpen(true);
                                    }}
                                  >
                                    <Users className="h-3.5 w-3.5 mr-1" />
                                    Contacts
                                  </button>
                                </Tooltip>
                                <Tooltip content="Edit document tags">
                                  <button
                                    className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3 flex items-center"
                                    onClick={() => {
                                      setDocumentToEditTags(doc);
                                      setEditTagsModalOpen(true);
                                    }}
                                  >
                                    <Tag className="h-3.5 w-3.5 mr-1" />
                                    Tags
                                  </button>
                                </Tooltip>
                                <Tooltip content="Delete this document">
                                  <button
                                    className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 flex items-center"
                                    onClick={() => {
                                      setDocumentToDelete(doc);
                                      setDeleteModalOpen(true);
                                    }}
                                  >
                                    <Trash2 className="h-3.5 w-3.5 mr-1" />
                                    Delete
                                  </button>
                                </Tooltip>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Upload Modal */}
      <UploadDocumentModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUploadComplete={() => {
          // Refresh documents after upload
          window.location.reload();
        }}
        selectedLibrary={selectedLibrary}
        contacts={contacts}
      />
      {/* Contacts fetch error display */}
      {contactsError && (
        <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded text-xs">
          {contactsError}
        </div>
      )}

      {/* Delete Modal */}
      <DeleteDocumentModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        document={documentToDelete}
        onDocumentDeleted={(deletedDocId) => {
          setDocuments(prevDocs => prevDocs.filter(doc => doc.id !== deletedDocId));
        }}
      />

      {/* Manage Contacts Modal */}
      <ManageContactsModal
        isOpen={manageContactsModalOpen}
        onClose={() => setManageContactsModalOpen(false)}
        document={documentToManage}
        onDocumentUpdated={(updatedDoc) => {
          setDocuments(prevDocs =>
            prevDocs.map(doc =>
              doc.id === updatedDoc.id ? updatedDoc : doc
            )
          );
        }}
      />

      {/* Edit Document Tags Modal */}
      <EditDocumentTagsModal
        isOpen={editTagsModalOpen}
        onClose={() => setEditTagsModalOpen(false)}
        document={documentToEditTags}
        onSave={handleDocumentTagsUpdated}
      />
    </div>
  );
}
