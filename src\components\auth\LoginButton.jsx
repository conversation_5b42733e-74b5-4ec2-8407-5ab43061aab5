"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

export default function LoginButton() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleLogin = async () => {
    setIsLoading(true);
    // This will redirect to Microsoft login page
    router.push("/api/auth/login");
  };

  return (
    <button
      onClick={handleLogin}
      disabled={isLoading}
      className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover disabled:opacity-50"
    >
      {isLoading ? "Signing In..." : "Sign In"}
    </button>
  );
}
