"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import { EditorProvider } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Image from "@tiptap/extension-image";
import { Link as LinkExtension } from "@tiptap/extension-link";
import TextAlign from "@tiptap/extension-text-align";
import TextStyle from "@tiptap/extension-text-style";
import Toolbar from "@/components/ui/editor/Toolbar";
import Preview from "@/components/ui/editor/Preview";
import "@/components/ui/editor/editor.css";

export default function EditTemplate() {
  const router = useRouter();
  const { id: templateId } = useParams();
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    subject: "",
    content: "",
  });
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  useEffect(() => {
    async function fetchTemplate() {
      try {
        const response = await fetch(`/api/email-templates/${templateId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch email template");
        }

        const template = await response.json();
        setFormData({
          name: template.name,
          description: template.description || "",
          subject: template.subject || "",
          content: template.content || "",
        });
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    }

    fetchTemplate();
  }, [templateId]);

  console.log("The form data: ", formData);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");

    try {
      const response = await fetch(`/api/email-templates/${templateId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData), // Send formData directly
      });

      if (!response.ok) {
        const data = await response.json();
        if (data.status === 409) {
          setError(
            "Template is being used in active campaigns. Cannot update."
          );
          return;
        }
        throw new Error(data.error || "Failed to update template");
      }

      router.push("/emails?tab=templates");
    } catch (err) {
      setError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Link
            href="/emails?tab=templates"
            className="mr-4 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
          >
            <span>← Back to Emails</span>
          </Link>
          <h1 className="text-2xl font-semibold">Edit Email Template</h1>
        </div>
        <button
          type="button"
          onClick={() => setIsPreviewMode(!isPreviewMode)}
          className="px-4 py-2 text-sm border rounded-md hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
        >
          {isPreviewMode ? "Edit Mode" : "Preview"}
        </button>
      </div>
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-red-600 dark:text-red-400 text-sm">
                {error}
              </div>
            </div>
            <button
              onClick={() => setError("")}
              className="text-red-400 hover:text-red-600 dark:text-red-300 dark:hover:text-red-100"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/50 text-red-600 dark:text-red-200 p-3 rounded-md">
            {error}
          </div>
        )}

        {!isPreviewMode && (
          <>
            <div>
              <label htmlFor="name" className="block text-sm font-medium mb-2">
                Template Name
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                required
              />
            </div>

            <div>
              <label
                htmlFor="description"
                className="block text-sm font-medium mb-2"
              >
                Description
              </label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                rows={2}
              />
            </div>

            <div>
              <label
                htmlFor="subject"
                className="block text-sm font-medium mb-2"
              >
                Email Subject
              </label>
              <input
                type="text"
                id="subject"
                value={formData.subject}
                onChange={(e) =>
                  setFormData({ ...formData, subject: e.target.value })
                }
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                required
              />
            </div>
          </>
        )}

        <div>
          {!isPreviewMode && (
            <label htmlFor="content" className="block text-sm font-medium mb-2">
              Email Content
            </label>
          )}
          <div className="rounded-lg border dark:border-gray-700 overflow-hidden">
            {isPreviewMode ? (
              <Preview content={formData.content} className="min-h-[300px]" />
            ) : (
              <EditorProvider
                extensions={[
                  StarterKit,
                  Image.configure({
                    HTMLAttributes: {
                      class: "rounded-lg max-w-full h-auto",
                    },
                    allowBase64: true,
                  }),
                  LinkExtension.configure({
                    openOnClick: false,
                    HTMLAttributes: {
                      class: "text-primary hover:text-primary-hover underline",
                    },
                  }),
                  TextAlign.configure({
                    types: ["heading", "paragraph"],
                  }),
                  TextStyle,
                ]}
                content={formData.content}
                editorProps={{
                  attributes: {
                    class: "ProseMirror p-4 min-h-[300px] focus:outline-none",
                  },
                }}
                onUpdate={({ editor }) => {
                  setFormData((prev) => ({
                    ...prev,
                    content: editor.getHTML(),
                  }));
                }}
                slotBefore={<Toolbar />}
              ></EditorProvider>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Link
            href="/emails?tab=templates"
            className="px-4 py-2 border rounded-md hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover disabled:opacity-50"
          >
            {isSubmitting ? "Saving..." : "Save Changes"}
          </button>
        </div>
      </form>
    </div>
  );
}
