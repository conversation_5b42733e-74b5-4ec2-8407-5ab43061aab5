"use client";

import { useState, useRef, useEffect } from "react";
import * as Dialog from '@radix-ui/react-dialog';
import {
  X,
  Upload,
  AlertCircle,
  Search,
  Plus,
  Trash2,
  Users,
} from "lucide-react";
import { FixedSizeList as List } from "react-window";
import DocumentGroupSelector from "./DocumentGroupSelector";
import DocumentTagSelector from "./DocumentTagSelector";

export default function UploadDocumentModal({
  isOpen,
  onClose,
  oneDrive,
  sharePoint,
  contacts,
  selectedFolder,
  selectedSite,
  selectedLibrary,
  onUploadComplete,
  user,
}) {
  const [file, setFile] = useState(null);
  const [destination, setDestination] = useState(
    selectedFolder === "sharepoint" ? "sharepoint" : "onedrive"
  );
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [selectedTags, setSelectedTags] = useState([]);
  const [contactSearchTerm, setContactSearchTerm] = useState("");
  const [showGroupSelector, setShowGroupSelector] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef(null);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFile(null);
      setDestination(
        selectedFolder === "sharepoint" ? "sharepoint" : "onedrive"
      );
      setSelectedContacts([]);
      setSelectedGroups([]);
      setSelectedTags([]);
      setContactSearchTerm("");
      setShowGroupSelector(false);
      setError(null);
      setProgress(0);
      setUploading(false);
    }
  }, [isOpen, selectedFolder]);

  // Handle group selection
  const handleGroupsChange = (groups) => {
    // Find which groups were removed (if any)
    const removedGroups = selectedGroups.filter(
      (prevGroup) => !groups.some((newGroup) => newGroup.id === prevGroup.id)
    );

    // Get contact IDs that should be removed (from removed groups)
    const contactsToRemove = removedGroups.flatMap(
      (group) => group.contacts?.map((contact) => contact.contactId) || []
    );

    // Update selected groups
    setSelectedGroups(groups);

    // If groups were removed, filter out their contacts
    if (removedGroups.length > 0) {
      // Remove contacts that are ONLY in the removed groups and not in any remaining selected groups
      const remainingGroupContactIds = groups.flatMap(
        (group) => group.contacts?.map((contact) => contact.contactId) || []
      );

      // Keep contacts that are either in remaining groups or were individually selected
      setSelectedContacts((prev) =>
        prev.filter(
          (contactId) =>
            // Keep if in remaining groups OR if it wasn't from a removed group
            remainingGroupContactIds.includes(contactId) ||
            !contactsToRemove.includes(contactId)
        )
      );
    } else {
      // No groups were removed, just add new contacts from added groups
      const newGroupContactIds = groups
        .filter(
          (group) =>
            !selectedGroups.some((prevGroup) => prevGroup.id === group.id)
        )
        .flatMap(
          (group) => group.contacts?.map((contact) => contact.contactId) || []
        );

      // Add new contacts (avoiding duplicates)
      const uniqueContactIds = [
        ...new Set([...selectedContacts, ...newGroupContactIds]),
      ];
      setSelectedContacts(uniqueContactIds);
    }
  };

  // Handle file selection
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
      setError(null);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!file) {
      setError("Please select a file to upload");
      return;
    }

    console.log(
      "UPLOAD MODAL: Starting upload process for file:",
      file.name,
      file.type,
      file.size
    );
    setUploading(true);
    setProgress(10);
    setError(null);

    try {
      // Validate file
      if (file.size === 0) {
        throw new Error("File is empty");
      }

      console.log("UPLOAD MODAL: Validating file is readable...");
      console.log(
        "UPLOAD MODAL: File type:",
        file.type,
        "File name:",
        file.name,
        "File size:",
        file.size
      );

      // Get file extension
      const fileExtension = file.name.split(".").pop().toLowerCase();
      console.log("UPLOAD MODAL: File extension:", fileExtension);

      // Create a file reader to check if the file is readable
      const reader = new FileReader();

      // Set up a promise to read the file
      const fileReadPromise = new Promise((resolve, reject) => {
        reader.onload = () => {
          console.log("UPLOAD MODAL: File read successful");
          resolve();
        };
        reader.onerror = (error) => {
          console.error("UPLOAD MODAL: File read error:", error);

          // Provide more specific error message based on file type
          const errorMessage = `Failed to read file. ${
            fileExtension ? `File type: .${fileExtension}.` : ""
          } Please ensure the file is not corrupted and try again.`;

          reject(new Error(errorMessage));
        };

        // Start reading a small slice of the file to verify it's readable
        try {
          // Try to read a slightly larger slice for complex file formats
          const sliceSize = ["docx", "xlsx", "pptx", "pdf"].includes(
            fileExtension
          )
            ? Math.min(file.size, 8192) // 8KB for complex formats
            : Math.min(file.size, 1024); // 1KB for other formats

          console.log(
            `UPLOAD MODAL: Reading file slice of size ${sliceSize} bytes`
          );
          reader.readAsArrayBuffer(file.slice(0, sliceSize));
        } catch (sliceError) {
          console.error("UPLOAD MODAL: Error slicing file:", sliceError);
          reject(
            new Error(
              `Failed to process file. Please try a different file or format.`
            )
          );
        }
      });

      // Wait for the file to be read
      try {
        await fileReadPromise;
        console.log("UPLOAD MODAL: File validation completed successfully");
      } catch (readError) {
        console.error("UPLOAD MODAL: File validation failed:", readError);
        throw readError;
      }

      setProgress(20);

      // Prepare the file for upload (especially important for text files)
      console.log("UPLOAD MODAL: Preparing file for upload...");
      const preparedFile = await prepareFileForUpload(file);
      console.log(
        "UPLOAD MODAL: File prepared:",
        preparedFile.name,
        preparedFile.type,
        preparedFile.size
      );

      setProgress(30);

      let uploadedFile;

      // Upload to OneDrive or SharePoint
      try {
        if (destination === "onedrive") {
          console.log("UPLOAD MODAL: Starting OneDrive upload...");

          // Check if we have the oneDrive object
          if (!oneDrive || typeof oneDrive.uploadFile !== "function") {
            console.error(
              "UPLOAD MODAL: OneDrive object is invalid:",
              oneDrive
            );
            throw new Error(
              "OneDrive service is not properly initialized. Please refresh the page and try again."
            );
          }

          // Get a fresh token before upload
          console.log("UPLOAD MODAL: Checking authentication before upload...");
          const authCheckResponse = await fetch("/api/auth/token");

          if (!authCheckResponse.ok) {
            console.error(
              "UPLOAD MODAL: Authentication check failed:",
              authCheckResponse.status,
              authCheckResponse.statusText
            );
            throw new Error(
              "Microsoft authentication error. Please log out and log back in to refresh your session."
            );
          }

          const authData = await authCheckResponse.json();
          if (!authData.accessToken) {
            console.error(
              "UPLOAD MODAL: No access token in authentication check response"
            );
            throw new Error(
              "Microsoft authentication error. Please log out and log back in to refresh your session."
            );
          }

          console.log(
            "UPLOAD MODAL: Authentication check passed, proceeding with upload"
          );
          uploadedFile = await oneDrive.uploadFile(preparedFile);
        } else if (destination === "sharepoint" && selectedLibrary) {
          console.log(
            "UPLOAD MODAL: Starting SharePoint upload to library:",
            selectedLibrary
          );

          // Check if we have the sharePoint object
          if (!sharePoint || typeof sharePoint.uploadFile !== "function") {
            console.error(
              "UPLOAD MODAL: SharePoint object is invalid:",
              sharePoint
            );
            throw new Error(
              "SharePoint service is not properly initialized. Please refresh the page and try again."
            );
          }

          // Get a fresh token before upload
          console.log("UPLOAD MODAL: Checking authentication before upload...");
          const authCheckResponse = await fetch("/api/auth/token");

          if (!authCheckResponse.ok) {
            console.error(
              "UPLOAD MODAL: Authentication check failed:",
              authCheckResponse.status,
              authCheckResponse.statusText
            );
            throw new Error(
              "Microsoft authentication error. Please log out and log back in to refresh your session."
            );
          }

          const authData = await authCheckResponse.json();
          if (!authData.accessToken) {
            console.error(
              "UPLOAD MODAL: No access token in authentication check response"
            );
            throw new Error(
              "Microsoft authentication error. Please log out and log back in to refresh your session."
            );
          }

          console.log(
            "UPLOAD MODAL: Authentication check passed, proceeding with upload"
          );
          uploadedFile = await sharePoint.uploadFile(
            selectedLibrary,
            preparedFile
          );
        } else {
          throw new Error("Invalid destination or missing library selection");
        }
      } catch (uploadError) {
        console.error("UPLOAD MODAL: Error during upload:", uploadError);

        // Check if this is an authentication error
        if (
          uploadError.message &&
          (uploadError.message.includes("authentication") ||
            uploadError.message.includes("Authentication") ||
            uploadError.message.includes("token") ||
            uploadError.message.includes("401") ||
            uploadError.message.includes("403"))
        ) {
          console.log("UPLOAD MODAL: Authentication error detected");

          // Show a more helpful error message
          throw new Error(
            "Microsoft authentication error. Please log out and log back in to refresh your session."
          );
        }

        // Re-throw the error for the main catch block to handle
        throw uploadError;
      }

      setProgress(70);

      console.log("UPLOAD MODAL: Upload completed, response:", uploadedFile);

      if (!uploadedFile) {
        console.error("UPLOAD MODAL: No file information returned from upload");
        throw new Error(
          "Upload completed but no file information was returned"
        );
      }

      // Verify that the uploaded file has the required properties
      if (!uploadedFile.id || !uploadedFile.webUrl) {
        console.error(
          "UPLOAD MODAL: Upload completed but file information is incomplete:",
          uploadedFile
        );
        throw new Error(
          "Upload completed but the response from Microsoft is incomplete. Please try again."
        );
      }

      // If contacts are selected, create associations in the database
      if (selectedContacts.length > 0) {
        const fileType = file.name.split(".").pop().toLowerCase();
        const fileSize = formatFileSize(file.size);

        // Create document records in database for each selected contact
        const createPromises = selectedContacts.map(async (contactId) => {
          const response = await fetch("/api/documents", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              name: file.name,
              fileType,
              fileSize,
              tags: Array.isArray(selectedTags) ? selectedTags : [],
              uploadedBy: user?.name || "Unknown",
              contactId,
              source: destination,
              url: uploadedFile.webUrl,
              externalId: uploadedFile.id,
              driveId: destination === "sharepoint" ? selectedLibrary : null,
            }),
          });

          if (!response.ok) {
            throw new Error(
              `Failed to create document association for contact ID: ${contactId}`
            );
          }

          return response.json();
        });

        // Wait for all associations to be created
        await Promise.all(createPromises);
      }

      setProgress(100);

      // Notify parent component that upload is complete
      if (onUploadComplete) {
        onUploadComplete();
      }

      // Close modal after a short delay
      setTimeout(() => {
        onClose();
      }, 1000);
    } catch (error) {
      console.error("UPLOAD MODAL ERROR:", error);

      // Provide more detailed error messages based on the error
      let errorMessage = "Failed to upload document";

      if (error.message) {
        errorMessage = error.message;
        console.log("UPLOAD MODAL: Error message:", errorMessage);

        // Get file extension for more specific error messages
        const fileExtension = file
          ? file.name.split(".").pop().toLowerCase()
          : "";

        // Check for specific error types
        if (error.message.includes("401")) {
          errorMessage = "Authentication error. Please try logging in again.";
        } else if (error.message.includes("403")) {
          errorMessage =
            "You do not have permission to upload to this location.";
        } else if (error.message.includes("404")) {
          errorMessage =
            "The upload location was not found. Please check your OneDrive configuration.";
        } else if (error.message.includes("413")) {
          errorMessage = "The file is too large to upload.";
        } else if (error.message.includes("415")) {
          errorMessage = `This file type (.${fileExtension}) is not supported by Microsoft OneDrive/SharePoint.`;
        } else if (error.message.includes("429")) {
          errorMessage = "Too many requests. Please try again later.";
        } else if (
          error.message.includes("500") ||
          error.message.includes("502") ||
          error.message.includes("503") ||
          error.message.includes("504")
        ) {
          errorMessage =
            "Microsoft OneDrive server error. Please try again later.";
        } else if (
          error.message.includes("network") ||
          error.message.includes("Network")
        ) {
          errorMessage =
            "Network error. Please check your internet connection and try again.";
        } else if (
          error.message.includes("timeout") ||
          error.message.includes("Timeout")
        ) {
          errorMessage =
            "The upload timed out. Please try again with a smaller file or check your connection.";
        } else if (
          error.message.includes("token") ||
          error.message.includes("Token") ||
          error.message.includes("authentication") ||
          error.message.includes("Authentication")
        ) {
          errorMessage =
            "Microsoft authentication error. Please ensure you are logged in with a Microsoft account that has access to OneDrive/SharePoint.";
        } else if (
          error.message.includes("CORS") ||
          error.message.includes("cors")
        ) {
          errorMessage =
            "Cross-origin request blocked. This is a browser security issue.";
        } else if (error.message.includes("Microsoft Graph Client")) {
          errorMessage =
            "Microsoft Graph Client error. Please ensure you have the Microsoft Graph API enabled for your account.";
        } else if (error.message.includes("Failed to read file")) {
          // Already has a detailed message from our improved file reader
        } else if (error.message.includes("Failed to process file")) {
          // Already has a detailed message from our improved file reader
        } else if (fileExtension) {
          // Add file extension to generic error messages
          errorMessage += ` (File type: .${fileExtension})`;
        }
      }

      // If we have an error object with a response property (from fetch)
      if (error.response) {
        console.log("UPLOAD MODAL: Error response:", error.response);
        errorMessage += ` (Status: ${error.response.status})`;
      }

      console.log("UPLOAD MODAL: Final error message to user:", errorMessage);
      setError(errorMessage);
      setProgress(0);
    } finally {
      setUploading(false);
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Prepare file for upload
  const prepareFileForUpload = async (file) => {
    console.log(
      "UPLOAD MODAL: Preparing file for upload:",
      file.name,
      file.type,
      file.size
    );

    // Get file extension
    const extension = file.name.split(".").pop().toLowerCase();
    console.log("UPLOAD MODAL: File extension for preparation:", extension);

    // For text files, ensure they're properly encoded
    if (file.type.startsWith("text/") || file.name.endsWith(".txt")) {
      try {
        console.log("UPLOAD MODAL: Preparing text file with proper encoding");
        // Read the file as text
        const text = await file.text();
        console.log(
          `UPLOAD MODAL: Text file content length: ${text.length} characters`
        );

        // Create a new Blob with the correct encoding
        const blob = new Blob([text], { type: "text/plain" });

        // Create a new File object with the same name
        const newFile = new File([blob], file.name, { type: "text/plain" });
        console.log(
          "UPLOAD MODAL: Created new file with text/plain MIME type:",
          newFile.type,
          newFile.size
        );
        return newFile;
      } catch (error) {
        console.error("UPLOAD MODAL: Error preparing text file:", error);
        // If there's an error, return the original file
        return file;
      }
    }

    // For Office documents, ensure they have the correct MIME type
    const officeExtensions = ["doc", "docx", "xls", "xlsx", "ppt", "pptx"];
    if (officeExtensions.includes(extension)) {
      try {
        console.log(
          "UPLOAD MODAL: Preparing Office document with proper MIME type"
        );

        // Map Office extensions to MIME types
        const officeMimeTypes = {
          doc: "application/msword",
          docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          xls: "application/vnd.ms-excel",
          xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          ppt: "application/vnd.ms-powerpoint",
          pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        };

        const mimeType = officeMimeTypes[extension];

        // If the file already has the correct MIME type, use it as is
        if (file.type === mimeType) {
          console.log(
            "UPLOAD MODAL: Office document already has correct MIME type:",
            file.type
          );
          return file;
        }

        // Otherwise, create a new file with the correct MIME type
        console.log(
          `UPLOAD MODAL: Setting correct MIME type for Office document: ${mimeType}`
        );
        const arrayBuffer = await file.arrayBuffer();
        const newFile = new File([arrayBuffer], file.name, { type: mimeType });
        console.log(
          "UPLOAD MODAL: Created new file with Office MIME type:",
          newFile.type,
          newFile.size
        );
        return newFile;
      } catch (error) {
        console.error("UPLOAD MODAL: Error preparing Office document:", error);
        // If there's an error, return the original file
        return file;
      }
    }

    // For files without a type, try to determine the type from the extension
    if (!file.type || file.type === "") {
      console.log(
        "UPLOAD MODAL: File has no MIME type, determining from extension"
      );
      let mimeType = "application/octet-stream"; // Default

      // Map common extensions to MIME types
      const mimeMap = {
        // Text formats
        txt: "text/plain",
        csv: "text/csv",
        html: "text/html",
        css: "text/css",
        js: "text/javascript",
        json: "application/json",
        xml: "application/xml",
        md: "text/markdown",

        // Document formats
        pdf: "application/pdf",
        doc: "application/msword",
        docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        rtf: "application/rtf",
        odt: "application/vnd.oasis.opendocument.text",

        // Spreadsheet formats
        xls: "application/vnd.ms-excel",
        xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ods: "application/vnd.oasis.opendocument.spreadsheet",

        // Presentation formats
        ppt: "application/vnd.ms-powerpoint",
        pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        odp: "application/vnd.oasis.opendocument.presentation",

        // Image formats
        jpg: "image/jpeg",
        jpeg: "image/jpeg",
        png: "image/png",
        gif: "image/gif",
        bmp: "image/bmp",
        svg: "image/svg+xml",
        webp: "image/webp",
        tiff: "image/tiff",
        tif: "image/tiff",

        // Archive formats
        zip: "application/zip",
        rar: "application/x-rar-compressed",
        "7z": "application/x-7z-compressed",
        tar: "application/x-tar",
        gz: "application/gzip",

        // Audio formats
        mp3: "audio/mpeg",
        wav: "audio/wav",
        ogg: "audio/ogg",
        m4a: "audio/mp4",

        // Video formats
        mp4: "video/mp4",
        avi: "video/x-msvideo",
        mov: "video/quicktime",
        wmv: "video/x-ms-wmv",
        webm: "video/webm",

        // Email formats
        eml: "message/rfc822",
        msg: "application/vnd.ms-outlook",
      };

      if (mimeMap[extension]) {
        mimeType = mimeMap[extension];
      }

      console.log(
        `UPLOAD MODAL: Determined MIME type: ${mimeType} for extension: ${extension}`
      );

      try {
        // Create a new file with the determined MIME type
        const arrayBuffer = await file.arrayBuffer();
        const newFile = new File([arrayBuffer], file.name, { type: mimeType });
        console.log(
          "UPLOAD MODAL: Created new file with determined MIME type:",
          newFile.type,
          newFile.size
        );
        return newFile;
      } catch (error) {
        console.error(
          "UPLOAD MODAL: Error creating file with determined MIME type:",
          error
        );
        return file;
      }
    }

    // For other file types, return as is
    console.log("UPLOAD MODAL: Using file as is with type:", file.type);
    return file;
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
        <Dialog.Content className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-3xl bg-white dark:bg-gray-800 rounded-lg shadow-lg p-5 max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-5">
            <Dialog.Title className="text-xl font-semibold text-gray-900 dark:text-white">
              Upload Document
            </Dialog.Title>
            <Dialog.Close className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" disabled={uploading}>
              <X className="h-5 w-5" />
            </Dialog.Close>
          </div>

        <form onSubmit={handleSubmit} className="px-5 pb-5">
          <div className="space-y-5">
            {/* File and Destination Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              {/* File Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                  Select File
                </label>
                <div className="flex items-center">
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                    disabled={uploading}
                  />
                  <button
                    type="button"
                    onClick={() => fileInputRef.current.click()}
                    className="px-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm flex-1 text-left truncate"
                    disabled={uploading}
                  >
                    {file ? file.name : "Choose a file..."}
                  </button>
                </div>
              </div>

              {/* Destination Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                  Upload Destination
                </label>
                <select
                  value={destination}
                  onChange={(e) => setDestination(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  disabled={uploading}
                >
                  <option value="onedrive">OneDrive</option>
                  <option value="sharepoint">SharePoint</option>
                </select>
                {destination === "sharepoint" && !selectedLibrary && (
                  <div className="flex items-center mt-2 p-2 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
                    <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400 mr-2" />
                    <p className="text-xs text-amber-600 dark:text-amber-400">
                      Please select a SharePoint library in the sidebar first.
                      The upload will not proceed until a library is selected.
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Contact Selection Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              {/* Group Selector */}
              <div>
                <div className="flex justify-between items-center mb-1.5">
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Select Contact Groups
                  </h3>
                  <span className="text-xs text-gray-500">
                    ({selectedGroups.length} groups selected)
                  </span>
                </div>
                <DocumentGroupSelector
                  selectedGroups={selectedGroups}
                  onChange={handleGroupsChange}
                  selectedContactIds={selectedContacts}
                  height={150} // Match height with individual contacts
                />
              </div>

              {/* Individual Contact Search */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                  Select Individual Contacts
                </h3>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <Search className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  </div>
                  <input
                    type="text"
                    className="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-primary focus:border-primary block w-full pl-10 p-2.5"
                    placeholder="Search contacts..."
                    value={contactSearchTerm}
                    onChange={(e) => setContactSearchTerm(e.target.value)}
                    disabled={uploading}
                  />
                </div>

                <div className="mt-2 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  {(Array.isArray(contacts) && contacts.length > 0) ? (
                    (() => {
                      const filteredContacts = contacts.filter(
                        (contact) =>
                          !selectedContacts.includes(contact.id) &&
                          (contactSearchTerm === "" ||
                            contact.name
                              .toLowerCase()
                              .includes(contactSearchTerm.toLowerCase()))
                      );

                      if (filteredContacts.length === 0) {
                        return (
                          <div className="text-center py-4 bg-gray-50 dark:bg-gray-700/30">
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              No matching contacts found
                            </p>
                          </div>
                        );
                      }

                      return (
                        <div className="max-h-[150px]">
                          <List
                            height={150}
                            width="100%"
                            itemCount={filteredContacts.length}
                            itemSize={36} // Slightly smaller items
                            className="divide-y divide-gray-200 dark:divide-gray-700"
                          >
                            {({ index, style }) => {
                              const contact = filteredContacts[index];
                              return (
                                <div
                                  style={style}
                                  className="flex justify-between items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                                  onClick={() => {
                                    if (
                                      !selectedContacts.includes(contact.id)
                                    ) {
                                      setSelectedContacts([
                                        ...selectedContacts,
                                        contact.id,
                                      ]);
                                      setContactSearchTerm("");
                                    }
                                  }}
                                  disabled={uploading}
                                >
                                  <span className="text-xs font-medium">
                                    {contact.name}
                                  </span>
                                  <span className="flex items-center text-primary text-xs">
                                    Add <Plus className="h-3.5 w-3.5 ml-1" />
                                  </span>
                                </div>
                              );
                            }}
                          </List>
                        </div>
                      );
                    })()
                  ) : (
                    <div className="text-center py-4 bg-gray-50 dark:bg-gray-700/30">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        No contacts available
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Selected Contacts - Full Width */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                Selected Contacts <span className="text-xs font-normal text-gray-500">({selectedContacts.length} contacts)</span>
              </h3>
              {selectedContacts.length > 0 ? (
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  <div className="max-h-[200px]">
                    <List
                      height={200}
                      width="100%"
                      itemCount={selectedContacts.length}
                      itemSize={36} // Slightly smaller items
                      className="divide-y divide-gray-200 dark:divide-gray-700"
                    >
                      {({ index, style }) => {
                        const contactId = selectedContacts[index];
                        const contact = contacts.find(
                          (c) => c.id === contactId
                        );
                        return (
                          <div
                            style={style}
                            className="flex justify-between items-center bg-gray-50 dark:bg-gray-700 p-2"
                          >
                            <span className="text-xs font-medium">
                              {contact
                                ? contact.name
                                : `Contact ID: ${contactId}`}
                            </span>
                            <button
                              onClick={() =>
                                setSelectedContacts((prev) =>
                                  prev.filter((id) => id !== contactId)
                                )
                              }
                              className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                              aria-label="Remove contact"
                              disabled={uploading}
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        );
                      }}
                    </List>
                  </div>
                </div>
              ) : (
                <div className="text-center py-6 bg-gray-50 dark:bg-gray-700/30 rounded-lg border border-gray-200 dark:border-gray-700 h-[200px] flex flex-col justify-center">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    No contacts selected
                  </p>
                  <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                    Use the options above to add contacts
                  </p>
                </div>
              )}
            </div>

            {/* Document Tags */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                Document Tags <span className="text-xs font-normal text-gray-500">(optional)</span>
              </h3>
              <DocumentTagSelector
                value={Array.isArray(selectedTags) ? selectedTags : []}
                onChange={(newTags) => setSelectedTags(Array.isArray(newTags) ? newTags : [])}
                disabled={uploading}
              />
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-600 dark:text-red-400 text-sm">
              {error}
            </div>
          )}

          {/* Upload Progress */}
          {uploading && (
            <div className="mb-4">
              <div className="flex justify-between text-sm mb-1">
                <span>Uploading...</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                <div
                  className="bg-primary h-2.5 rounded-full"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
              disabled={uploading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover disabled:opacity-50 flex items-center"
              disabled={
                uploading || (destination === "sharepoint" && !selectedLibrary)
              }
            >
              {uploading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-1" />
                  Upload
                </>
              )}
            </button>
          </div>
        </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
