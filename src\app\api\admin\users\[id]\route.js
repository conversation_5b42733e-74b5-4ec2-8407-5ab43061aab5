import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser, hasRole } from "@/lib/auth";
import { auditLogger } from "@/lib/services/auditLogger";
import { ROLE_ADMIN } from "@/constants/permissions";

/**
 * GET /api/admin/users/[id] - Get a specific user
 * This endpoint returns a specific user by ID
 * Only accessible to admins
 */
export async function GET(request, { params }) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);

    if (user.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get the user by ID
    const targetUser = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        permissions: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            contacts: true,
            contactGroups: true,
          },
        },
      },
    });

    if (!targetUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json(targetUser);
  } catch (error) {
    console.error("Failed to fetch user:", error);
    return NextResponse.json(
      { error: "Failed to fetch user" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/users/[id] - Update a specific user
 * This endpoint updates a specific user by ID
 * Only accessible to admins
 */
export async function PATCH(request, { params }) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);

    if (user.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();

    // Get the user by ID
    const targetUser = await prisma.user.findUnique({
      where: { id: params.id },
    });

    if (!targetUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Prevent changing your own role (to prevent locking yourself out)
    if (params.id === user.id && body.role && body.role !== user.role) {
      return NextResponse.json(
        { error: "You cannot change your own role" },
        { status: 400 }
      );
    }

    // Update the user
    const updatedUser = await prisma.user.update({
      where: { id: params.id },
      data: {
        name: body.name !== undefined ? body.name : undefined,
        role: body.role !== undefined ? body.role : undefined,
        permissions:
          body.permissions !== undefined ? body.permissions : undefined,
      },
    });

    // Log the user update
    await auditLogger.logUserUpdate({
      userId: user.id,
      targetUserId: params.id,
      oldValues: targetUser,
      newValues: updatedUser,
      request
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error("Failed to update user:", error);
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/users/[id] - Delete a specific user
 * This endpoint deletes a specific user by ID
 * Only accessible to admins
 */
export async function DELETE(request, { params }) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);

    if (user.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Prevent deleting yourself
    if (params.id === user.id) {
      return NextResponse.json(
        { error: "You cannot delete your own account" },
        { status: 400 }
      );
    }

    // Get the user by ID
    const targetUser = await prisma.user.findUnique({
      where: { id: params.id },
    });

    if (!targetUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Log the user deletion
    await auditLogger.logUserDelete({
      userId: user.id,
      targetUserId: params.id,
      oldValues: targetUser,
      request
    });

    // Delete the user
    await prisma.user.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete user:", error);
    return NextResponse.json(
      { error: "Failed to delete user" },
      { status: 500 }
    );
  }
}
