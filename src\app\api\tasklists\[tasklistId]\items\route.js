import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { NextResponse } from 'next/server';

// GET: List all items for a tasklist
export async function GET(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { tasklistId } = await params;
    // Only allow access to user's own tasklists unless admin
    const where = user.role === 'admin' ? { id: tasklistId } : { id: tasklistId, userId: user.id };
    const tasklist = await prisma.tasklist.findFirst({ where, include: { items: { orderBy: { order: 'asc' } } } });
    if (!tasklist) {
      return NextResponse.json({ error: 'Tasklist not found' }, { status: 404 });
    }
    return NextResponse.json(tasklist.items);
  } catch (error) {
    console.error('Failed to fetch tasklist items:', error);
    return NextResponse.json({ error: 'Failed to fetch tasklist items' }, { status: 500 });
  }
}

// POST: Create a new item in a tasklist
export async function POST(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { tasklistId } = await params;
    let data = await request.json();
    // Convert dueDate to ISO string if present and not already ISO
    if (typeof data.dueDate !== 'undefined') {
      if (!data.dueDate || data.dueDate === '') {
        data.dueDate = null;
      } else {
        try {
          const dateObj = new Date(data.dueDate);
          if (!isNaN(dateObj.getTime())) {
            data.dueDate = dateObj.toISOString();
          }
        } catch {
          data.dueDate = null;
        }
      }
    }
    // Only allow access to user's own tasklists unless admin
    const where = user.role === 'admin' ? { id: tasklistId } : { id: tasklistId, userId: user.id };
    const tasklist = await prisma.tasklist.findFirst({ where });
    if (!tasklist) {
      return NextResponse.json({ error: 'Tasklist not found' }, { status: 404 });
    }
    const item = await prisma.tasklistItem.create({
      data: {
        ...data,
        tasklistId,
      },
    });
    return NextResponse.json(item);
  } catch (error) {
    console.error('Failed to create tasklist item:', error);
    return NextResponse.json({ error: 'Failed to create tasklist item' }, { status: 500 });
  }
}
