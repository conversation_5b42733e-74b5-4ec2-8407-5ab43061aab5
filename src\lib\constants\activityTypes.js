export const ACTIVITY_TYPES = [
  {
    id: 'call',
    label: 'Call',
    icon: '📞',
    color: 'blue',
    description: 'Phone call with the contact'
  },
  {
    id: 'email',
    label: 'Email',
    icon: '✉️',
    color: 'indigo',
    description: 'Email communication'
  },
  {
    id: 'meeting',
    label: 'Meeting',
    icon: '👥',
    color: 'green',
    description: 'In-person or virtual meeting'
  },
  {
    id: 'note',
    label: 'Note',
    icon: '📝',
    color: 'yellow',
    description: 'General note or comment'
  },
  {
    id: 'task',
    label: 'Task',
    icon: '✓',
    color: 'purple',
    description: 'Task or to-do item'
  },
  {
    id: 'demo',
    label: 'Demo',
    icon: '🎮',
    color: 'pink',
    description: 'Product demonstration'
  },
  {
    id: 'proposal',
    label: 'Proposal',
    icon: '📄',
    color: 'orange',
    description: 'Business proposal or quote'
  },
  {
    id: 'follow-up',
    label: 'Follow-up',
    icon: '🔄',
    color: 'teal',
    description: 'Follow-up communication'
  }
];

export const getActivityTypeById = (id) => {
  return ACTIVITY_TYPES.find(type => type.id === id) || {
    id: 'unknown',
    label: 'Unknown',
    icon: '❓',
    color: 'gray',
    description: 'Unknown activity type'
  };
};

export const getActivityTypeColor = (typeId) => {
  const type = getActivityTypeById(typeId);
  
  const colorMap = {
    blue: 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100',
    indigo: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-800 dark:text-indigo-100',
    green: 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
    yellow: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
    purple: 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100',
    pink: 'bg-pink-100 text-pink-800 dark:bg-pink-800 dark:text-pink-100',
    orange: 'bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100',
    teal: 'bg-teal-100 text-teal-800 dark:bg-teal-800 dark:text-teal-100',
    gray: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
  };
  
  return colorMap[type.color] || colorMap.gray;
};

export const getActivityIconBgColor = (typeId) => {
  const type = getActivityTypeById(typeId);
  
  const colorMap = {
    blue: 'bg-blue-200 dark:bg-blue-700',
    indigo: 'bg-indigo-200 dark:bg-indigo-700',
    green: 'bg-green-200 dark:bg-green-700',
    yellow: 'bg-yellow-200 dark:bg-yellow-700',
    purple: 'bg-purple-200 dark:bg-purple-700',
    pink: 'bg-pink-200 dark:bg-pink-700',
    orange: 'bg-orange-200 dark:bg-orange-700',
    teal: 'bg-teal-200 dark:bg-teal-700',
    gray: 'bg-gray-200 dark:bg-gray-700'
  };
  
  return colorMap[type.color] || colorMap.gray;
};
