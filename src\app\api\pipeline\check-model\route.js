import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma/client';

export async function GET() {
  try {
    // Check if we can access the StageAction model
    const modelInfo = {
      hasStageAction: !!prisma.stageAction,
      models: Object.keys(prisma)
    };
    
    return NextResponse.json(modelInfo);
  } catch (error) {
    console.error('Error checking model:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
