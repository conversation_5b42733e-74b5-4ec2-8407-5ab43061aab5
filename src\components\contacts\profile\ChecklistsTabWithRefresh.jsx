'use client';

import ChecklistsTab from './ChecklistsTab';
import { useStatsRefresh } from '@/hooks/useStatsRefresh';

export default function ChecklistsTabWithRefresh({ contactId }) {
  const { triggerRefresh } = useStatsRefresh();

  // Function to handle checklist changes
  const handleChecklistChange = () => {
    // Trigger a refresh of the stats, but only for checklists
    // This ensures only the checklists card in the quick stats is refreshed
    triggerRefresh(['checklists']);
  };

  return <ChecklistsTab contactId={contactId} onChecklistChange={handleChecklistChange} />;
}
