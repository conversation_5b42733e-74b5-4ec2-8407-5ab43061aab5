'use client';
import { toast } from 'react-hot-toast';

import * as Dialog from '@radix-ui/react-dialog';
import { useState, useEffect } from 'react';

import { CONTACT_FIELDS } from '@/lib/constants/contactFields';
import * as Accordion from '@radix-ui/react-accordion';
import { Settings2, Loader2 } from 'lucide-react';
import Switch from '@/components/ui/Switch';
import TagSelector from '@/components/ui/TagSelector';
import useContactTags from '@/lib/hooks/useTagSuggestions';

// Grouped field definitions for better UX
const FIELD_GROUPS = [
  {
    label: 'Compliance',
    fields: [
      'privacyPolicySent', 'privacyPolicyDate', 'formAdvSent', 'formAdvDate', 'schwabEnvelopeStatus', 'schwabEnvelopeDate', 'advisoryAgreementStatus', 'advisoryAgreementDate', 'onboardingStatus', 'onboardingDate', 'governmentIssuedIdType', 'governmentIssuedId', 'lastReviewDate', 'nextReviewDate',
    ],
  },
  {
    label: 'Contact Info',
    fields: [
      'company', 'gender', 'dateOfBirth', 'maritalStatus', 'addressLine1', 'addressLine2', 'city', 'state', 'postalCode', 'country', 'jobTitle', 'department', 'trustedContactName', 'trustedContactPhone', 'trustedContactEmail', 'middleName', 'preferredName',
    ],
  },
  {
    label: 'CRM',
    fields: [
      'communicationPreferences', 'preferredLanguage', 'referredBy', 'referralSource', 'leadSource', 'leadScore', 'lastActivityDate', 'preferredContactMethod', 'doNotContact',
    ],
  },
  {
    label: 'Essential',
    fields: [
      'tags', 'lastName', 'firstName', 'phone', 'type', 'email', 'pipelineStage', 'updated', 'created',
    ],
  },
  {
    label: 'Investment Advisory',
    fields: [
      'clientType', 'clientStatus', 'riskTolerance', 'investmentObjective', 'investmentExperience', 'annualIncome', 'netWorth', 'liquidNetWorth', 'sourceOfFunds', 'employmentStatus', 'employerName', 'employerAddress', 'occupation', 'yearsWithEmployer', 'taxBracket', 'citizenship', 'ssnOrTaxId', 'regulatoryStatus', 'complianceFlags', 'accountNumbers', 'custodian', 'accountType', 'accountOpenDate', 'accountCloseDate', 'accountStatus', 'beneficiaryNames', 'trustedContactName', 'trustedContactPhone', 'trustedContactEmail', 'estatePlanOnFile', 'attorneyName', 'attorneyEmail', 'cpaName', 'cpaEmail', 'financialAdvisorName', 'financialAdvisorEmail',
    ],
  },
  {
    label: 'Insurance',
    fields: [
      'weightLb', 'heightInches', 'insuranceType', 'insuranceEstimatedAnnualPremiumBudget', 'insuranceClientInfoSubmittedDate', 'insuranceQuoteReceivedDate', 'insuranceQuestionnaireSentToClientDate', 'insuranceDocumentsSubmittedToCarrierDate', 'insuranceCompletionDate', 'insuranceStatus',
    ],
  },
];

// Utility: get all unique field IDs from all groups
const ALL_FIELD_IDS = FIELD_GROUPS.flatMap(g => g.fields);
const getDefaultVisibleFields = () => [...ALL_FIELD_IDS];

function getFieldDef(id) {
  return CONTACT_FIELDS.find(f => f.id === id);
}

import React from 'react';

function ContactEditDialog({ contact, isOpen, onClose, onSave }) {
  // List of required field IDs for edit dialog
  const REQUIRED_FIELDS = ['firstName', 'lastName'];
  // Helper: for each group, check if any visible field in the group has an error
  const groupHasError = (group) => {
    return group.fields.some(
      (fieldId) => visibleFields.includes(fieldId) && errors[fieldId]
    );
  };
  // Validation state
  const [errors, setErrors] = useState({});
  const { tags: allTags, loading: tagsLoading } = useContactTags();
  const initialFormData = Object.fromEntries(CONTACT_FIELDS.map(f => [f.id, Array.isArray(f.render({})) ? [] : '']));
  initialFormData.type = 'prospective';

  const [formData, setFormData] = useState({ ...initialFormData });
  // No longer need ref for tag selector
  const [openAccordions, setOpenAccordions] = useState([FIELD_GROUPS[0].label]);
  const [showFieldConfig, setShowFieldConfig] = useState(false);
  const [visibleFields, setVisibleFields] = useState(getDefaultVisibleFields());
  const [isLoadingPreferences, setIsLoadingPreferences] = useState(false);
  const [isSavingPreferences, setIsSavingPreferences] = useState(false);

  // Dialog key for this specific dialog
  const dialogKey = 'contact-add-edit';

  // Fetch dialog field preferences when dialog opens
  useEffect(() => {
    if (isOpen) {
      fetchDialogPreferences();
    }
  }, [isOpen]);

  // Fetch dialog field preferences from the API
  const fetchDialogPreferences = async () => {
    try {
      setIsLoadingPreferences(true);
      const response = await fetch(`/api/dialog-preferences?dialogKey=${dialogKey}`);

      if (response.ok) {
        const data = await response.json();

        // If visibleFields is empty, it means all fields are visible (default)
        if (data.visibleFields && data.visibleFields.length > 0) {
          setVisibleFields(data.visibleFields);
        } else {
          // Use default (all fields visible)
          setVisibleFields(getDefaultVisibleFields());
        }
      }
    } catch (error) {
      console.error('Failed to fetch dialog preferences:', error);
      // Fallback to default
      setVisibleFields(getDefaultVisibleFields());
    } finally {
      setIsLoadingPreferences(false);
    }
  };

  // Save dialog field preferences to the API
  const saveDialogPreferences = async () => {
    try {
      setIsSavingPreferences(true);

      const response = await fetch('/api/dialog-preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dialogKey,
          visibleFields,
        }),
      });

      if (!response.ok) {
        console.error('Failed to save dialog preferences');
      }
    } catch (error) {
      console.error('Failed to save dialog preferences:', error);
    } finally {
      setIsSavingPreferences(false);
    }
  };

  // Update form data when contact changes
  useEffect(() => {
    if (contact) {
      const newForm = { ...initialFormData };
      for (const key in contact) {
        if (key in newForm) newForm[key] = contact[key];
      }
      setFormData(newForm);
    }
  }, [contact]);


  const handleChange = (id, value) => {
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  // Field config dialog content
  const renderFieldConfigDialog = () => (
    <Dialog.Root open={showFieldConfig} onOpenChange={setShowFieldConfig}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/40 z-[120]" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg z-[130]">
          <Dialog.Title className="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center gap-2">
            <Settings2 className="w-5 h-5" />
            Dialog Field Visibility
          </Dialog.Title>
          <div className="mb-2 text-sm text-gray-600 dark:text-gray-300">
            Choose which fields are available in the <b>Add/Edit Contact</b> dialogs.<br />
            <span className="text-xs text-gray-500">(This does <b>not</b> affect the table columns. Use the column customizer for that.)</span>
          </div>
          <div className="max-h-72 overflow-y-auto border rounded p-3 bg-gray-50 dark:bg-gray-900 relative">
            {isLoadingPreferences ? (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-50/80 dark:bg-gray-900/80">
                <Loader2 className="h-6 w-6 text-primary animate-spin" />
              </div>
            ) : null}
            {CONTACT_FIELDS.filter(f => ALL_FIELD_IDS.includes(f.id)).map(field => (
              <div key={field.id} className="py-1">
                <Switch
                  id={`field-visibility-${field.id}`}
                  checked={visibleFields.includes(field.id)}
                  onChange={() => {
                    setVisibleFields(v =>
                      visibleFields.includes(field.id)
                        ? v.filter(id => id !== field.id)
                        : [...v, field.id]
                    );
                  }}
                  size="sm"
                  label={field.label}
                  disabled={isLoadingPreferences}
                />
              </div>
            ))}
          </div>
          <div className="flex justify-between items-center mt-6">
            <div className="flex gap-2">
              <button
                type="button"
                className="px-3 py-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-1"
                onClick={() => {
                  // If all fields are already visible, this will do nothing
                  setVisibleFields(getDefaultVisibleFields());
                }}
                disabled={isLoadingPreferences || isSavingPreferences}
              >
                Show All Fields
              </button>
              <button
                type="button"
                className="px-3 py-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-1"
                onClick={() => {
                  // Toggle all fields - if all are visible, hide all, otherwise show all
                  const allFields = getDefaultVisibleFields();
                  if (visibleFields.length === allFields.length) {
                    // All fields are visible, so hide all (except required ones)
                    const requiredFields = ['firstName', 'lastName', 'type'];
                    setVisibleFields(requiredFields);
                  } else {
                    // Not all fields are visible, so show all
                    setVisibleFields(allFields);
                  }
                }}
                disabled={isLoadingPreferences || isSavingPreferences}
              >
                Toggle All Fields
              </button>
            </div>

            <div className="flex gap-2">
              <Dialog.Close asChild>
                <button className="px-3 py-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">Cancel</button>
              </Dialog.Close>
              <button
                className="px-4 py-1.5 text-xs bg-primary text-white rounded-lg hover:bg-primary-hover flex items-center gap-1"
                onClick={async () => {
                  await saveDialogPreferences();
                  setShowFieldConfig(false);
                }}
                disabled={isSavingPreferences}
              >
                {isSavingPreferences && <Loader2 size={12} className="animate-spin" />}
                {isSavingPreferences ? 'Saving...' : 'Save & Close'}
              </button>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );

  const handleSubmit = async (e) => {
    e.preventDefault();
    // Validation: require firstName and lastName
    const newErrors = {};
    if (!formData.firstName?.trim()) newErrors.firstName = 'First Name is required';
    if (!formData.lastName?.trim()) newErrors.lastName = 'Last Name is required';
    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) {
      toast.error('Please fill in all required fields.');
      return;
    }
    try {
      await onSave({ ...formData, id: contact.id });
      toast.success('Contact updated!');
      onClose();
    } catch (error) {
      toast.error('Failed to update contact.');
      console.error('Error saving contact:', error);
    }
  };


  // Helper for toggling "Show More" for rarely used fields (not used, but kept for reference)
  // const toggleShowMore = groupLabel => {
  //   setShowMore(prev => ({ ...prev, [groupLabel]: !prev[groupLabel] }));
  // };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-[100]" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-8 w-full max-w-6xl z-[110]">
          <div className="flex items-center justify-between mb-6">
            <Dialog.Title className="text-2xl font-semibold text-gray-900 dark:text-white">Edit Contact</Dialog.Title>
            <button
              type="button"
              className="ml-2 p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
              title="Configure which fields are visible in this dialog"
              onClick={() => setShowFieldConfig(true)}
            >
              <Settings2 className="w-5 h-5 text-gray-500 dark:text-gray-300" />
            </button>
          </div>
          {showFieldConfig && renderFieldConfigDialog()}
          <form onSubmit={handleSubmit} className="space-y-6 max-h-[65vh] overflow-y-auto pr-2">
            <Accordion.Root type="multiple" value={openAccordions} onValueChange={setOpenAccordions} collapsible>
              {FIELD_GROUPS.map((group) => {
                const hasError = groupHasError(group);
                return (
                  <Accordion.Item key={group.label} value={group.label} className={`mb-4 border rounded-lg ${hasError ? 'border-red-500 dark:border-red-400' : 'border-gray-200 dark:border-gray-700'}`}>
                    <Accordion.Header>
                      <Accordion.Trigger className={`w-full flex justify-between items-center px-6 py-3 rounded-t-lg text-base font-bold focus:outline-none sticky top-0 z-10 ${hasError ? 'bg-red-50 dark:bg-red-900 text-red-700 dark:text-red-300 border-b border-red-500' : 'bg-gray-100 dark:bg-gray-900 text-primary'}`}>
                        <span className="flex items-center">
                          {group.label}
                          {hasError && (
                            <span className="ml-2 text-red-500 dark:text-red-400" title="Please fill all required fields in this section">
                              &#9888;
                            </span>
                          )}
                        </span>
                        <span className="ml-2">{openAccordions.includes(group.label) ? '▲' : '▼'}</span>
                      </Accordion.Trigger>
                    </Accordion.Header>
                    <Accordion.Content className="px-6 py-3 bg-white dark:bg-gray-800 rounded-b-lg border-t border-gray-200 dark:border-gray-700">
                      {/* Render tags field as a full-width row above the grid for visibility and correct suggestions dropdown */}
                      {group.fields.includes('tags') && visibleFields.includes('tags') && (
                        <div className="mb-4">
                          <label className="block text-[11px] font-medium text-gray-600 dark:text-gray-300 mb-0.5">
                            Tags

                          </label>
                          <div className="bg-gray-50 dark:bg-gray-700 rounded px-1 py-0.5 min-h-7 flex items-center w-full relative z-10">
                            <TagSelector
                              value={formData.tags || []}
                              onChange={tags => handleChange('tags', tags)}
                              availableTags={allTags}
                              loading={tagsLoading}
                            />
                          </div>
                          {errors['tags'] && (
                            <span className="ml-2 text-xs text-red-500">{errors['tags']}</span>
                          )}
                        </div>
                      )}
                      <div className="grid grid-cols-1 md:grid-cols-3 xl:grid-cols-5 gap-3">
                        {group.fields.filter(fieldId => visibleFields.includes(fieldId) && fieldId !== 'tags').map(fieldId => {
                          const field = getFieldDef(fieldId);
                          if (!field) return null;
                          let input = null;
                          if (fieldId === 'type') {
                            input = (
                              <select className="block w-full rounded border border-gray-300 dark:border-gray-600 px-2 py-1 bg-white dark:bg-gray-700 text-xs text-gray-900 dark:text-white h-8" value={formData.type} onChange={e => handleChange('type', e.target.value)}>
                                <option value="prospective">Prospective</option>
                                <option value="client">Client</option>
                                <option value="contact">Contact</option>
                                <option value="partner">Partner</option>
                                <option value="customer">Customer</option>
                              </select>
                            );
                          } else if (Array.isArray(formData[fieldId])) {
                              input = (
                                <input
                                  type="text"
                                  className="block w-full rounded border border-gray-300 dark:border-gray-600 px-2 py-1 bg-white dark:bg-gray-700 text-xs text-gray-900 dark:text-white h-8"
                                  value={formData[fieldId].join(', ')}
                                  onChange={e => handleChange(fieldId, e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                                  placeholder="Comma separated"
                                />
                              );
                          } else if (fieldId.toLowerCase().includes('date')) {
                            input = (
                              <input type="date" className="block w-full rounded border border-gray-300 dark:border-gray-600 px-2 py-1 bg-white dark:bg-gray-700 text-xs text-gray-900 dark:text-white h-8" value={formData[fieldId] ? formData[fieldId].slice(0, 10) : ''} onChange={e => handleChange(fieldId, e.target.value)} />
                            );
                          } else if (typeof formData[fieldId] === 'boolean') {
                            input = (
                              <select className="block w-full rounded border border-gray-300 dark:border-gray-600 px-2 py-1 bg-white dark:bg-gray-700 text-xs text-gray-900 dark:text-white h-8" value={formData[fieldId] ? 'true' : 'false'} onChange={e => handleChange(fieldId, e.target.value === 'true')}>
                                <option value="">--</option>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                              </select>
                            );
                          } else {
                            input = (
                              <input type="text" className="block w-full rounded border border-gray-300 dark:border-gray-600 px-2 py-1 bg-white dark:bg-gray-700 text-xs text-gray-900 dark:text-white h-8" value={formData[fieldId] || ''} onChange={e => handleChange(fieldId, e.target.value)} />
                            );
                          }
                          const isRequired = REQUIRED_FIELDS.includes(fieldId);
                          return (
                            <div key={fieldId} className="flex flex-col">
                              <label className="block text-[11px] font-medium text-gray-600 dark:text-gray-300 mb-0.5">
                                {field.label}
                                {isRequired && <span className="text-red-500 ml-0.5" title="Required">*</span>}
                                {field.tooltip && (
                                  <span className="ml-1 cursor-pointer" title={field.tooltip}>🛈</span>
                                )}
                                {(errors[fieldId]) && (
                                  <span className="ml-2 text-xs text-red-500">{errors[fieldId]}</span>
                                )}
                              </label>
                              <div className="bg-gray-50 dark:bg-gray-700 rounded px-1 py-0.5 flex-1 min-h-7 flex items-center">{input}</div>
                            </div>
                          );
                        })}
                      </div>
                    </Accordion.Content>
                  </Accordion.Item>
                );
              })}
            </Accordion.Root>
            <div className="flex justify-end gap-3 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-3 py-1.5 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-lg"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-1.5 text-xs bg-primary text-white rounded-lg hover:bg-primary-hover"
              >
                Update Contact
              </button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}

export default ContactEditDialog;
