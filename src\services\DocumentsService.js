import { graphConfig } from "@/config/authConfig";

/**
 * Service to handle document operations with Microsoft Graph API
 */
export class DocumentsService {
  /**
   * Get documents from OneDrive
   * @param {string} accessToken - Microsoft Graph API access token
   * @returns {Promise<Array>} - List of documents
   */
  static async getOneDriveDocuments(accessToken) {
    try {
      const headers = new Headers();
      headers.append("Authorization", `Bearer ${accessToken}`);

      const response = await fetch(
        `${graphConfig.graphMeEndpoint}/drive/root/children`,
        {
          method: "GET",
          headers: headers,
        }
      );

      if (!response.ok) {
        throw new Error(
          `Error fetching OneDrive documents: ${response.statusText}`
        );
      }

      const data = await response.json();
      return data.value.map((item) => this.mapOneDriveItemToDocument(item));
    } catch (error) {
      console.error("Error in getOneDriveDocuments:", error);
      throw error;
    }
  }

  /**
   * Get documents from SharePoint
   * @param {string} accessToken - Microsoft Graph API access token
   * @param {string} siteId - SharePoint site ID
   * @returns {Promise<Array>} - List of documents
   */
  static async getSharePointDocuments(accessToken, siteId) {
    try {
      const headers = new Headers();
      headers.append("Authorization", `Bearer ${accessToken}`);

      // First, get the default document library
      const libraryResponse = await fetch(
        `${graphConfig.graphEndpoint}/sites/${siteId}/drives`,
        {
          method: "GET",
          headers: headers,
        }
      );

      if (!libraryResponse.ok) {
        throw new Error(
          `Error fetching SharePoint libraries: ${libraryResponse.statusText}`
        );
      }

      const libraries = await libraryResponse.json();
      if (!libraries.value || libraries.value.length === 0) {
        return [];
      }

      // Use the first document library (usually "Documents")
      const defaultLibrary = libraries.value[0];

      // Get files from the default library
      const response = await fetch(
        `${graphConfig.graphEndpoint}/drives/${defaultLibrary.id}/root/children`,
        {
          method: "GET",
          headers: headers,
        }
      );

      if (!response.ok) {
        throw new Error(
          `Error fetching SharePoint documents: ${response.statusText}`
        );
      }

      const data = await response.json();
      return data.value.map((item) =>
        this.mapSharePointItemToDocument(item, defaultLibrary.name)
      );
    } catch (error) {
      console.error("Error in getSharePointDocuments:", error);
      throw error;
    }
  }

  /**
   * Upload a document to OneDrive
   * @param {string} accessToken - Microsoft Graph API access token
   * @param {File} file - File to upload
   * @param {string} folderPath - Path to folder (optional)
   * @returns {Promise<Object>} - Uploaded document info
   */
  static async uploadToOneDrive(accessToken, file, folderPath = "") {
    try {
      const headers = new Headers();
      headers.append("Authorization", `Bearer ${accessToken}`);

      let endpoint = `${graphConfig.graphMeEndpoint}/drive/root:/${file.name}:/content`;
      if (folderPath) {
        endpoint = `${graphConfig.graphMeEndpoint}/drive/root:/${folderPath}/${file.name}:/content`;
      }

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: headers,
        body: file,
      });

      if (!response.ok) {
        throw new Error(`Error uploading to OneDrive: ${response.statusText}`);
      }

      const data = await response.json();
      return this.mapOneDriveItemToDocument(data);
    } catch (error) {
      console.error("Error in uploadToOneDrive:", error);
      throw error;
    }
  }

  /**
   * Upload a document to SharePoint
   * @param {string} accessToken - Microsoft Graph API access token
   * @param {string} siteId - SharePoint site ID
   * @param {string} driveId - SharePoint drive ID
   * @param {File} file - File to upload
   * @param {string} folderPath - Path to folder (optional)
   * @returns {Promise<Object>} - Uploaded document info
   */
  static async uploadToSharePoint(
    accessToken,
    siteId,
    driveId,
    file,
    folderPath = ""
  ) {
    try {
      const headers = new Headers();
      headers.append("Authorization", `Bearer ${accessToken}`);

      let endpoint = `${graphConfig.graphEndpoint}/drives/${driveId}/root:/${file.name}:/content`;
      if (folderPath) {
        endpoint = `${graphConfig.graphEndpoint}/drives/${driveId}/root:/${folderPath}/${file.name}:/content`;
      }

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: headers,
        body: file,
      });

      if (!response.ok) {
        throw new Error(
          `Error uploading to SharePoint: ${response.statusText}`
        );
      }

      const data = await response.json();
      return this.mapSharePointItemToDocument(data, "Documents"); // Assuming default library name
    } catch (error) {
      console.error("Error in uploadToSharePoint:", error);
      throw error;
    }
  }

  /**
   * Download a document from OneDrive or SharePoint
   * @param {string} accessToken - Microsoft Graph API access token
   * @param {string} downloadUrl - URL to download the file
   * @returns {Promise<Blob>} - File blob
   */
  static async downloadDocument(accessToken, downloadUrl) {
    try {
      const headers = new Headers();
      headers.append("Authorization", `Bearer ${accessToken}`);

      const response = await fetch(downloadUrl, {
        method: "GET",
        headers: headers,
      });

      if (!response.ok) {
        throw new Error(`Error downloading document: ${response.statusText}`);
      }

      return await response.blob();
    } catch (error) {
      console.error("Error in downloadDocument:", error);
      throw error;
    }
  }

  /**
   * Delete a document from OneDrive or SharePoint
   * @param {string} accessToken - Microsoft Graph API access token
   * @param {string} itemId - ID of the item to delete
   * @param {string} driveId - Drive ID (required for SharePoint)
   * @returns {Promise<boolean>} - Success status
   */
  static async deleteDocument(accessToken, itemId, driveId = null) {
    try {
      const headers = new Headers();
      headers.append("Authorization", `Bearer ${accessToken}`);

      let endpoint;
      if (driveId) {
        // SharePoint document
        endpoint = `${graphConfig.graphEndpoint}/drives/${driveId}/items/${itemId}`;
      } else {
        // OneDrive document
        endpoint = `${graphConfig.graphMeEndpoint}/drive/items/${itemId}`;
      }

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: headers,
      });

      return response.ok;
    } catch (error) {
      console.error("Error in deleteDocument:", error);
      throw error;
    }
  }

  /**
   * Map OneDrive item to document object
   * @param {Object} item - OneDrive item
   * @returns {Object} - Document object
   */
  static mapOneDriveItemToDocument(item) {
    return {
      id: item.id,
      name: item.name,
      type: item.name.split(".").pop().toLowerCase(),
      size: this.formatFileSize(item.size),
      sizeBytes: item.size,
      createdAt: item.createdDateTime,
      modifiedAt: item.lastModifiedDateTime,
      webUrl: item.webUrl,
      downloadUrl: item["@microsoft.graph.downloadUrl"],
      source: "onedrive",
      folder: item.parentReference?.path?.replace("/drive/root:", "") || "/",
      isFolder: item.folder ? true : false,
    };
  }

  /**
   * Map SharePoint item to document object
   * @param {Object} item - SharePoint item
   * @param {string} libraryName - SharePoint library name
   * @returns {Object} - Document object
   */
  static mapSharePointItemToDocument(item, libraryName) {
    return {
      id: item.id,
      name: item.name,
      type: item.name.split(".").pop().toLowerCase(),
      size: this.formatFileSize(item.size),
      sizeBytes: item.size,
      createdAt: item.createdDateTime,
      modifiedAt: item.lastModifiedDateTime,
      webUrl: item.webUrl,
      downloadUrl: item["@microsoft.graph.downloadUrl"],
      source: "sharepoint",
      folder:
        libraryName +
        (item.parentReference?.path?.replace("/drive/root:", "") || "/"),
      isFolder: item.folder ? true : false,
      driveId: item.parentReference?.driveId,
      siteId: item.parentReference?.siteId,
    };
  }

  /**
   * Format file size to human-readable format
   * @param {number} bytes - File size in bytes
   * @returns {string} - Formatted file size
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Get SharePoint sites
   * @param {string} accessToken - Microsoft Graph API access token
   * @returns {Promise<Array>} - List of SharePoint sites
   */
  static async getSharePointSites(accessToken) {
    try {
      const headers = new Headers();
      headers.append("Authorization", `Bearer ${accessToken}`);

      const response = await fetch(
        `${graphConfig.graphEndpoint}/sites?search=*`,
        {
          method: "GET",
          headers: headers,
        }
      );

      if (!response.ok) {
        throw new Error(
          `Error fetching SharePoint sites: ${response.statusText}`
        );
      }

      const data = await response.json();
      return data.value.map((site) => ({
        id: site.id,
        name: site.displayName,
        url: site.webUrl,
        description: site.description,
      }));
    } catch (error) {
      console.error("Error in getSharePointSites:", error);
      throw error;
    }
  }
}
