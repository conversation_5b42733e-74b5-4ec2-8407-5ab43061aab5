import { NextResponse } from 'next/server';
import { getSignedInUser } from '@/lib/auth';
import { getGraphClient } from '@/lib/graph';
import { auditLogger } from '@/lib/services/auditLogger';

export async function POST(request) {
  try {
    const { user } = await getSignedInUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { recipient, subject, content, contactId } = await request.json();

    // Validate required fields
    if (!recipient || !subject || !content) {
      return NextResponse.json(
        { error: 'Recipient, subject, and content are required' },
        { status: 400 }
      );
    }

    // Get Microsoft Graph client
    const graphClient = await getGraphClient();
    
    const email = {
      message: {
        subject,
        body: {
          contentType: 'Text',
          content,
        },
        toRecipients: [
          {
            emailAddress: {
              address: recipient,
            },
          },
        ],
      },
    };

    // Send the email through Microsoft Graph
    await graphClient
      .api('/me/sendMail')
      .post(email);

    // Log the email sending
    await auditLogger.logEmailSent({
      userId: user.id,
      emailData: {
        subject,
        content,
        recipient,
        contactId
      },
      recipients: [recipient],
      request
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Email sent successfully' 
    });

  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    );
  }
}
