import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";

export async function GET(request, { params }) {
  try {

    // Get the current user
    const { user } = await getSignedInUser(request);

    const { id } = await params;

    // Build where clause
    let whereClause = { id: String(id) };
    if (user.role !== "admin") {
      whereClause.userId = String(user.id);
    }
    // First verify the contact exists
    const contact = await prisma.contact.findFirst({
      where: whereClause,
    });

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    // Get tasks from the database
    const tasks = await prisma.task.findMany({
      where: { contactId: id },
      orderBy: { dueDate: "asc" },
    });

    console.log("Found tasks:", tasks.length);

    return NextResponse.json(tasks);
  } catch (error) {
    console.error("Failed to fetch tasks:", error);
    console.error("Error details:", JSON.stringify(error, null, 2));
    console.error("Error stack:", error.stack);
    return NextResponse.json(
      { error: `Failed to fetch tasks: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function POST(request, { params }) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);

    const { id } = await params;

    // First verify the contact belongs to the user (or any if admin)
    let whereClause = { id: String(id) };
    if (user.role !== "admin") {
      whereClause.userId = String(user.id);
    }
    const contact = await prisma.contact.findFirst({
      where: whereClause,
    });

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    const body = await request.json();
    console.log("Creating task with body:", body);

    // Check if required fields are present
    if (!body.title) {
      return NextResponse.json({ error: "Title is required" }, { status: 400 });
    }

    if (!body.dueDate) {
      return NextResponse.json(
        { error: "Due date is required" },
        { status: 400 }
      );
    }

    // Create a basic data object with required fields
    const data = {
      title: body.title,
      description: body.description || "",
      contactId: id,
      completed: false,
      createdAt: new Date(),
      dueDate: new Date(`${body.dueDate}T12:00:00Z`),
      priority: body.priority || "medium",
    };
    try {
      const sampleTask = await prisma.task.findFirst();
      if (sampleTask && "completedAt" in sampleTask) {
        data.completedAt = null;
      }
      if (sampleTask && "updatedAt" in sampleTask) {
        data.updatedAt = new Date();
      }
    } catch (schemaError) {
      console.warn("Error checking schema:", schemaError.message);
    }
    console.log("Creating task with data:", data);
    const task = await prisma.task.create({ data });
    console.log("Task created:", task);
    return NextResponse.json(task);
  } catch (error) {
    console.error("Failed to create task:", error);
    console.error("Error details:", JSON.stringify(error, null, 2));
    console.error("Error stack:", error.stack);
    return NextResponse.json(
      { error: "Failed to create task: " + error.message },
      { status: 500 }
    );
  }
}
