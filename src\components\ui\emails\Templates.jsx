"use client";
import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";
import { Search, Filter, Star } from "lucide-react";

const SkeletonCard = () => (
  <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 animate-pulse">
    <div className="flex justify-between items-start mb-2">
      <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
      <div className="h-5 w-5 bg-gray-200 dark:bg-gray-700 rounded"></div>
    </div>
    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
    <div className="flex justify-end space-x-2">
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-14"></div>
    </div>
  </div>
);

export default function Templates({ activeTab }) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filter, setFilter] = useState("all"); // all, recent, favorites
  const [errorMessage, setErrorMessage] = useState("");
  const queryClient = useQueryClient();
  const router = useRouter();

  const {
    data: templates = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ["templates"],
    queryFn: async () => {
      const response = await fetch("/api/email-templates");
      if (!response.ok) {
        // Check if the response is an auth error
        const data = await response.json();
        if (data.error === "Unauthorized") {
          // Redirect to the login page
          router.push("/api/auth/login");
          return;
        } else {
          throw new Error("Failed to fetch templates");
        }
      }
      const data = await response.json();
      return data;
    },
    enabled: activeTab === "templates",
    staleTime: 30000,
  });
  const [isDeleting, setIsDeleting] = useState(false);

  if (isLoading) {
    return (
      <div className="col-span-full space-y-6">
        {/* Skeleton Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-lg w-full animate-pulse"></div>
          </div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-lg w-48 animate-pulse"></div>
        </div>

        {/* Skeleton Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <SkeletonCard key={index} />
          ))}
        </div>
      </div>
    );
  }

  const handleDelete = async (templateId, e) => {
    e.preventDefault();

    if (
      !confirm(
        "Are you sure you want to delete this template? This action cannot be undone."
      )
    ) {
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/email-templates/${templateId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        // show error message
        console.log("THE ERROR: ", error.status);
        console.log("THE ERROR MESSAGE FROM API: ", error.error);

        if (error.status === 409) {
          setErrorMessage(
            "Error deleting template. Please delete any active campaigns using this template and try again."
          );
          return;
        }
        throw new Error(error.message || "Failed to delete template");
      }

      // Invalidate and refetch the templates query
      queryClient.invalidateQueries(["templates"]);
    } catch (error) {
      console.error("Error deleting template:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleToggleFavorite = async (templateId, isFavorite) => {
    try {
      const response = await fetch(`/api/email-templates/${templateId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isFavorite }),
      });

      if (!response.ok) {
        throw new Error("Failed to update template");
      }

      // Invalidate and refetch the templates query
      queryClient.invalidateQueries(["templates"]);
    } catch (error) {
      console.error("Error updating template:", error);
      // Optionally show an error toast/notification
    }
  };

  // Filter and search templates
  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description?.toLowerCase().includes(searchTerm.toLowerCase());

    if (filter === "recent") {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return matchesSearch && new Date(template.createdAt) > thirtyDaysAgo;
    }
    if (filter === "favorites") {
      return matchesSearch && template.isFavorite;
    }
    return matchesSearch;
  });

  if (!templates?.length) {
    return (
      <div className="col-span-full">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No email templates yet
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            Get started by creating your first email template
          </p>
          <Link
            href="/emails/templates/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-hover"
          >
            Create Template
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="col-span-full space-y-6">
      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border rounded-lg dark:bg-gray-800 dark:border-gray-700"
          />
        </div>
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="px-4 py-2 border rounded-lg dark:bg-gray-800 dark:border-gray-700"
        >
          <option value="all">All Templates</option>
          <option value="recent">Recent (30 days)</option>
          <option value="favorites">Favorites</option>
        </select>
      </div>
      {/* Error message */}
      {errorMessage && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-red-600 dark:text-red-400 text-sm">
                {errorMessage}
              </div>
            </div>
            <button
              onClick={() => setErrorMessage("")}
              className="text-red-400 hover:text-red-600 dark:text-red-300 dark:hover:text-red-100"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      )}
      {/* Template Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <div
            key={template.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
          >
            <div className="flex justify-between items-start mb-2">
              <h3 className="text-lg font-medium">{template.name}</h3>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  handleToggleFavorite(template.id, !template.isFavorite);
                }}
                className="text-gray-400 hover:text-yellow-400 transition-colors"
              >
                <Star
                  className={`h-5 w-5 ${
                    template.isFavorite ? "fill-yellow-400 text-yellow-400" : ""
                  }`}
                />
              </button>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
              {template.description || "No description"}
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500 mb-4">
              Created{" "}
              {formatDistanceToNow(new Date(template.createdAt), {
                addSuffix: true,
              })}
            </p>
            <div className="flex justify-end space-x-2">
              <Link
                href={`/emails/templates/${template.id}/edit`}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Edit
              </Link>

              <button
                onClick={(e) => handleDelete(template.id, e)}
                className="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        ))}
      </div>
      {/* No Results Message */}
      {filteredTemplates.length === 0 && searchTerm && (
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400">
            No templates found matching "{searchTerm}"
          </p>
        </div>
      )}
    </div>
  );
}
