import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma/client';

export async function GET() {
  try {
    // In a real implementation, you would fetch milestones from a dedicated table
    // For now, we'll return mock data
    
    // Get some contacts to associate with our mock milestones
    const contacts = await prisma.contact.findMany({
      take: 3,
      select: {
        id: true,
        firstName: true,
        lastName: true,
      },
    });
    
    // Create mock milestones if we have contacts
    const upcomingMilestones = contacts.length > 0 
      ? [
          {
            id: '1',
            title: 'Contract Renewal',
            date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
            contact: contacts[0],
          },
          {
            id: '2',
            title: 'Quarterly Review',
            date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
            contact: contacts.length > 1 ? contacts[1] : contacts[0],
          },
          {
            id: '3',
            title: 'Project Deadline',
            date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            contact: contacts.length > 2 ? contacts[2] : contacts[0],
          },
        ]
      : [];

    return NextResponse.json(upcomingMilestones);
  } catch (error) {
    console.error('Failed to fetch upcoming milestones:', error);
    return NextResponse.json(
      { error: 'Failed to fetch upcoming milestones' },
      { status: 500 }
    );
  }
}
