'use client';

import { useState, useRef, useEffect, useMemo } from 'react';
import { Info, Search } from 'lucide-react';
import { dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import PragmaticContactCard from './PragmaticContactCard';
import Tooltip from '@/components/ui/Tooltip';
import PipelineColumnFilter from './PipelineColumnFilter';
import './pipeline-styles.css';

// Define the state types for the column
const ColumnState = {
  IDLE: 'idle',
  DRAG_OVER: 'drag-over'
};

export default function PragmaticUnassignedColumn({
  contacts,
  onLoadMore,
  isMultiSelectMode = false,
  selectedContacts = [],
  onContactSelect,
  onContactMove
}) {
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [columnState, setColumnState] = useState(ColumnState.IDLE);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState({ field: null, direction: 'asc' });
  const scrollRef = useRef(null);

  const unassignedStage = {
    id: 'unassigned',
    name: 'Unassigned',
    order: 0
  };

  // Set up drop target
  useEffect(() => {
    if (!scrollRef.current || isMultiSelectMode) return;

    console.log('Setting up drop target for unassigned column');

    const cleanup = dropTargetForElements({
      element: scrollRef.current,
      getIsSticky: () => true,
      canDrop: ({ source }) => {
        // Only allow contact cards to be dropped
        return source.data?.type === 'contact-card';
      },
      getData: () => ({
        stageId: 'unassigned',
        type: 'pipeline-column'
      }),
      onDragEnter: ({ source }) => {
        console.log(`Drag entered unassigned column from contact ${source.data?.contactId}`);
        setColumnState(ColumnState.DRAG_OVER);
      },
      onDragLeave: () => {
        console.log('Drag left unassigned column');
        setColumnState(ColumnState.IDLE);
      },
      onDrop: ({ source }) => {
        console.log('Drop on unassigned column');

        // Get the contact data
        const contactId = source.data?.contactId;
        const contact = source.data?.contact;

        if (!contactId) {
          console.log('Missing contact ID in drop data');
          setColumnState(ColumnState.IDLE);
          return;
        }

        // Don't do anything if the contact is already unassigned
        if (!contact?.pipelineStage) {
          console.log(`Contact ${contactId} is already unassigned`);
          setColumnState(ColumnState.IDLE);
          return;
        }

        console.log(`Moving contact ${contactId} to unassigned`);

        // Call the parent's onContactMove handler
        if (onContactMove) {
          onContactMove(contactId, null);
        }

        setColumnState(ColumnState.IDLE);
      }
    });

    return () => {
      console.log('Cleaning up drop target for unassigned column');
      if (typeof cleanup === 'function') {
        cleanup();
      } else if (cleanup && typeof cleanup.cleanup === 'function') {
        cleanup.cleanup();
      }
    };
  }, [isMultiSelectMode, onContactMove]);

  const handleScroll = (e) => {
    if (isLoadingMore || !hasMore) return;

    const { scrollTop, scrollHeight, clientHeight } = e.target;
    // Load more when user scrolls to 80% of the way down
    if (scrollHeight - scrollTop <= clientHeight * 1.2) {
      loadMoreContacts();
    }
  };

  const loadMoreContacts = async () => {
    if (isLoadingMore || !hasMore || !onLoadMore) return;

    setIsLoadingMore(true);
    const nextPage = currentPage + 1;

    try {
      const result = await onLoadMore(nextPage);

      if (!result.contacts || result.contacts.length === 0) {
        setHasMore(false);
      } else {
        setCurrentPage(nextPage);
      }
    } catch (error) {
      console.error('Failed to load more contacts:', error);
      setHasMore(false);
    } finally {
      setIsLoadingMore(false);
    }
  };

  return (
    <div className="pipeline-column flex-none w-56 md:w-64 h-full flex flex-col bg-gray-200 dark:bg-gray-800 rounded-lg shadow-md border border-gray-300 dark:border-gray-700 transition-shadow duration-200">
      {/* Column Header */}
      <div className="p-2 border-b border-gray-300 dark:border-gray-700 bg-gray-300 dark:bg-gray-700 rounded-t-lg">
        <div className="flex flex-col">
          {/* Stage header with name and count */}
          <div className="flex justify-between items-center mb-1.5">
            <div className="flex items-center">
              <Tooltip content={unassignedStage.name} position="bottom">
                <h3 className="font-semibold text-gray-800 dark:text-white text-sm truncate max-w-[100px] cursor-help">{unassignedStage.name}</h3>
              </Tooltip>
              <Tooltip content={`${contacts.length} contact${contacts.length !== 1 ? 's' : ''} not assigned to any stage`} position="bottom">
                <span className="ml-1.5 px-1.5 py-0.5 text-xs font-medium bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full cursor-help">
                  {contacts.length}
                </span>
              </Tooltip>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-1">
              <Tooltip content="Contacts that are not assigned to any pipeline stage" position="bottom">
                <div className="text-gray-500 dark:text-gray-400 cursor-help p-1">
                  <Info className="h-3.5 w-3.5" />
                </div>
              </Tooltip>

              <PipelineColumnFilter
                onFilterChange={setFilters}
                onSortChange={setSort}
                activeFilters={filters}
                activeSort={sort}
              />
            </div>
          </div>

          {/* Search input - more compact */}
          <div className="relative w-full mb-1">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search..."
              className="w-full px-6 py-0.5 text-xs border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <Search className="absolute left-1.5 top-1 h-3.5 w-3.5 text-gray-400" />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute right-1.5 top-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                title="Clear search"
              >
                <X className="h-3.5 w-3.5" />
              </button>
            )}
          </div>

          {/* Clear all filters button - more compact */}
          {(searchTerm || Object.values(filters).some(v => v)) && (
            <div className="flex justify-end">
              <button
                onClick={() => {
                  setSearchTerm('');
                  setFilters({});
                  setSort({ field: null, direction: 'asc' });
                }}
                className="text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 flex items-center py-0.5"
              >
                <X className="h-3 w-3 mr-0.5" />
                <span className="text-[10px]">Clear filters</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Column Content - Scrollable only when needed */}
      <div
        ref={scrollRef}
        data-stage-id="unassigned"
        className={`pipeline-scroll flex-1 ${contacts.length > 0 ? 'overflow-y-auto' : 'overflow-hidden'} p-2 space-y-2 transition-colors
          ${columnState === ColumnState.DRAG_OVER ? 'bg-blue-50/50 dark:bg-blue-900/20' : 'bg-white dark:bg-gray-800/80'}`}
        style={{ minHeight: '100px' }}
        onScroll={contacts.length > 0 ? handleScroll : undefined}
      >
        {contacts.length === 0 && (
          <div className="h-full flex items-center justify-center text-center p-4">
            <div className="flex flex-col items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-gray-300 dark:text-gray-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <p className="text-sm text-gray-500 dark:text-gray-400">No unassigned contacts</p>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">Drag contacts here</p>
            </div>
          </div>
        )}

        {/* Filtered and sorted contacts */}
        {useMemo(() => {
          // Apply filters and search
          let filteredContacts = [...contacts];

          // Apply search
          if (searchTerm) {
            const search = searchTerm.toLowerCase();
            filteredContacts = filteredContacts.filter(contact => {
              return (
                (contact.firstName && contact.firstName.toLowerCase().includes(search)) ||
                (contact.lastName && contact.lastName.toLowerCase().includes(search)) ||
                (contact.email && contact.email.toLowerCase().includes(search)) ||
                (contact.company && contact.company.toLowerCase().includes(search)) ||
                (contact.phone && contact.phone.toLowerCase().includes(search))
              );
            });
          }

          // Apply filters
          if (filters.name) {
            const nameFilter = filters.name.toLowerCase();
            filteredContacts = filteredContacts.filter(contact => {
              const fullName = `${contact.firstName || ''} ${contact.lastName || ''}`.toLowerCase();
              return fullName.includes(nameFilter);
            });
          }

          if (filters.company) {
            const companyFilter = filters.company.toLowerCase();
            filteredContacts = filteredContacts.filter(contact => {
              return contact.company && contact.company.toLowerCase().includes(companyFilter);
            });
          }

          if (filters.type) {
            filteredContacts = filteredContacts.filter(contact => {
              return contact.type === filters.type;
            });
          }

          // Apply sorting
          if (sort.field) {
            filteredContacts.sort((a, b) => {
              let valueA, valueB;

              // Handle different field types
              if (sort.field === 'name') {
                valueA = `${a.firstName || ''} ${a.lastName || ''}`.toLowerCase();
                valueB = `${b.firstName || ''} ${b.lastName || ''}`.toLowerCase();
              } else if (sort.field === 'company') {
                valueA = (a.company || '').toLowerCase();
                valueB = (b.company || '').toLowerCase();
              } else if (sort.field === 'createdAt' || sort.field === 'lastContactDate') {
                valueA = a[sort.field] ? new Date(a[sort.field]) : new Date(0);
                valueB = b[sort.field] ? new Date(b[sort.field]) : new Date(0);
              } else {
                valueA = a[sort.field] || '';
                valueB = b[sort.field] || '';
              }

              // Compare values based on direction
              if (sort.direction === 'asc') {
                return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
              } else {
                return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
              }
            });
          }

          if (filteredContacts.length > 0) {
            return filteredContacts.map((contact, index) => (
              <div key={contact.id} className="contact-card-wrapper">
                <PragmaticContactCard
                  contact={contact}
                  isMultiSelectMode={isMultiSelectMode}
                  isSelected={selectedContacts.includes(contact.id)}
                  onSelect={onContactSelect}
                />
              </div>
            ));
          } else if (searchTerm || Object.values(filters).some(v => v)) {
            return (
              <div className="text-center p-4 text-gray-500 dark:text-gray-400">
                <p>No contacts match your filters</p>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setFilters({});
                    setSort({ field: null, direction: 'asc' });
                  }}
                  className="text-primary text-sm mt-2 hover:underline"
                >
                  Clear filters
                </button>
              </div>
            );
          } else {
            return (
              <div className="text-center p-4 text-gray-500 dark:text-gray-400">
                <p>No unassigned contacts</p>
              </div>
            );
          }
        }, [contacts, searchTerm, filters, sort, isMultiSelectMode, selectedContacts, onContactSelect])}
        {isLoadingMore && (
          <div className="py-2 text-center text-gray-500 dark:text-gray-400">
            <div className="inline-block animate-spin h-4 w-4 border-2 border-gray-300 dark:border-gray-600 border-t-primary rounded-full mr-2"></div>
            Loading more...
          </div>
        )}
      </div>
    </div>
  );
}
