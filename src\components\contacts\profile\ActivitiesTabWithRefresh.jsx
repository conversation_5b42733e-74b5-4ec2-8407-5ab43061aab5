'use client';

import ActivitiesTab from './ActivitiesTab';
import { useStatsRefresh } from '@/hooks/useStatsRefresh';

export default function ActivitiesTabWithRefresh({ contactId }) {
  const { triggerRefresh } = useStatsRefresh();
  
  // Function to handle activity changes
  const handleActivityChange = () => {
    // Trigger a refresh of the stats
    triggerRefresh(['activities']);
  };
  
  return <ActivitiesTab contactId={contactId} onActivityChange={handleActivityChange} />;
}
