"use client";

// Microsoft Graph API configuration
export const graphConfig = {
  graphEndpoint: "https://graph.microsoft.com/v1.0",
  graphMeEndpoint: "https://graph.microsoft.com/v1.0/me",
};

// Authentication request configuration for different scopes
export const getAuthRequest = (scopeType = "documents") => {
  const scopes = {
    documents: [
      "User.Read",
      "Files.Read",
      "Files.ReadWrite",
      "Files.Read.All",
      "Files.ReadWrite.All",
      "Sites.Read.All",
    ],
    email: [
      "User.Read",
      "Mail.Read",
      "Mail.ReadWrite",
      "Mail.Send",
    ],
    calendar: [
      "User.Read",
      "Calendars.Read",
      "Calendars.ReadWrite",
    ],
    contacts: [
      "User.Read",
      "Contacts.Read",
      "Contacts.ReadWrite",
    ],
    default: ["User.Read"],
  };

  return {
    scopes: scopes[scopeType] || scopes.default,
  };
};
