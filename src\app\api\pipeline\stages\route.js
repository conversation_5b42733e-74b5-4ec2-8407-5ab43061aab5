import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

export async function GET() {
  try {
    const stages = await prisma.pipelineStage.findMany({
      orderBy: {
        order: 'asc'
      }
    });
    return NextResponse.json(stages);
  } catch (error) {
    console.error('Failed to fetch pipeline stages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pipeline stages' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const body = await request.json();

    // Get the current user
    const { user } = await getSignedInUser(request);

    const stage = await prisma.pipelineStage.create({
      data: {
        name: body.name,
        order: body.order
      }
    });

    // Log the pipeline stage creation
    await auditLogger.logPipelineStageCreate({
      userId: user?.id,
      stage,
      request
    });

    return NextResponse.json(stage);
  } catch (error) {
    console.error('Failed to create pipeline stage:', error);
    return NextResponse.json(
      { error: 'Failed to create pipeline stage' },
      { status: 500 }
    );
  }
}

export async function PUT(request) {
  try {
    const stages = await request.json();
    
    // Update all stages in a transaction
    await prisma.$transaction(
      stages.map((stage) => 
        prisma.pipelineStage.upsert({
          where: { 
            id: stage.id || 'new' 
          },
          update: {
            name: stage.name,
            order: stage.order
          },
          create: {
            name: stage.name,
            order: stage.order
          }
        })
      )
    );

    // Delete stages that are no longer in the list
    const stageIds = stages.map(s => s.id).filter(Boolean);
    if (stageIds.length > 0) {
      await prisma.pipelineStage.deleteMany({
        where: {
          id: {
            notIn: stageIds
          }
        }
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to update pipeline stages:', error);
    return NextResponse.json(
      { error: 'Failed to update pipeline stages' },
      { status: 500 }
    );
  }
}