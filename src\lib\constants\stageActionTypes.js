export const STAGE_ACTION_TYPES = [
  {
    id: 'activity',
    label: 'Create Activity',
    icon: '📋',
    color: 'blue',
    description: 'Create an activity when a contact enters this stage'
  },
  {
    id: 'task',
    label: 'Create Task',
    icon: '✓',
    color: 'green',
    description: 'Create a task when a contact enters this stage'
  },
  {
    id: 'note',
    label: 'Create Note',
    icon: '📝',
    color: 'yellow',
    description: 'Create a note when a contact enters this stage'
  },
  {
    id: 'email',
    label: 'Send Email',
    icon: '✉️',
    color: 'indigo',
    description: 'Send an email when a contact enters this stage'
  }
];

export const getActionTypeById = (id) => {
  return STAGE_ACTION_TYPES.find(type => type.id === id) || {
    id: 'unknown',
    label: 'Unknown Action',
    icon: '❓',
    color: 'gray',
    description: 'Unknown action type'
  };
};
