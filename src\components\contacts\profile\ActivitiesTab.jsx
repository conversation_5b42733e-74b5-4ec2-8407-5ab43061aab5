'use client';

import { useState, useEffect } from 'react';
import ActivityForm from './ActivityForm';
import ActivityFilter from './ActivityFilter';
import ActivityTimeline from './ActivityTimeline';
import SearchInput from '@/components/common/SearchInput';

export default function ActivitiesTab({ contactId, onActivityChange }) {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [currentActivity, setCurrentActivity] = useState(null);
  const [filters, setFilters] = useState({
    type: '',
    sortBy: 'date',
    sortOrder: 'desc',
    searchTerm: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  });

  const fetchActivities = async (page = 1) => {
    setLoading(true);
    setError('');
    try {
      console.log('Fetching activities with filters:', filters);

      const queryParams = new URLSearchParams({
        page: page.toString(),
        pageSize: pagination.pageSize.toString(),
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder
      });

      if (filters.type) {
        queryParams.append('type', filters.type);
      }

      // We'll handle search client-side instead of server-side
      // if (filters.searchTerm) {
      //   queryParams.append('search', filters.searchTerm);
      //   console.log('Added search param:', filters.searchTerm);
      // }

      const url = `/api/contacts/${contactId}/activities?${queryParams}`;
      console.log('Fetching from URL:', url);

      const response = await fetch(url);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API response not OK:', response.status, errorText);
        throw new Error(`Failed to fetch activities: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Received data:', { count: data.activities.length, total: data.pagination.total });

      // Apply client-side filtering for search
      let filteredActivities = data.activities || [];
      let totalCount = data.pagination?.total || 0;

      // Only attempt filtering if we have activities and a search term
      if (filteredActivities.length > 0 && filters.searchTerm && filters.searchTerm.trim() !== '') {
        try {
          const searchTerm = filters.searchTerm.toLowerCase().trim();
          console.log('Filtering activities by search term:', searchTerm);

          filteredActivities = data.activities.filter(activity => {
            // Skip invalid activities
            if (!activity) return false;
          // Safely check if title exists and includes search term
          const titleMatch = activity.title ?
            activity.title.toLowerCase().includes(searchTerm) : false;

          // Safely check if description exists and includes search term
          const descriptionMatch = activity.description ?
            activity.description.toLowerCase().includes(searchTerm) : false;

          return titleMatch || descriptionMatch;
        });

        totalCount = filteredActivities.length;
        console.log(`Filtered from ${data.activities.length} to ${filteredActivities.length} activities`);
        } catch (searchError) {
          console.error('Error during search filtering:', searchError);
          // If search filtering fails, use unfiltered data
          filteredActivities = data.activities;
          totalCount = data.pagination.total;
        }
      }

      setActivities(filteredActivities);
      setPagination({
        ...pagination,
        page,
        total: totalCount,
        totalPages: Math.ceil(totalCount / pagination.pageSize)
      });
    } catch (error) {
      console.error('Failed to fetch activities:', error);
      setError(`Failed to load activities: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Fetch activities when component mounts or filters change (except search)
  useEffect(() => {
    console.log('useEffect triggered, fetching activities');
    fetchActivities(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contactId, filters.type, filters.sortBy, filters.sortOrder]);

  // Handle search separately to avoid API calls on every keystroke
  useEffect(() => {
    // Only refetch if we already have activities loaded
    if (activities.length > 0 && !loading) {
      console.log('Search term changed, filtering existing activities');
      // We already have activities loaded, just filter them client-side
      fetchActivities(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters.searchTerm]);

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleSearch = (searchTerm) => {
    console.log('Search term changed:', searchTerm);
    setFilters(prev => ({
      ...prev,
      searchTerm
    }));
  };

  const handleAddActivity = () => {
    setCurrentActivity(null);
    setIsFormOpen(true);
  };

  const handleEditActivity = (activity) => {
    setCurrentActivity(activity);
    setIsFormOpen(true);
  };

  const handleDeleteActivity = async (activityId) => {
    if (!window.confirm('Are you sure you want to delete this activity?')) {
      return;
    }

    try {
      const response = await fetch(`/api/contacts/${contactId}/activities?activityId=${activityId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete activity');
      }

      // Remove the deleted activity from the state
      setActivities(activities.filter(activity => activity.id !== activityId));

      // Update pagination total count
      setPagination(prev => {
        const newTotal = Math.max(0, prev.total - 1);
        return {
          ...prev,
          total: newTotal,
          totalPages: Math.ceil(newTotal / prev.pageSize)
        };
      });

      // Notify parent component that an activity has changed
      if (onActivityChange) onActivityChange();
    } catch (error) {
      console.error('Failed to delete activity:', error);
      alert('Failed to delete activity. Please try again.');
    }
  };

  const handleActivitySaved = (savedActivity) => {
    if (currentActivity) {
      // Update existing activity
      setActivities(activities.map(activity =>
        activity.id === savedActivity.id ? savedActivity : activity
      ));
    } else {
      // Add new activity
      setActivities([savedActivity, ...activities]);

      // Update pagination total count
      setPagination(prev => ({
        ...prev,
        total: prev.total + 1,
        totalPages: Math.ceil((prev.total + 1) / prev.pageSize)
      }));
    }

    // Notify parent component that an activity has changed
    if (onActivityChange) onActivityChange();
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      fetchActivities(newPage);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow h-full overflow-auto">
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Activity Timeline</h3>
          <button
            onClick={handleAddActivity}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
          >
            Add Activity
          </button>
        </div>

        {error && (
          <div className="p-3 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md mb-4">
            {error}
          </div>
        )}

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <SearchInput
            placeholder="Search activities..."
            onSearch={handleSearch}
            initialValue={filters.searchTerm}
          />

          <div className="flex items-center gap-2">
            <ActivityFilter onFilterChange={handleFilterChange} />

            <div className="text-sm font-medium">
              <span className="text-primary">{activities.length}</span> <span className="text-gray-600 dark:text-gray-300">activities found</span>
              {filters.searchTerm && (
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                  {activities.length === 0 ? '(no matches)' : ''}
                </span>
              )}
            </div>
          </div>
        </div>

        {loading ? (
          <div className="py-10 text-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent">
              <span className="sr-only">Loading...</span>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <ActivityTimeline
              activities={activities}
              onEdit={handleEditActivity}
              onDelete={handleDeleteActivity}
            />

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex justify-center space-x-2 pt-4">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                  className="px-3 py-1 rounded-md border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 disabled:opacity-50"
                >
                  Previous
                </button>

                <span className="px-3 py-1 text-gray-700 dark:text-gray-300">
                  Page {pagination.page} of {pagination.totalPages}
                </span>

                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.totalPages}
                  className="px-3 py-1 rounded-md border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            )}
          </div>
        )}

        <ActivityForm
          contactId={contactId}
          activity={currentActivity}
          isOpen={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          onSuccess={handleActivitySaved}
        />
      </div>
    </div>
  );
}