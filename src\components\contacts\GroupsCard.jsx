'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import * as Dialog from '@radix-ui/react-dialog';
import GroupDialog from './GroupDialog';
import GroupMembersDialog from './GroupMembersDialog';

export default function GroupsCard({ onGroupSelect, activeGroupId = '' }) {
  const [groups, setGroups] = useState([]);
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isMembersDialogOpen, setIsMembersDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [activeGroup, setActiveGroup] = useState(null);
  const router = useRouter();

  // Fetch groups
  const fetchGroups = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/groups?includeMemberCounts=true');

      if (!response.ok) {
        throw new Error('Failed to fetch groups');
      }

      const data = await response.json();
      setGroups(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching groups:', err);
      setError('Failed to load groups');
    } finally {
      setLoading(false);
    }
  };

  // Fetch contacts
  const fetchContacts = async () => {
    try {
      const response = await fetch('/api/contacts');

      if (!response.ok) {
        throw new Error('Failed to fetch contacts');
      }

      const data = await response.json();
      setContacts(data);
    } catch (err) {
      console.error('Error fetching contacts:', err);
    }
  };

  useEffect(() => {
    fetchGroups();
    fetchContacts();

    // Listen for the clearAllFilters custom event
    const handleClearAllFiltersEvent = () => {
      setActiveGroup(null);

      // Clear any active group styling
      const activeGroupElements = document.querySelectorAll('.group-card-active');
      activeGroupElements.forEach(element => {
        element.classList.remove('group-card-active', 'bg-primary/10', 'border', 'border-primary/30');
      });
    };

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('clearAllFilters', handleClearAllFiltersEvent);
    }

    // Clean up
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('clearAllFilters', handleClearAllFiltersEvent);
      }
    };
  }, []);

  // Update active group when activeGroupId changes
  useEffect(() => {
    if (activeGroupId) {
      const group = groups.find(g => g.id === activeGroupId);
      setActiveGroup(group || null);
    } else {
      setActiveGroup(null);

      // If we had a previously selected group and now it's cleared,
      // make sure to update the UI to reflect the deselection
      const activeGroupElements = document.querySelectorAll('.group-card-active');
      activeGroupElements.forEach(element => {
        element.classList.remove('group-card-active', 'bg-primary/10', 'border', 'border-primary/30');
      });
    }
  }, [activeGroupId, groups]);

  const handleGroupClick = (group) => {
    if (onGroupSelect) {
      onGroupSelect(group);
    }
  };

  const handleAddGroup = () => {
    setSelectedGroup(null);
    setIsDialogOpen(true);
  };

  const handleEditGroup = (group, e) => {
    e.stopPropagation();
    setSelectedGroup(group);
    setIsDialogOpen(true);
  };

  const handleManageContacts = (group, e) => {
    e.stopPropagation();
    setSelectedGroup(group);
    setIsMembersDialogOpen(true);
  };

  const handleGroupSaved = () => {
    fetchGroups();
    setIsDialogOpen(false);
  };

  const handleMembersUpdated = () => {
    fetchGroups();
  };

  // Generate a color based on the group name (for groups without a color)
  const getGroupColor = (group) => {
    if (group.color) return group.color;

    // Simple hash function to generate a color
    const hash = group.name.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    const hue = Math.abs(hash % 360);
    return `hsl(${hue}, 70%, 50%)`;
  };

  return (
    <div className="card-light dark:bg-gray-800 rounded-lg p-4 mb-4 flex-1 min-h-0">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Contact Groups</h2>
        <button
          onClick={handleAddGroup}
          className="text-primary hover:text-primary-hover"
          title="Add Group"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-4">
          <svg className="animate-spin h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      ) : error ? (
        <div className="text-red-500 dark:text-red-400 text-center py-2">{error}</div>
      ) : groups.length === 0 ? (
        <div className="text-gray-500 dark:text-gray-400 text-center py-4">
          No groups found. Create your first group!
        </div>
      ) : (
        <div className="space-y-2 flex-1 overflow-y-auto pr-1 show-scrollbar">
          {groups.map((group) => (
            <div
              key={group.id}
              onClick={() => handleGroupClick(group)}
              className={`flex items-center justify-between p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer ${activeGroupId === group.id ? 'group-card-active bg-primary/10 border border-primary/30' : ''}`}
            >
              <div className="flex items-center">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: getGroupColor(group) }}
                ></div>
                <span className="text-sm text-gray-700 dark:text-gray-300">{group.name}</span>
              </div>
              <div className="flex items-center">
                <span className="text-xs text-gray-500 dark:text-gray-400 mr-2">
                  {group.memberCount || 0} {(group.memberCount || 0) === 1 ? 'contact' : 'contacts'}
                </span>
                <button
                  onClick={(e) => handleManageContacts(group, e)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-1"
                  title="Manage Contacts"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                  </svg>
                </button>
                <button
                  onClick={(e) => handleEditGroup(group, e)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  title="Edit Group"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      <Dialog.Root open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <GroupDialog
          group={selectedGroup}
          onSave={handleGroupSaved}
          onClose={() => setIsDialogOpen(false)}
        />
      </Dialog.Root>

      <Dialog.Root open={isMembersDialogOpen} onOpenChange={setIsMembersDialogOpen}>
        {selectedGroup && (
          <GroupMembersDialog
            group={selectedGroup}
            contacts={contacts}
            onClose={() => setIsMembersDialogOpen(false)}
            onMembersUpdated={handleMembersUpdated}
          />
        )}
      </Dialog.Root>
    </div>
  );
}
