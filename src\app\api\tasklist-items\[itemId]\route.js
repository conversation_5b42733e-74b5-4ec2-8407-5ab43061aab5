import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { NextResponse } from 'next/server';

// PATCH: Update a tasklist item
export async function PATCH(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { itemId } = await params;
    let data = await request.json();
    // Convert dueDate to ISO string if present and not already ISO
    if (data.dueDate) {
      try {
        // Only convert if not already a valid ISO string with time
        const dateObj = new Date(data.dueDate);
        if (!isNaN(dateObj.getTime())) {
          data.dueDate = dateObj.toISOString();
        }
      } catch {}
    }
    // Only allow access to user's own items unless admin
    const item = await prisma.tasklistItem.findUnique({ where: { id: itemId }, include: { tasklist: true } });
    if (!item || (user.role !== 'admin' && item.tasklist.userId !== user.id)) {
      return NextResponse.json({ error: 'Tasklist item not found' }, { status: 404 });
    }
    const updated = await prisma.tasklistItem.update({ where: { id: itemId }, data });
    return NextResponse.json(updated);
  } catch (error) {
    console.error('Failed to update tasklist item:', error);
    return NextResponse.json({ error: 'Failed to update tasklist item' }, { status: 500 });
  }
}

// DELETE: Delete a tasklist item
export async function DELETE(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { itemId } = await params;
    const item = await prisma.tasklistItem.findUnique({ where: { id: itemId }, include: { tasklist: true } });
    if (!item || (user.role !== 'admin' && item.tasklist.userId !== user.id)) {
      return NextResponse.json({ error: 'Tasklist item not found' }, { status: 404 });
    }
    await prisma.tasklistItem.delete({ where: { id: itemId } });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete tasklist item:', error);
    return NextResponse.json({ error: 'Failed to delete tasklist item' }, { status: 500 });
  }
}
