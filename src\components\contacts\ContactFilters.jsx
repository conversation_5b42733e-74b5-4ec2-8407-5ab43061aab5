'use client';

import { useState, useEffect, useRef } from 'react';
import GroupsCard from './GroupsCard';

export default function ContactFilters({ onFilterChange, contacts = [], columnFilters = {}, searchTerm = '', pipelineStages = {}, pipelineStagesLoaded = false, groupContactsCache = {} }) {

  // Create refs for the dropdown elements
  const typeSelectRef = useRef(null);
  const pipelineSelectRef = useRef(null);
  const [filters, setFilters] = useState({
    type: '',
    groupId: '',
    pipelineStage: '',
  });

  // Get distinct contact types from the contacts data with counts
  const getContactTypesWithCounts = () => {
    if (!contacts || contacts.length === 0) return [];

    const typeCounts = {};
    contacts.forEach(contact => {
      if (contact.type) {
        typeCounts[contact.type] = (typeCounts[contact.type] || 0) + 1;
      }
    });

    return Object.entries(typeCounts)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => a.type.localeCompare(b.type));
  };

  const contactTypesWithCounts = getContactTypesWithCounts();

  // Get pipeline stages with counts
  const getPipelineStagesWithCounts = () => {
    if (!contacts || contacts.length === 0 || Object.keys(pipelineStages).length === 0) return [];

    const stageCounts = {};
    let unassignedCount = 0;

    contacts.forEach(contact => {
      if (contact.pipelineStage) {
        const stageId = contact.pipelineStage;
        const stageName = pipelineStages[stageId] || 'Unknown';
        stageCounts[stageId] = {
          count: (stageCounts[stageId]?.count || 0) + 1,
          name: stageName
        };
      } else {
        unassignedCount++;
      }
    });

    const result = Object.entries(stageCounts)
      .map(([id, data]) => ({
        id,
        name: data.name,
        count: data.count
      }))
      .sort((a, b) => a.name.localeCompare(b.name));

    // Add 'Not Assigned' at the beginning if there are any unassigned contacts
    if (unassignedCount > 0) {
      result.unshift({
        id: 'unassigned',
        name: 'Not Assigned',
        count: unassignedCount
      });
    }

    return result;
  };

  const pipelineStagesWithCounts = getPipelineStagesWithCounts();

  const handleFilterChange = (key, value) => {
    const newFilters = {
      ...filters,
      [key]: value,
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Reset filters when parent component clears them
  useEffect(() => {
    // Check if all filters from parent are empty
    const allFiltersEmpty = !filters.type && !filters.groupId && !filters.pipelineStage;

    // If parent filters are empty but our local state isn't, reset our local state
    if (allFiltersEmpty && (filters.type || filters.groupId || filters.pipelineStage)) {
      setFilters({
        type: '',
        groupId: '',
        pipelineStage: '',
      });

      // Reset dropdown selections to their default values
      if (typeSelectRef.current) {
        typeSelectRef.current.selectedIndex = 0;
        typeSelectRef.current.value = '';
      }

      if (pipelineSelectRef.current) {
        pipelineSelectRef.current.selectedIndex = 0;
        pipelineSelectRef.current.value = '';
      }
    }
  }, [filters]);

  const handleGroupSelect = (group) => {
    // If the same group is clicked again, clear the filter
    const groupId = filters.groupId === group.id ? '' : group.id;
    handleFilterChange('groupId', groupId);
  };

  const handleClearAllFilters = () => {
    // Reset all filters
    setFilters({
      type: '',
      groupId: '',
      pipelineStage: '',
    });

    // Reset dropdown selections to default index
    if (typeSelectRef.current) {
      typeSelectRef.current.selectedIndex = 0;
      typeSelectRef.current.value = '';
    }

    if (pipelineSelectRef.current) {
      pipelineSelectRef.current.selectedIndex = 0;
      pipelineSelectRef.current.value = '';
    }

    // Notify parent component
    onFilterChange({
      type: '',
      groupId: '',
      pipelineStage: '',
    });

    // Dispatch a custom event to notify other components that filters have been cleared
    if (typeof window !== 'undefined') {
      const clearEvent = new CustomEvent('clearAllFilters');
      window.dispatchEvent(clearEvent);
    }
  };

  // (Removed: pipeline stages fetching, now handled by parent)

  // Listen for the clearAllFilters custom event
  useEffect(() => {
    const handleClearAllFiltersEvent = () => {
      // Reset all filters
      setFilters({
        type: '',
        groupId: '',
        pipelineStage: '',
      });

      // Reset dropdown selections to default index
      if (typeSelectRef.current) {
        typeSelectRef.current.selectedIndex = 0;
        typeSelectRef.current.value = '';
      }

      if (pipelineSelectRef.current) {
        pipelineSelectRef.current.selectedIndex = 0;
        pipelineSelectRef.current.value = '';
      }
    };

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('clearAllFilters', handleClearAllFiltersEvent);
    }

    // Clean up
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('clearAllFilters', handleClearAllFiltersEvent);
      }
    };
  }, []);

  return (
    <div className="space-y-4 flex-1 min-h-0 flex flex-col">
      <div className="card-light dark:bg-gray-800 p-4 rounded-lg space-y-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-semibold text-gray-900 dark:text-white">Filters</h3>
          {(Object.values(filters).some(value => value !== '') ||
            Object.keys(columnFilters).length > 0 ||
            searchTerm) && (
            <button
              onClick={handleClearAllFilters}
              className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 flex items-center"
              title="Clear all filters"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              Clear All
            </button>
          )}
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Contact Type</label>
            <select
              name="contactType"
              ref={typeSelectRef}
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="w-full rounded-md border border-gray-400 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="">All Types ({contacts.length})</option>
              {contactTypesWithCounts.length > 0 ? (
                contactTypesWithCounts.map((item, index) => (
                  <option key={index} value={item.type}>
                    {item.type.charAt(0).toUpperCase() + item.type.slice(1)} ({item.count})
                  </option>
                ))
              ) : (
                // Fallback options if no contact types are found
                <>
                  <option value="prospective">Prospective</option>
                  <option value="customer">Customer</option>
                  <option value="partner">Partner</option>
                </>
              )}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Pipeline Stage</label>
            <select
              name="pipelineStage"
              ref={pipelineSelectRef}
              value={filters.pipelineStage}
              onChange={(e) => handleFilterChange('pipelineStage', e.target.value)}
              className="w-full rounded-md border border-gray-400 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              disabled={!pipelineStagesLoaded}
            >
              <option value="">All Stages ({contacts.length})</option>
              {!pipelineStagesLoaded ? (
                <option disabled>Loading stages...</option>
              ) : pipelineStagesWithCounts.length > 0 ? (
                pipelineStagesWithCounts.map((stage) => (
                  <option key={stage.id} value={stage.id}>
                    {stage.name} ({stage.count})
                  </option>
                ))
              ) : (
                <option disabled>No pipeline stages found</option>
              )}
            </select>
          </div>

          {filters.groupId && (
            <div className="mt-2">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Group Filter</label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      // Force a refresh by toggling the filter off and on
                      const currentGroupId = filters.groupId;
                      handleFilterChange('groupId', '');
                      setTimeout(() => handleFilterChange('groupId', currentGroupId), 100);
                    }}
                    className="text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                    title="Refresh group filter"
                  >
                    Refresh
                  </button>
                  <button
                    onClick={() => handleFilterChange('groupId', '')}
                    className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  >
                    Clear
                  </button>
                </div>
              </div>
              <div className="mt-1 px-3 py-2 bg-primary/10 text-primary rounded-md text-sm">
                Filtering by group
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 min-h-0 flex flex-col">
        <GroupsCard onGroupSelect={handleGroupSelect} activeGroupId={filters.groupId} />
      </div>
    </div>
  );
}