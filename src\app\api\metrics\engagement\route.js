import { NextResponse } from "next/server";
import prisma from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";

export async function GET(request) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);

    // Build where clause
    const whereClause = {};

    // Only filter by user ID if not an admin
    if (user.role !== "admin") {
      whereClause.userId = user.id;
    }

    // Get total contacts count
    const totalContacts = await prisma.contact.count({
      where: whereClause,
    });

    // Get active contacts (with activity in the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get contacts with recent activity
    const activityWhereClause = {
      OR: [
        {
          activities: {
            some: {
              date: {
                gte: thirtyDaysAgo,
              },
            },
          },
        },
        {
          tasklists: {
            some: {
              createdAt: {
                gte: thirtyDaysAgo,
              },
            },
          },
        },
        {
          notes: {
            some: {
              createdAt: {
                gte: thirtyDaysAgo,
              },
            },
          },
        },
      ],
    };

    // Only filter by user ID if not an admin
    if (user.role !== "admin") {
      activityWhereClause.userId = user.id;
    }

    const contactsWithRecentActivity = await prisma.contact.findMany({
      where: activityWhereClause,
      select: {
        id: true,
      },
    });

    const activeContactsCount = contactsWithRecentActivity.length;

    // Calculate active contacts percentage change from previous month
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

    // Build where clause for previous month activity
    const previousMonthWhereClause = {
      OR: [
        {
          activities: {
            some: {
              date: {
                gte: sixtyDaysAgo,
                lt: thirtyDaysAgo,
              },
            },
          },
        },
        {
          tasklists: {
            some: {
              createdAt: {
                gte: sixtyDaysAgo,
                lt: thirtyDaysAgo,
              },
            },
          },
        },
        {
          notes: {
            some: {
              createdAt: {
                gte: sixtyDaysAgo,
                lt: thirtyDaysAgo,
              },
            },
          },
        },
      ],
    };

    // Only filter by user ID if not an admin
    if (user.role !== "admin") {
      previousMonthWhereClause.userId = user.id;
    }

    const contactsWithPreviousMonthActivity = await prisma.contact.findMany({
      where: previousMonthWhereClause,
      select: {
        id: true,
      },
    });

    const previousMonthActiveCount = contactsWithPreviousMonthActivity.length;

    // Calculate trend percentage
    let activeContactsTrend = 0;
    if (previousMonthActiveCount > 0) {
      activeContactsTrend = Math.round(
        ((activeContactsCount - previousMonthActiveCount) /
          previousMonthActiveCount) *
          100
      );
    }

    // For now, we'll use mock data for the other metrics
    // In a real implementation, these would be calculated from actual data
    const mockEngagementMetrics = {
      avgResponseTime: "3.2 hours",
      responseTimeTrend: -5, // negative means improved (faster)
      responseRate: "78%",
      responseRateTrend: 3,
      conversionRate: "12%",
      conversionRateTrend: 2,
    };

    // Combine real and mock data
    const engagementMetrics = {
      activeContactsCount,
      activeContactsTrend,
      activeContactsPercentage:
        totalContacts > 0
          ? Math.round((activeContactsCount / totalContacts) * 100)
          : 0,
      ...mockEngagementMetrics,
    };

    return NextResponse.json(engagementMetrics);
  } catch (error) {
    console.error("Failed to fetch engagement metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch engagement metrics" },
      { status: 500 }
    );
  }
}
