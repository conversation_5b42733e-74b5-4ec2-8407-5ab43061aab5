"use client";

import { useState } from "react";
import { 
  <PERSON>Chart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  AreaChart,
  Area,
  Legend
} from "recharts";
import { 
  Activity, 
  TrendingUp, 
  Calendar,
  BarChart3
} from "lucide-react";
import CustomTooltip from "@/components/ui/Tooltip";
import * as LucideIcons from "lucide-react";

export default function ActivityTrendsChart({ data, compact = false }) {
  const [chartType, setChartType] = useState('line');

  // Activity type mapping with proper names, icons, and colors
  const activityTypeMap = {
    'call': { name: 'Call', icon: 'Phone', color: '#3b82f6' },
    'email': { name: 'Email', icon: 'Mail', color: '#10b981' },
    'meeting': { name: 'Meeting', icon: 'Users', color: '#6366f1' },
    'note': { name: 'Note', icon: 'FileText', color: '#f59e0b' },
    'task': { name: 'Task', icon: 'CheckSquare', color: '#8b5cf6' },
    'demo': { name: 'Demo', icon: 'Monitor', color: '#ec4899' },
    'proposal': { name: 'Proposal', icon: 'FileText', color: '#f97316' },
    'follow-up': { name: 'Follow-up', icon: 'RotateCcw', color: '#14b8a6' }
  };

  const getActivityTypeInfo = (typeId) => {
    return activityTypeMap[typeId] || {
      name: typeId ? typeId.charAt(0).toUpperCase() + typeId.slice(1) : 'Unknown',
      icon: 'Activity',
      color: '#6b7280'
    };
  };

  if (!data || data.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Activity Trends
        </h3>
        <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
          No activity data available
        </div>
      </div>
    );
  }

  // Process data for chart with proper activity names
  const chartData = data.map(item => {
    const processedItem = {
      date: new Date(item.date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      }),
      fullDate: item.date,
      total: item.total
    };

    // Convert activity type IDs to proper names
    Object.keys(item.byType || {}).forEach(typeId => {
      const typeInfo = getActivityTypeInfo(typeId);
      processedItem[typeInfo.name] = item.byType[typeId];
    });

    return processedItem;
  });

  // Get all activity types for legend (using proper names)
  const activityTypes = data.reduce((types, item) => {
    Object.keys(item.byType || {}).forEach(typeId => {
      const typeInfo = getActivityTypeInfo(typeId);
      if (!types.includes(typeInfo.name)) {
        types.push(typeInfo.name);
      }
    });
    return types;
  }, []);

  // Color palette for different activity types
  const colors = [
    '#3b82f6', // blue
    '#10b981', // green
    '#f59e0b', // amber
    '#ef4444', // red
    '#8b5cf6', // purple
    '#06b6d4', // cyan
    '#84cc16', // lime
    '#f97316'  // orange
  ];

  const getTypeColor = (typeName) => {
    // Find the original type ID for this name and return its color
    const originalData = data.find(item => item.byType);
    if (originalData) {
      const typeId = Object.keys(originalData.byType).find(id =>
        getActivityTypeInfo(id).name === typeName
      );
      if (typeId) {
        return getActivityTypeInfo(typeId).color;
      }
    }
    return '#6b7280'; // fallback gray
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
            {new Date(data.fullDate).toLocaleDateString('en-US', { 
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </h4>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Total Activities:</span>
              <span className="font-medium text-gray-900 dark:text-white">{data.total}</span>
            </div>
            {Object.entries(data.byType || {}).map(([type, count]) => (
              <div key={type} className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">{type}:</span>
                <span className="font-medium text-gray-900 dark:text-white">{count}</span>
              </div>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };

  // Calculate summary statistics
  const totalActivities = chartData.reduce((sum, item) => sum + item.total, 0);
  const averageDaily = totalActivities / chartData.length;
  const maxDaily = Math.max(...chartData.map(item => item.total));
  const trend = chartData.length > 1 
    ? ((chartData[chartData.length - 1].total - chartData[0].total) / chartData[0].total) * 100
    : 0;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      {/* Header */}
      <div className={`flex items-center justify-between ${compact ? "mb-3" : "mb-6"}`}>
        <div>
          <CustomTooltip content="Daily activity patterns showing engagement trends across different activity types (calls, emails, meetings, notes)" position="top">
            <h3 className={`font-semibold text-gray-900 dark:text-white ${compact ? "text-base" : "text-lg"}`}>
              Activity Trends
            </h3>
          </CustomTooltip>
          {!compact && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Daily activity patterns over time
            </p>
          )}
        </div>
        
        {/* Chart Type Toggle */}
        <div className="flex space-x-2">
          <button
            onClick={() => setChartType('line')}
            className={`p-2 rounded-lg transition-colors ${
              chartType === 'line'
                ? 'bg-primary text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            <TrendingUp className="h-4 w-4" />
          </button>
          <button
            onClick={() => setChartType('area')}
            className={`p-2 rounded-lg transition-colors ${
              chartType === 'area'
                ? 'bg-primary text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            <BarChart3 className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Chart */}
      <div className={`mb-4 ${compact ? "h-48" : "h-80"}`}>
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'line' ? (
            <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
              <XAxis 
                dataKey="date" 
                stroke="#6b7280"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis stroke="#6b7280" fontSize={12} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="total" 
                stroke="#3b82f6" 
                strokeWidth={3}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6 }}
                name="Total Activities"
              />
              {activityTypes.map((type) => (
                <Line
                  key={type}
                  type="monotone"
                  dataKey={type}
                  stroke={getTypeColor(type)}
                  strokeWidth={2}
                  dot={{ fill: getTypeColor(type), strokeWidth: 1, r: 3 }}
                  name={type}
                />
              ))}
            </LineChart>
          ) : (
            <AreaChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
              <XAxis 
                dataKey="date" 
                stroke="#6b7280"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis stroke="#6b7280" fontSize={12} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Area
                type="monotone"
                dataKey="total"
                stackId="1"
                stroke="#3b82f6"
                fill="#3b82f6"
                fillOpacity={0.6}
                name="Total Activities"
              />
            </AreaChart>
          )}
        </ResponsiveContainer>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* Total Activities */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Total Activities
            </h4>
            <Activity className="h-4 w-4 text-blue-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {totalActivities.toLocaleString()}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            In selected period
          </p>
        </div>

        {/* Daily Average */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Daily Average
            </h4>
            <Calendar className="h-4 w-4 text-green-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {averageDaily.toFixed(1)}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Activities per day
          </p>
        </div>

        {/* Peak Day */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Peak Day
            </h4>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {maxDaily}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Highest single day
          </p>
        </div>

        {/* Trend */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Trend
            </h4>
            <TrendingUp className={`h-4 w-4 ${trend >= 0 ? 'text-green-500' : 'text-red-500'}`} />
          </div>
          <div className={`text-xl font-bold ${trend >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
            {trend >= 0 ? '+' : ''}{trend.toFixed(1)}%
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Period over period
          </p>
        </div>
      </div>

      {/* Activity Type Breakdown */}
      {activityTypes.length > 0 && (
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
            Activity Type Breakdown
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {activityTypes.map((typeName) => {
              const typeTotal = chartData.reduce((sum, item) => sum + (item[typeName] || 0), 0);
              const percentage = totalActivities > 0 ? (typeTotal / totalActivities) * 100 : 0;

              // Find the original type ID to get icon info
              const originalData = data.find(item => item.byType);
              const typeId = originalData ? Object.keys(originalData.byType).find(id =>
                getActivityTypeInfo(id).name === typeName
              ) : null;
              const typeInfo = typeId ? getActivityTypeInfo(typeId) : { icon: 'Activity', color: '#6b7280' };
              const IconComponent = LucideIcons[typeInfo.icon] || LucideIcons.Activity;

              return (
                <div key={typeName} className="flex items-center space-x-2">
                  <div
                    className="w-6 h-6 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: typeInfo.color }}
                  >
                    <IconComponent className="w-3 h-3 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {typeName}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {typeTotal} ({percentage.toFixed(1)}%)
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
