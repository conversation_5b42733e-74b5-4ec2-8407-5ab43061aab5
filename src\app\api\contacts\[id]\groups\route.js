import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";

export async function GET(request, { params }) {
  try {
    const { id } = await params;
    // Get the current user
    const { user } = await getSignedInUser(request);
    let whereClause = { id: String(id) };
    if (user.role !== 'admin') {
      whereClause.userId = String(user.id);
    }
    // Verify contact exists and is accessible
    const contact = await prisma.contact.findFirst({ where: whereClause });
    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }
    // Get groups the contact belongs to
    const memberships = await prisma.contactGroupMembership.findMany({
      where: { contactId: id },
      include: {
        group: true,
      },
    });
    // Extract just the group data
    const groups = memberships.map((membership) => membership.group);
    return NextResponse.json(groups);
  } catch (error) {
    console.error('Failed to fetch contact groups:', error);
    return NextResponse.json(
      { error: `Failed to fetch contact groups: ${error.message}` },
      { status: 500 }
    );
  }
}
