'use client';

import { useState, useRef, useEffect } from 'react';
import { X, ChevronDown } from 'lucide-react';

const TagSelector = ({ value = [], onChange, availableTags = [], loading = false, disabled = false }) => {
  // Ensure value is always an array to prevent undefined errors
  const safeValue = Array.isArray(value) ? value : [];
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const removeTag = (tagToRemove) => {
    if (disabled) return;
    onChange(safeValue.filter(tag => tag !== tagToRemove));
  };

  const addTag = (tagToAdd) => {
    if (disabled || safeValue.includes(tagToAdd)) return;
    onChange([...safeValue, tagToAdd]);
    setSearchTerm('');
  };

  const toggleDropdown = () => {
    if (disabled) return;
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchTerm('');
    }
  };

  // Filter available tags based on search term and exclude already selected tags
  const safeAvailableTags = Array.isArray(availableTags) ? availableTags : [];
  const filteredTags = safeAvailableTags.filter(tag =>
    !safeValue.includes(tag) &&
    tag.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="relative w-full" ref={dropdownRef}>
      {/* Selected tags and dropdown trigger */}
      <div 
        className={`flex flex-wrap items-center gap-1 border rounded px-2 py-1 bg-white dark:bg-gray-700 min-h-10 w-full cursor-pointer ${
          disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 dark:hover:border-gray-500'
        } ${isOpen ? 'border-primary ring-1 ring-primary' : 'border-gray-300 dark:border-gray-600'}`}
        onClick={toggleDropdown}
      >
        {/* Selected tags */}
        {safeValue.map(tag => (
          <span 
            key={tag} 
            className="flex items-center bg-primary/10 text-primary px-2 py-0.5 rounded-full text-xs"
          >
            {tag}
            <button
              type="button"
              className="ml-1 text-primary hover:text-red-500 focus:outline-none"
              onClick={(e) => {
                e.stopPropagation();
                removeTag(tag);
              }}
              disabled={disabled}
              aria-label={`Remove ${tag} tag`}
            >
              <X className="w-3 h-3" />
            </button>
          </span>
        ))}
        
        {/* Placeholder or dropdown indicator */}
        <div className="flex-1 flex items-center justify-between min-w-0">
          <span className={`text-xs ${safeValue.length === 0 ? 'text-gray-500 dark:text-gray-400' : 'text-transparent'}`}>
            {safeValue.length === 0 ? 'Select tags...' : ''}
          </span>
          <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 mt-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
          {/* Search input */}
          <div className="p-2 border-b border-gray-200 dark:border-gray-700">
            <input
              ref={searchInputRef}
              type="text"
              className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
              placeholder="Search tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onClick={(e) => e.stopPropagation()}
            />
          </div>

          {/* Tag options */}
          <div className="max-h-40 overflow-y-auto">
            {loading ? (
              <div className="px-3 py-2 text-xs text-gray-500 dark:text-gray-400">
                Loading tags...
              </div>
            ) : filteredTags.length > 0 ? (
              filteredTags.map(tag => (
                <div
                  key={tag}
                  className="px-3 py-2 text-xs cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    addTag(tag);
                  }}
                >
                  {tag}
                </div>
              ))
            ) : searchTerm ? (
              <div className="px-3 py-2 text-xs text-gray-500 dark:text-gray-400">
                No tags found matching "{searchTerm}"
              </div>
            ) : (
              <div className="px-3 py-2 text-xs text-gray-500 dark:text-gray-400">
                {safeAvailableTags.length === 0
                  ? 'No tags available. Contact your administrator to add tags.'
                  : 'All available tags are already selected'
                }
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TagSelector;
