"use client";

import { useState, useEffect, useRef } from "react";
import {
  draggable,
  dropTargetForElements,
} from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { setCustomNativeDragPreview } from "@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview";
import { pointerOutsideOfPreview } from "@atlaskit/pragmatic-drag-and-drop/element/pointer-outside-of-preview";
import ReactDOM from "react-dom";
import Tooltip from "@/components/ui/Tooltip";
import ManageActionsDialog from "./ManageActionsDialog";
import { getActionTypeById } from "@/lib/constants/stageActionTypes";

// Define the state types for the sortable stage
const StageState = {
  IDLE: "idle",
  DRAGGING: "dragging",
  DRAG_OVER_TOP: "drag-over-top",
  DRAG_OVER_BOTTOM: "drag-over-bottom",
};

// Sortable stage component for the pipeline stages
export default function PragmaticSortableStage({
  stage,
  index,
  onReorder,
  isEditing,
  isConfirmingDelete,
  onEdit,
  onCancelEdit,
  onSaveEdit,
  onDelete,
  onCancelDelete,
  onConfirmDelete,
  onManageActions,
  editName,
  setEditName,
  contactsInStage,
  stageActions = [],
}) {
  const stageRef = useRef(null);
  const dragHandleRef = useRef(null);
  const [state, setState] = useState(StageState.IDLE);
  const [previewContainer, setPreviewContainer] = useState(null);
  const [isManageActionsOpen, setIsManageActionsOpen] = useState(false);

  const id = stage.id || `new-${index}`;
  const disabled = isEditing || isConfirmingDelete;

  // Set up draggable
  useEffect(() => {
    if (!stageRef.current || !dragHandleRef.current || disabled) return;

    console.log(`Setting up draggable for stage ${id} at index ${index}`);

    // Create the draggable
    const draggableCleanup = draggable({
      element: stageRef.current,
      dragHandle: dragHandleRef.current,
      getInitialData: () => ({ id, index, type: "pipeline-stage" }),
      onGenerateDragPreview({ nativeSetDragImage }) {
        setCustomNativeDragPreview({
          nativeSetDragImage,
          getOffset: pointerOutsideOfPreview({
            x: 16,
            y: 8,
          }),
          render({ container }) {
            setPreviewContainer(container);
            return () => setState(StageState.DRAGGING);
          },
        });
      },
      onDragStart: (e) => {
        console.log(`Started dragging stage ${id}`);
        setState(StageState.DRAGGING);

        // Set data for HTML5 drag and drop (for the empty drop target)
        if (e.nativeEvent && e.nativeEvent.dataTransfer) {
          e.nativeEvent.dataTransfer.setData('text/plain', JSON.stringify({
            id,
            index,
            type: "pipeline-stage"
          }));
        }

        // Add a class to the body to indicate dragging is in progress
        document.body.classList.add("is-dragging-stage");
      },
      onDrop: () => {
        console.log(`Dropped stage ${id}`);
        setState(StageState.IDLE);
        setPreviewContainer(null);
        // Remove the class from the body
        document.body.classList.remove("is-dragging-stage");
      },
    });

    // Create the drop target
    const dropTargetCleanup = dropTargetForElements({
      element: stageRef.current,
      getIsSticky: () => true,
      canDrop: ({ source }) => {
        // Don't allow dropping on yourself
        if (source.element === stageRef.current) {
          return false;
        }
        // Only allow pipeline stages to be dropped
        return source.data?.type === "pipeline-stage";
      },
      getData: () => ({ id, index, type: "pipeline-stage" }),
      onDragEnter: ({ source, location }) => {
        if (source.element === stageRef.current) return;
        console.log(`Drag entered stage ${id}`);

        // Get the mouse position from the location
        const mouseY = location.current.input.clientY;

        // Determine if we're over the top or bottom half
        const rect = stageRef.current.getBoundingClientRect();
        const position = mouseY < rect.top + rect.height / 2 ? "top" : "bottom";

        setState(
          position === "top"
            ? StageState.DRAG_OVER_TOP
            : StageState.DRAG_OVER_BOTTOM
        );
      },
      onDrag: ({ source, location }) => {
        if (source.element === stageRef.current) return;

        // Get the mouse position from the location
        const mouseY = location.current.input.clientY;

        // Determine if we're over the top or bottom half
        const rect = stageRef.current.getBoundingClientRect();
        const position = mouseY < rect.top + rect.height / 2 ? "top" : "bottom";

        setState(
          position === "top"
            ? StageState.DRAG_OVER_TOP
            : StageState.DRAG_OVER_BOTTOM
        );
      },
      onDragLeave: () => {
        console.log(`Drag left stage ${id}`);
        setState(StageState.IDLE);
      },
      onDrop: ({ source, location }) => {
        console.log(`Drop on stage ${id}`);

        // Get the source data
        const sourceId = source.data?.id;
        const sourceIndex = source.data?.index;

        if (sourceId === undefined || sourceIndex === undefined) {
          console.log("Missing source data", source.data);
          setState(StageState.IDLE);
          return;
        }

        // Get the mouse position from the location
        const mouseY = location.current.input.clientY;

        // Determine if we're over the top or bottom half
        const rect = stageRef.current.getBoundingClientRect();
        const position = mouseY < rect.top + rect.height / 2 ? "top" : "bottom";

        console.log(
          `Dropping ${sourceId} (${sourceIndex}) onto ${id} (${index}) at ${position}`
        );

        // Call the parent's onReorder handler
        if (onReorder) {
          onReorder(sourceId, sourceIndex, id, index, position);
        }

        setState(StageState.IDLE);
      },
    });

    // Combine the cleanups
    return () => {
      console.log(`Cleaning up draggable for stage ${id}`);
      if (typeof draggableCleanup === "function") {
        draggableCleanup();
      } else if (
        draggableCleanup &&
        typeof draggableCleanup.cleanup === "function"
      ) {
        draggableCleanup.cleanup();
      }

      if (typeof dropTargetCleanup === "function") {
        dropTargetCleanup();
      } else if (
        dropTargetCleanup &&
        typeof dropTargetCleanup.cleanup === "function"
      ) {
        dropTargetCleanup.cleanup();
      }
    };
  }, [id, index, disabled, onReorder]);

  // Determine if we should show a drop indicator and where
  const showTopIndicator = state === StageState.DRAG_OVER_TOP;
  const showBottomIndicator = state === StageState.DRAG_OVER_BOTTOM;
  const isDragging = state === StageState.DRAGGING;

  // Render different content based on the current state (edit mode, delete confirmation, or normal)
  const renderContent = () => {
    if (isConfirmingDelete) {
      return (
        <div className="p-4">
          <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
            {contactsInStage > 0
              ? `Warning: This stage contains ${contactsInStage} contact${
                  contactsInStage > 1 ? "s" : ""
                }. If you delete this stage, these contacts will no longer be in any pipeline stage.`
              : "Are you sure you want to delete this stage?"}
          </p>
          <div className="flex justify-end gap-2">
            <button
              onClick={onCancelDelete}
              className="px-3 py-1 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            >
              Cancel
            </button>
            <button
              onClick={onConfirmDelete}
              className={`px-3 py-1 text-sm ${
                contactsInStage > 0 ? "bg-red-500" : "bg-red-600"
              } text-white hover:bg-red-700 rounded`}
            >
              {contactsInStage > 0 ? "Delete Anyway" : "Delete"}
            </button>
          </div>
        </div>
      );
    } else if (isEditing) {
      return (
        <div className="p-4 flex items-center gap-2">
          <input
            type="text"
            value={editName}
            onChange={(e) => setEditName(e.target.value)}
            className="flex-1 rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            placeholder="Stage name"
            autoFocus
          />
          <button
            onClick={onCancelEdit}
            className="px-3 py-1 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
          >
            Cancel
          </button>
          <button
            onClick={onSaveEdit}
            className="px-3 py-1 text-sm bg-primary text-white hover:bg-primary-hover rounded"
          >
            Save
          </button>
        </div>
      );
    } else {
      return (
        <div className="p-4 flex items-center justify-between">
          <div className="flex items-center">
            <div
              ref={dragHandleRef}
              className="drag-handle mr-3"
              title="Drag to reorder"
              aria-label={`Reorder ${stage.name} stage`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
                stroke="none"
              >
                <circle cx="9" cy="5" r="1.5"></circle>
                <circle cx="9" cy="12" r="1.5"></circle>
                <circle cx="9" cy="19" r="1.5"></circle>
                <circle cx="15" cy="5" r="1.5"></circle>
                <circle cx="15" cy="12" r="1.5"></circle>
                <circle cx="15" cy="19" r="1.5"></circle>
              </svg>
            </div>
            <div className="flex items-center">
              <span className="font-medium text-gray-900 dark:text-white">
                {stage.name}
              </span>
              {stageActions.length > 0 && (
                <div className="relative group">
                  <div
                    className="flex items-center ml-2 text-amber-500 dark:text-amber-400 cursor-pointer hover:text-amber-600 dark:hover:text-amber-300"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setIsManageActionsOpen(true);
                    }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                    <span className="text-xs ml-0.5">
                      {stageActions.length}
                    </span>
                  </div>

                  {/* Custom tooltip positioned to the middle right and lowered slightly */}
                  <div className="absolute top-1/2 left-full ml-2 -translate-y-1/3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="bg-gray-900 dark:bg-gray-800 text-white rounded shadow-lg p-3 text-xs max-w-[200px]">
                      <p className="font-medium mb-1">Automated actions:</p>
                      <ul className="space-y-1">
                        {stageActions.map((action) => {
                          const actionType = getActionTypeById(
                            action.actionType
                          );
                          return (
                            <li key={action.id} className="flex items-center">
                              <span className="mr-1">{actionType.icon}</span>
                              <span>{actionType.label}</span>
                            </li>
                          );
                        })}
                      </ul>
                      {/* Arrow pointing left */}
                      <div className="absolute top-1/3 -left-1.5 -mt-1.5 w-3 h-3 bg-gray-900 dark:bg-gray-800 transform rotate-45"></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-1">
            <button
              onClick={onManageActions}
              className="p-2 text-primary hover:text-primary-hover dark:text-primary-light dark:hover:text-primary-light-hover hover:scale-110 active:scale-95 transition-all duration-150"
              title="Manage Actions"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </button>
            <button
              onClick={onEdit}
              className="p-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 hover:scale-110 active:scale-95 transition-all duration-150"
              title="Edit"
            >
              ✏️
            </button>
            <button
              onClick={onDelete}
              className="p-2 text-red-600 hover:text-red-700 dark:text-red-500 dark:hover:text-red-400 hover:scale-110 active:scale-95 transition-all duration-150"
              title="Delete"
            >
              🗑️
            </button>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="relative">
      <div
        ref={stageRef}
        data-id={id}
        data-index={index}
        className={`stage-item border-b border-gray-200 dark:border-gray-700 last:border-b-0
          ${
            isConfirmingDelete
              ? "bg-red-50 dark:bg-red-900/10"
              : isDragging
              ? "stage-item-dragging"
              : "bg-white dark:bg-gray-800"
          }`}
      >
        {renderContent()}
      </div>

      {/* Drop indicators */}
      {showTopIndicator && <div className="drop-indicator top" />}
      {showBottomIndicator && <div className="drop-indicator bottom" />}

      {/* Drag preview */}
      {previewContainer &&
        ReactDOM.createPortal(
          <div className="stage-drag-preview">{stage.name}</div>,
          previewContainer
        )}

      {/* Manage Actions Dialog */}
      {stage.id && (
        <ManageActionsDialog
          stage={stage}
          isOpen={isManageActionsOpen}
          onClose={() => setIsManageActionsOpen(false)}
          onActionsChange={() => {
            // We don't need to refresh actions here as this component
            // gets its actions from the parent component
          }}
        />
      )}
    </div>
  );
}
