import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';

// GET /api/workflows/all - Get all workflows (simplified for loop detection)
export async function GET(request) {
  try {
    const { user } = await getSignedInUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Get all workflows with their triggers, conditions, and actions
    const workflows = await prisma.workflow.findMany({
      where: {
        userId,
      },
      select: {
        id: true,
        name: true,
        isActive: true,
        source: true,
        trigger: {
          select: {
            triggerType: true,
            config: true,
          },
        },
        conditions: {
          select: {
            field: true,
            operator: true,
            value: true,
            order: true,
          },
          orderBy: {
            order: 'asc',
          },
        },
        actions: {
          select: {
            actionType: true,
            config: true,
            order: true,
          },
          orderBy: {
            order: 'asc',
          },
        },
      },
    });

    // Return the actual workflows from the database, even if empty
    // This allows the loop detection to work with real data

    return NextResponse.json(workflows);
  } catch (error) {
    console.error('Error fetching workflows:', error);
    return NextResponse.json({ error: 'Failed to fetch workflows' }, { status: 500 });
  }
}
