"use client";

import { useState, useMemo } from "react";
import {
  AlertTriangle,
  Clock,
  TrendingDown,
  User,
  Mail,
  Phone,
  Building,
  Calendar,
  Activity,
  Search,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Eye,
  MessageSquare,
  UserPlus
} from "lucide-react";
import * as Tooltip from "@radix-ui/react-tooltip";

export default function RiskAnalysisTable({ data }) {
  const [activeTab, setActiveTab] = useState('high-risk');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState('lastActivityDate');
  const [sortDirection, setSortDirection] = useState('asc');
  const [stageFilter, setStageFilter] = useState('');
  const [riskFilter, setRiskFilter] = useState('');

  // Risk definitions
  const riskDefinitions = {
    'high-risk': {
      title: 'High Risk Contacts',
      description: 'Contacts at high risk of churning',
      definition: 'Churning means contacts who are likely to disengage or leave the pipeline. This includes contacts with engagement scores below 30, no activity in 30+ days, or stuck in a stage for 90+ days.',
      criteria: ['Engagement score < 30', 'No activity in 30+ days', 'Stuck in stage 90+ days']
    },
    'dormant': {
      title: 'Dormant Contacts',
      description: 'Contacts with no recent activity',
      definition: 'Recent activity means any interaction (calls, emails, meetings, notes) within the last 30 days. Dormant contacts have had no recorded activities in this timeframe.',
      criteria: ['No activity in 30+ days', 'No engagement tracking']
    },
    'stuck': {
      title: 'Stuck in Pipeline',
      description: 'Contacts stuck in stages too long',
      definition: 'Too long means contacts who have been in their current pipeline stage for more than 60 days, which typically indicates a stalled sales process.',
      criteria: ['60+ days in current stage', 'No stage progression', 'Potential bottleneck']
    }
  };

  if (!data) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Risk Analysis
        </h3>
        <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
          No risk data available
        </div>
      </div>
    );
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatDaysAgo = (dateString) => {
    if (!dateString) return 'Never';
    const days = Math.floor((new Date() - new Date(dateString)) / (1000 * 60 * 60 * 24));
    if (days === 0) return 'Today';
    if (days === 1) return '1 day ago';
    return `${days} days ago`;
  };

  const getContactName = (contact) => {
    if (!contact) return 'Unknown Contact';
    return `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || contact.email || 'Unknown Contact';
  };

  const getRiskBadge = (riskLevel) => {
    const colors = {
      high: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
      medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
      low: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[riskLevel] || colors.low}`}>
        {riskLevel?.charAt(0).toUpperCase() + riskLevel?.slice(1) || 'Low'}
      </span>
    );
  };

  const tabs = [
    {
      id: 'high-risk',
      label: 'High Risk',
      icon: AlertTriangle,
      data: data.highRiskContacts || [],
      description: riskDefinitions['high-risk'].description
    },
    {
      id: 'dormant',
      label: 'Dormant',
      icon: TrendingDown,
      data: data.dormantContacts || [],
      description: riskDefinitions['dormant'].description
    },
    {
      id: 'stuck',
      label: 'Stuck in Pipeline',
      icon: Clock,
      data: data.stuckContacts || [],
      description: riskDefinitions['stuck'].description
    }
  ];

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  // Get unique stages for filter
  const uniqueStages = useMemo(() => {
    const stages = new Set();
    activeTabData?.data.forEach(item => {
      if (item.stageName && item.stageName !== 'No Stage') {
        stages.add(item.stageName);
      }
    });
    return Array.from(stages).sort();
  }, [activeTabData?.data]);

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {
    if (!activeTabData?.data) return [];

    let filtered = activeTabData.data.filter(item => {
      const contactName = getContactName(item.contact).toLowerCase();
      const email = item.contact?.email?.toLowerCase() || '';
      const company = item.contact?.company?.toLowerCase() || '';
      const stage = item.stageName?.toLowerCase() || '';

      const matchesSearch = searchTerm === '' ||
        contactName.includes(searchTerm.toLowerCase()) ||
        email.includes(searchTerm.toLowerCase()) ||
        company.includes(searchTerm.toLowerCase());

      const matchesStage = stageFilter === '' || item.stageName === stageFilter;

      const matchesRisk = riskFilter === '' ||
        (activeTab === 'high-risk' && item.riskLevel === riskFilter);

      return matchesSearch && matchesStage && matchesRisk;
    });

    // Sort data
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortField) {
        case 'name':
          aValue = getContactName(a.contact);
          bValue = getContactName(b.contact);
          break;
        case 'email':
          aValue = a.contact?.email || '';
          bValue = b.contact?.email || '';
          break;
        case 'company':
          aValue = a.contact?.company || '';
          bValue = b.contact?.company || '';
          break;
        case 'stageName':
          aValue = a.stageName || '';
          bValue = b.stageName || '';
          break;
        case 'lastActivityDate':
          aValue = a.lastActivityDate ? new Date(a.lastActivityDate) : new Date(0);
          bValue = b.lastActivityDate ? new Date(b.lastActivityDate) : new Date(0);
          break;
        case 'currentStageDays':
          aValue = a.currentStageDays || 0;
          bValue = b.currentStageDays || 0;
          break;
        case 'daysInPipeline':
          aValue = a.daysInPipeline || 0;
          bValue = b.daysInPipeline || 0;
          break;
        case 'engagementScore':
          aValue = a.engagementScore || 0;
          bValue = b.engagementScore || 0;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [activeTabData?.data, searchTerm, stageFilter, riskFilter, sortField, sortDirection, activeTab]);

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />;
    return sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  return (
    <Tooltip.Provider>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        {/* Header */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Risk Analysis
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Identify contacts that need immediate attention
          </p>
        </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg mb-6">
        {tabs.map((tab) => {
          const IconComponent = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <IconComponent className="h-4 w-4" />
              <span>{tab.label}</span>
              <span className="bg-gray-200 dark:bg-gray-500 text-gray-700 dark:text-gray-300 px-2 py-0.5 rounded-full text-xs">
                {tab.data.length}
              </span>
            </button>
          );
        })}
      </div>

      {/* Definition Section */}
      <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
          {riskDefinitions[activeTab]?.title} - Definition
        </h4>
        <p className="text-sm text-blue-800 dark:text-blue-200 mb-3">
          {riskDefinitions[activeTab]?.definition}
        </p>
        <div className="text-sm text-blue-700 dark:text-blue-300">
          <strong>Criteria:</strong>
          <ul className="list-disc list-inside mt-1 space-y-1">
            {riskDefinitions[activeTab]?.criteria.map((criterion, index) => (
              <li key={index}>{criterion}</li>
            ))}
          </ul>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Search */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Search Contacts
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by name, email, or company..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        {/* Stage Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Pipeline Stage
          </label>
          <select
            value={stageFilter}
            onChange={(e) => setStageFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">All Stages</option>
            {uniqueStages.map((stage) => (
              <option key={stage} value={stage}>
                {stage}
              </option>
            ))}
          </select>
        </div>

        {/* Risk Level Filter (only for high-risk tab) */}
        {activeTab === 'high-risk' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Risk Level
            </label>
            <select
              value={riskFilter}
              onChange={(e) => setRiskFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">All Risk Levels</option>
              <option value="high">High Risk</option>
              <option value="medium">Medium Risk</option>
              <option value="low">Low Risk</option>
            </select>
          </div>
        )}
      </div>

      {/* Table */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        {filteredAndSortedData.length > 0 ? (
        <table>
          <>
            {/* Table Header */}
            <div className="bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-12 gap-4 px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                <div className="col-span-3">
                  <button
                    onClick={() => handleSort('name')}
                    className="flex items-center space-x-1 hover:text-gray-700 dark:hover:text-gray-100"
                  >
                    <span>Contact</span>
                    {getSortIcon('name')}
                  </button>
                </div>
                <div className={`${activeTab === 'high-risk' ? 'col-span-2' : 'col-span-3'}`}>
                  <button
                    onClick={() => handleSort('stageName')}
                    className="flex items-center space-x-1 hover:text-gray-700 dark:hover:text-gray-100"
                  >
                    <span>Stage</span>
                    {getSortIcon('stageName')}
                  </button>
                </div>
                {activeTab === 'high-risk' && (
                  <div className="col-span-1">
                    <span>Risk Level</span>
                  </div>
                )}
                <div className="col-span-2">
                  <button
                    onClick={() => handleSort('lastActivityDate')}
                    className="flex items-center space-x-1 hover:text-gray-700 dark:hover:text-gray-100"
                  >
                    <span>Last Activity</span>
                    {getSortIcon('lastActivityDate')}
                  </button>
                </div>
                {activeTab === 'stuck' && (
                  <div className="col-span-2">
                    <button
                      onClick={() => handleSort('currentStageDays')}
                      className="flex items-center space-x-1 hover:text-gray-700 dark:hover:text-gray-100"
                    >
                      <span>Days in Stage</span>
                      {getSortIcon('currentStageDays')}
                    </button>
                  </div>
                )}
                {activeTab === 'dormant' && (
                  <div className="col-span-2">
                    <button
                      onClick={() => handleSort('daysInPipeline')}
                      className="flex items-center space-x-1 hover:text-gray-700 dark:hover:text-gray-100"
                    >
                      <span>Days in Pipeline</span>
                      {getSortIcon('daysInPipeline')}
                    </button>
                  </div>
                )}
                <div className="col-span-2">
                  <span>Actions</span>
                </div>
              </div>
            </div>

            {/* Scrollable Table Body */}
            <div className="max-h-96 overflow-y-auto">
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredAndSortedData.map((item, index) => (
                  <div key={index} className="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                    {/* Contact Info */}
                    <div className="col-span-3">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            <User className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                          </div>
                        </div>
                        <div className="ml-3 min-w-0 flex-1">
                          <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {getContactName(item.contact)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {item.contact?.email && (
                              <span className="flex items-center">
                                <Mail className="h-3 w-3 mr-1" />
                                {item.contact.email}
                              </span>
                            )}
                          </div>
                          {item.contact?.company && (
                            <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                              <Building className="h-3 w-3 mr-1 inline" />
                              {item.contact.company}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Stage */}
                    <div className={`${activeTab === 'high-risk' ? 'col-span-2' : 'col-span-3'} flex items-center`}>
                      <div className="text-sm text-gray-900 dark:text-white">
                        {item.stageName || 'No Stage'}
                      </div>
                    </div>

                    {/* Risk Level (High Risk tab only) */}
                    {activeTab === 'high-risk' && (
                      <div className="col-span-1 flex items-center">
                        {getRiskBadge(item.riskLevel)}
                      </div>
                    )}

                    {/* Last Activity */}
                    <div className="col-span-2 flex items-center">
                      <div>
                        <div className="text-sm text-gray-900 dark:text-white">
                          {formatDate(item.lastActivityDate)}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {formatDaysAgo(item.lastActivityDate)}
                        </div>
                      </div>
                    </div>

                    {/* Days in Stage (Stuck tab) */}
                    {activeTab === 'stuck' && (
                      <div className="col-span-2 flex items-center">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {item.currentStageDays || 0} days
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            In current stage
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Days in Pipeline (Dormant tab) */}
                    {activeTab === 'dormant' && (
                      <div className="col-span-2 flex items-center">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {item.daysInPipeline || 0} days
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Total pipeline time
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Actions with Tooltips */}
                    <div className="col-span-2 flex items-center">
                      <div className="flex space-x-2">
                        <Tooltip.Root>
                          <Tooltip.Trigger asChild>
                            <button className="text-primary hover:text-primary/80 transition-colors p-1 rounded">
                              <Eye className="h-4 w-4" />
                            </button>
                          </Tooltip.Trigger>
                          <Tooltip.Portal>
                            <Tooltip.Content
                              className="bg-gray-900 text-white px-2 py-1 rounded text-xs"
                              sideOffset={5}
                            >
                              View Contact Details
                              <Tooltip.Arrow className="fill-gray-900" />
                            </Tooltip.Content>
                          </Tooltip.Portal>
                        </Tooltip.Root>

                        <Tooltip.Root>
                          <Tooltip.Trigger asChild>
                            <button className="text-green-600 hover:text-green-500 transition-colors p-1 rounded">
                              <UserPlus className="h-4 w-4" />
                            </button>
                          </Tooltip.Trigger>
                          <Tooltip.Portal>
                            <Tooltip.Content
                              className="bg-gray-900 text-white px-2 py-1 rounded text-xs"
                              sideOffset={5}
                            >
                              Add Activity
                              <Tooltip.Arrow className="fill-gray-900" />
                            </Tooltip.Content>
                          </Tooltip.Portal>
                        </Tooltip.Root>

                        <Tooltip.Root>
                          <Tooltip.Trigger asChild>
                            <button className="text-blue-600 hover:text-blue-500 transition-colors p-1 rounded">
                              <Mail className="h-4 w-4" />
                            </button>
                          </Tooltip.Trigger>
                          <Tooltip.Portal>
                            <Tooltip.Content
                              className="bg-gray-900 text-white px-2 py-1 rounded text-xs"
                              sideOffset={5}
                            >
                              Send Email
                              <Tooltip.Arrow className="fill-gray-900" />
                            </Tooltip.Content>
                          </Tooltip.Portal>
                        </Tooltip.Root>

                        <Tooltip.Root>
                          <Tooltip.Trigger asChild>
                            <button className="text-purple-600 hover:text-purple-500 transition-colors p-1 rounded">
                              <MessageSquare className="h-4 w-4" />
                            </button>
                          </Tooltip.Trigger>
                          <Tooltip.Portal>
                            <Tooltip.Content
                              className="bg-gray-900 text-white px-2 py-1 rounded text-xs"
                              sideOffset={5}
                            >
                              Add Note
                              <Tooltip.Arrow className="fill-gray-900" />
                            </Tooltip.Content>
                          </Tooltip.Portal>
                        </Tooltip.Root>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
          </table>
        ) : (
          <div className="text-center py-12">
            <div className="flex flex-col items-center">
              <div className="h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-4">
                {activeTabData && <activeTabData.icon className="h-6 w-6 text-gray-400" />}
              </div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                No {activeTabData?.label.toLowerCase()} contacts found
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {searchTerm || stageFilter || riskFilter
                  ? 'Try adjusting your filters to see more results.'
                  : 'Great! No contacts in this risk category.'
                }
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Summary Stats */}
      {filteredAndSortedData.length > 0 && (
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
            Summary Statistics {searchTerm || stageFilter || riskFilter ? '(Filtered)' : ''}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {filteredAndSortedData.length}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {searchTerm || stageFilter || riskFilter ? 'Filtered' : 'Total'} {activeTabData?.label.toLowerCase()} contacts
              </div>
            </div>

            {activeTab === 'dormant' && (
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {filteredAndSortedData.length > 0 ? Math.round(
                    filteredAndSortedData.reduce((sum, item) =>
                      sum + (item.daysInPipeline || 0), 0
                    ) / filteredAndSortedData.length
                  ) : 0}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Avg days in pipeline
                </div>
              </div>
            )}

            {activeTab === 'stuck' && (
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {filteredAndSortedData.length > 0 ? Math.round(
                    filteredAndSortedData.reduce((sum, item) =>
                      sum + (item.currentStageDays || 0), 0
                    ) / filteredAndSortedData.length
                  ) : 0}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Avg days in current stage
                </div>
              </div>
            )}

            {activeTab === 'high-risk' && (
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {filteredAndSortedData.length > 0 ? Math.round(
                    filteredAndSortedData.reduce((sum, item) =>
                      sum + (item.engagementScore || 0), 0
                    ) / filteredAndSortedData.length
                  ) : 0}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Avg engagement score
                </div>
              </div>
            )}

            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {filteredAndSortedData.filter(item =>
                  item.lastActivityDate &&
                  (new Date() - new Date(item.lastActivityDate)) > (7 * 24 * 60 * 60 * 1000)
                ).length}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                No activity in 7+ days
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </Tooltip.Provider>
  );
}
