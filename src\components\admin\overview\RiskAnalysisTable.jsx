"use client";

import { useState } from "react";
import { 
  AlertTriangle, 
  Clock, 
  TrendingDown, 
  User,
  Mail,
  Phone,
  Building,
  Calendar,
  Activity
} from "lucide-react";

export default function RiskAnalysisTable({ data }) {
  const [activeTab, setActiveTab] = useState('high-risk');

  if (!data) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Risk Analysis
        </h3>
        <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
          No risk data available
        </div>
      </div>
    );
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatDaysAgo = (dateString) => {
    if (!dateString) return 'Never';
    const days = Math.floor((new Date() - new Date(dateString)) / (1000 * 60 * 60 * 24));
    if (days === 0) return 'Today';
    if (days === 1) return '1 day ago';
    return `${days} days ago`;
  };

  const getContactName = (contact) => {
    if (!contact) return 'Unknown Contact';
    return `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || contact.email || 'Unknown Contact';
  };

  const getRiskBadge = (riskLevel) => {
    const colors = {
      high: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
      medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
      low: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[riskLevel] || colors.low}`}>
        {riskLevel?.charAt(0).toUpperCase() + riskLevel?.slice(1) || 'Low'}
      </span>
    );
  };

  const tabs = [
    {
      id: 'high-risk',
      label: 'High Risk',
      icon: AlertTriangle,
      data: data.highRiskContacts || [],
      description: 'Contacts at high risk of churning'
    },
    {
      id: 'dormant',
      label: 'Dormant',
      icon: TrendingDown,
      data: data.dormantContacts || [],
      description: 'Contacts with no recent activity'
    },
    {
      id: 'stuck',
      label: 'Stuck in Pipeline',
      icon: Clock,
      data: data.stuckContacts || [],
      description: 'Contacts stuck in stages too long'
    }
  ];

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Risk Analysis
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Identify contacts that need immediate attention
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg mb-6">
        {tabs.map((tab) => {
          const IconComponent = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <IconComponent className="h-4 w-4" />
              <span>{tab.label}</span>
              <span className="bg-gray-200 dark:bg-gray-500 text-gray-700 dark:text-gray-300 px-2 py-0.5 rounded-full text-xs">
                {tab.data.length}
              </span>
            </button>
          );
        })}
      </div>

      {/* Tab Description */}
      <div className="mb-4">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {activeTabData?.description}
        </p>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        {activeTabData?.data.length > 0 ? (
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Risk Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Last Activity
                </th>
                {activeTab === 'stuck' && (
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Days in Stage
                  </th>
                )}
                {activeTab === 'dormant' && (
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Days in Pipeline
                  </th>
                )}
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {activeTabData.data.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                          <User className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {getContactName(item.contact)}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center space-x-4">
                          {item.contact?.email && (
                            <span className="flex items-center">
                              <Mail className="h-3 w-3 mr-1" />
                              {item.contact.email}
                            </span>
                          )}
                          {item.contact?.company && (
                            <span className="flex items-center">
                              <Building className="h-3 w-3 mr-1" />
                              {item.contact.company}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getRiskBadge(item.riskLevel)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {formatDate(item.lastActivityDate)}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {formatDaysAgo(item.lastActivityDate)}
                    </div>
                  </td>
                  {activeTab === 'stuck' && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {item.currentStageDays || 0} days
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        In current stage
                      </div>
                    </td>
                  )}
                  {activeTab === 'dormant' && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {item.daysInPipeline || 0} days
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Total pipeline time
                      </div>
                    </td>
                  )}
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button className="text-primary hover:text-primary/80 transition-colors">
                        <User className="h-4 w-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-500 transition-colors">
                        <Activity className="h-4 w-4" />
                      </button>
                      <button className="text-blue-600 hover:text-blue-500 transition-colors">
                        <Mail className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="text-center py-12">
            <div className="flex flex-col items-center">
              <div className="h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-4">
                {activeTabData && <activeTabData.icon className="h-6 w-6 text-gray-400" />}
              </div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                No {activeTabData?.label.toLowerCase()} contacts
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Great! No contacts in this risk category.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Summary Stats */}
      {activeTabData?.data.length > 0 && (
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {activeTabData.data.length}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Total {activeTabData.label.toLowerCase()} contacts
              </div>
            </div>
            
            {activeTab === 'dormant' && (
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {Math.round(
                    activeTabData.data.reduce((sum, item) => 
                      sum + (item.daysInPipeline || 0), 0
                    ) / activeTabData.data.length
                  )}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Avg days in pipeline
                </div>
              </div>
            )}
            
            {activeTab === 'stuck' && (
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {Math.round(
                    activeTabData.data.reduce((sum, item) => 
                      sum + (item.currentStageDays || 0), 0
                    ) / activeTabData.data.length
                  )}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Avg days in current stage
                </div>
              </div>
            )}
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {activeTabData.data.filter(item => 
                  item.lastActivityDate && 
                  (new Date() - new Date(item.lastActivityDate)) > (7 * 24 * 60 * 60 * 1000)
                ).length}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                No activity in 7+ days
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
