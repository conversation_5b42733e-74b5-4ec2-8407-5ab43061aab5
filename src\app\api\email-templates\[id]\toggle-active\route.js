import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";

export async function PATCH(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { id: templateId } = await params;
    const data = await request.json();

    // Update the template's active status
    const template = await prisma.emailTemplate.update({
      where: { id: templateId },
      data: {
        isActiveGlobal: !data.isActiveGlobal, // Toggle active status
      },
    });

    // Fetch all global templates to return
    const globalTemplates = await prisma.emailTemplate.findMany({
      where: {
        isGlobal: true,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(globalTemplates);
  } catch (error) {
    console.error("Failed to create template:", error);
    return NextResponse.json(
      { error: "Failed to create template: " + error.message },
      { status: 500 }
    );
  }
}
