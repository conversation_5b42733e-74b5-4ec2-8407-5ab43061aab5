'use client';

import { useState, useEffect } from 'react';
import { Edit, Trash2 } from 'lucide-react';
import NoteForm from './NoteForm';
import NoteFilter from './NoteFilter';
import SearchInput from '@/components/common/SearchInput';

export default function NotesTab({ contactId, onNoteChange }) {
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [currentNote, setCurrentNote] = useState(null);
  const [expandedNoteId, setExpandedNoteId] = useState(null);
  const [filters, setFilters] = useState({
    sortBy: 'createdAt',
    sortOrder: 'desc',
    searchTerm: ''
  });

  const fetchNotes = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch(`/api/contacts/${contactId}/notes`);
      if (!response.ok) {
        throw new Error('Failed to fetch notes');
      }
      const data = await response.json();
      setNotes(data);
    } catch (error) {
      console.error('Failed to fetch notes:', error);
      setError('Failed to load notes. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotes();
  }, [contactId]);

  const handleAddNote = () => {
    setCurrentNote(null);
    setIsFormOpen(true);
  };

  const handleEditNote = (note) => {
    setCurrentNote(note);
    setIsFormOpen(true);
  };

  const handleDeleteNote = async (noteId) => {
    if (!window.confirm('Are you sure you want to delete this note?')) {
      return;
    }

    try {
      const response = await fetch(`/api/notes/${noteId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete note');
      }

      // Remove the deleted note from the state
      setNotes(notes.filter(note => note.id !== noteId));

      // Notify parent component that a note has changed
      if (onNoteChange) onNoteChange();
    } catch (error) {
      console.error('Failed to delete note:', error);
      setError('Failed to delete note. Please try again.');
    }
  };

  const handleNoteSaved = (savedNote) => {
    if (currentNote) {
      // Update existing note
      setNotes(notes.map(note =>
        note.id === savedNote.id ? savedNote : note
      ));
    } else {
      // Add new note
      setNotes([savedNote, ...notes]);
    }

    // Clear any errors
    setError('');

    // Log success for debugging
    console.log('Note saved successfully:', savedNote);

    // Notify parent component that a note has changed
    if (onNoteChange) onNoteChange();
  };

  const toggleExpandNote = (noteId) => {
    setExpandedNoteId(expandedNoteId === noteId ? null : noteId);
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleSearch = (searchTerm) => {
    setFilters(prev => ({
      ...prev,
      searchTerm
    }));
  };

  // Filter notes by search term
  const filteredNotes = notes.filter(note => {
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const titleMatch = note.title.toLowerCase().includes(searchLower);
      const contentMatch = note.content.toLowerCase().includes(searchLower);
      return titleMatch || contentMatch;
    }
    return true;
  });

  // Sort notes
  const sortedNotes = [...filteredNotes].sort((a, b) => {
    const sortOrder = filters.sortOrder === 'asc' ? 1 : -1;

    if (filters.sortBy === 'createdAt' || filters.sortBy === 'updatedAt') {
      return (new Date(a[filters.sortBy]) - new Date(b[filters.sortBy])) * sortOrder;
    }

    if (filters.sortBy === 'title') {
      return a.title.localeCompare(b.title) * sortOrder;
    }

    return 0;
  });

  if (loading) return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow h-full overflow-auto">
      <div className="p-4 flex items-center justify-center h-full">
        <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow h-full overflow-auto">
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Notes</h3>
          <button
            onClick={handleAddNote}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
          >
            Add Note
          </button>
        </div>

        {error && (
          <div className="p-3 mb-4 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
            {error}
          </div>
        )}

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <SearchInput
            placeholder="Search notes..."
            onSearch={handleSearch}
            initialValue={filters.searchTerm}
          />

          <div className="flex items-center gap-2">
            <NoteFilter onFilterChange={handleFilterChange} />

            <div className="text-sm font-medium">
              <span className="text-primary">{sortedNotes.length}</span> <span className="text-gray-600 dark:text-gray-300">notes found</span>
              {filteredNotes.length !== notes.length && (
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">(filtered from {notes.length})</span>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          {sortedNotes.map((note) => (
            <div
              key={note.id}
              className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600 relative"
            >
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 mb-2">
                <div>
                  <h4 className="text-base font-medium text-gray-900 dark:text-white">
                    {note.title}
                  </h4>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Created {new Date(note.createdAt).toLocaleString()}
                    {note.updatedAt && note.updatedAt !== note.createdAt && (
                      <span className="ml-2">
                        · Updated {new Date(note.updatedAt).toLocaleString()}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex flex-wrap gap-2 ml-auto">
                  <button
                    onClick={() => handleEditNote(note)}
                    className="p-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                    title="Edit note"
                    aria-label="Edit note"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteNote(note.id)}
                    className="p-1 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                    title="Delete note"
                    aria-label="Delete note"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Note content */}
              <div className="mt-3 text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {expandedNoteId === note.id
                  ? note.content
                  : note.content.length > 150
                    ? `${note.content.substring(0, 150)}...`
                    : note.content
                }
              </div>
              {note.content.length > 150 && (
                <button
                  onClick={() => toggleExpandNote(note.id)}
                  className="mt-2 px-2 py-1 text-sm text-primary hover:text-primary-hover border border-primary/20 hover:border-primary/40 rounded-md transition-colors inline-flex items-center"
                >
                  {expandedNoteId === note.id ? (
                    <>
                      <span className="mr-1">▲</span> Show less
                    </>
                  ) : (
                    <>
                      <span className="mr-1">▼</span> Read more
                    </>
                  )}
                </button>
              )}
            </div>
          ))}
          {sortedNotes.length === 0 && (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
              No notes yet
            </div>
          )}
        </div>
      </div>

      <NoteForm
        contactId={contactId}
        note={currentNote}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSuccess={handleNoteSaved}
      />
    </div>
  );
}