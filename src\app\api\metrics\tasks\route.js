
import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";

export async function GET(request) {
  try {
    const { user } = await getSignedInUser(request);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const endOfToday = new Date(today);
    endOfToday.setHours(23, 59, 59, 999);
    const endOfWeek = new Date(today);
    endOfWeek.setDate(today.getDate() + (7 - today.getDay()));
    endOfWeek.setHours(23, 59, 59, 999);

    // Fetch all tasklists for the user (or all if admin)
    const tasklists = await prisma.tasklist.findMany({
      where: user.role === "admin" ? {} : { contact: { userId: user.id } },
      include: {
        items: true,
        contact: true,
      },
    });

    // Flatten all items
    const allItems = tasklists.flatMap(tl =>
      (tl.items || []).map(item => ({
        ...item,
        contact: tl.contact,
      }))
    );

    // Upcoming: incomplete, due today or later
    const upcomingTasks = allItems
      .filter(item => !item.completed && item.dueDate && new Date(item.dueDate) >= today)
      .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate))
      .slice(0, 5);

    // Due today: incomplete, dueDate is today
    const tasksDueToday = allItems.filter(item => {
      if (!item.dueDate || item.completed) return false;
      const due = new Date(item.dueDate);
      return due >= today && due <= endOfToday;
    }).length;

    // Due this week: incomplete, dueDate is this week
    const tasksDueThisWeek = allItems.filter(item => {
      if (!item.dueDate || item.completed) return false;
      const due = new Date(item.dueDate);
      return due >= today && due <= endOfWeek;
    }).length;

    return NextResponse.json({
      upcomingTasks,
      tasksDueToday,
      tasksDueThisWeek,
    });
  } catch (error) {
    console.error("Failed to fetch task metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch task metrics" },
      { status: 500 }
    );
  }
}
