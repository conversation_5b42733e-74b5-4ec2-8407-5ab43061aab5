import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

export async function PATCH(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    
    // Check if user is admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const { name, type } = await request.json();

    // Validate input
    if (!name || !type) {
      return NextResponse.json(
        { error: 'Name and type are required' },
        { status: 400 }
      );
    }

    // Validate type
    const validTypes = ['Contact', 'Document', 'Email'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: 'Invalid tag type. Must be Contact, Document, or Email' },
        { status: 400 }
      );
    }

    // Check if tag exists
    const existingTag = await prisma.tag.findUnique({
      where: { id }
    });

    if (!existingTag) {
      return NextResponse.json(
        { error: 'Tag not found' },
        { status: 404 }
      );
    }

    // Check if another tag with the same name and type exists (excluding current tag)
    const duplicateTag = await prisma.tag.findFirst({
      where: {
        name: name.trim(),
        type: type,
        id: { not: id }
      }
    });

    if (duplicateTag) {
      return NextResponse.json(
        { error: `A ${type} tag with the name "${name}" already exists` },
        { status: 409 }
      );
    }

    const updatedTag = await prisma.tag.update({
      where: { id },
      data: {
        name: name.trim(),
        type: type
      }
    });

    // Log the tag update
    await auditLogger.logTagUpdate({
      userId: user.id,
      tagId: id,
      oldValues: existingTag,
      newValues: updatedTag,
      request
    });

    return NextResponse.json(updatedTag);
  } catch (error) {
    console.error('Error updating tag:', error);
    return NextResponse.json(
      { error: 'Failed to update tag' },
      { status: 500 }
    );
  }
}

export async function DELETE(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    
    // Check if user is admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    const { id } = await params;

    // Check if tag exists
    const existingTag = await prisma.tag.findUnique({
      where: { id }
    });

    if (!existingTag) {
      return NextResponse.json(
        { error: 'Tag not found' },
        { status: 404 }
      );
    }

    // Check if tag is being used
    let isInUse = false;
    let usageDetails = [];

    // Check contacts
    if (existingTag.type === 'Contact') {
      const contactsUsingTag = await prisma.contact.count({
        where: {
          tags: {
            has: existingTag.name
          }
        }
      });
      if (contactsUsingTag > 0) {
        isInUse = true;
        usageDetails.push(`${contactsUsingTag} contact(s)`);
      }
    }

    // Check documents
    if (existingTag.type === 'Document') {
      const documentsUsingTag = await prisma.document.count({
        where: {
          tags: {
            has: existingTag.name
          }
        }
      });
      if (documentsUsingTag > 0) {
        isInUse = true;
        usageDetails.push(`${documentsUsingTag} document(s)`);
      }
    }

    // Check email campaigns
    if (existingTag.type === 'Email') {
      const campaignsUsingTag = await prisma.emailCampaign.count({
        where: {
          tags: {
            has: existingTag.name
          }
        }
      });
      if (campaignsUsingTag > 0) {
        isInUse = true;
        usageDetails.push(`${campaignsUsingTag} email campaign(s)`);
      }
    }

    if (isInUse) {
      return NextResponse.json(
        { 
          error: `Cannot delete tag "${existingTag.name}". It is currently being used by: ${usageDetails.join(', ')}. Please remove the tag from all items before deleting.` 
        },
        { status: 409 }
      );
    }

    // Log the tag deletion
    await auditLogger.logTagDelete({
      userId: user.id,
      tagId: id,
      oldValues: existingTag,
      request
    });

    await prisma.tag.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Tag deleted successfully' });
  } catch (error) {
    console.error('Error deleting tag:', error);
    return NextResponse.json(
      { error: 'Failed to delete tag' },
      { status: 500 }
    );
  }
}
