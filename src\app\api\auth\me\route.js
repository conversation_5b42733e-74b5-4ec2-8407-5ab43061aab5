import { NextResponse } from "next/server";
import { getSignedInUser } from "@/lib/auth";

/**
 * GET /api/auth/me - Get the current user's information
 * This endpoint returns the current user's information, including their role and permissions
 */
export async function GET(request) {
  try {
    const { user } = await getSignedInUser(request);

    if (!user) {
      console.log("there was no user!");
      return NextResponse.json(
        { error: "Unauthorized", redirectTo: "/api/auth/login" },
        { status: 401 }
      );
    }

    // Return user information, excluding sensitive data
    return NextResponse.json({
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      permissions: user.permissions,
    });
  } catch (error) {
    console.error("Failed to get current user:", error);
    return NextResponse.json(
      { error: "Failed to get current user" },
      { status: 500 }
    );
  }
}
