import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";

export async function PATCH(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { id: campaignId } = await params;
    const { status } = await request.json();

    // Update campaign status
    const updatedCampaign = await prisma.emailCampaign.update({
      where: { id: campaignId, userId: user.id },
      data: { status },
    });

    return NextResponse.json(updatedCampaign);
  } catch (error) {
    console.error("Error updating campaign status:", error);
    return NextResponse.json(
      { message: "Failed to update campaign status" },
      { status: 500 }
    );
  }
}
