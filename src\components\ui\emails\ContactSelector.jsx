"use client";

import { useState, useEffect, useMemo } from "react";
import { CheckIcon, FunnelIcon } from "@heroicons/react/24/solid";

export default function ContactSelector({
  selectedIds = [],
  onChange,
  selectedGroups = [],
}) {
  const [contacts, setContacts] = useState([]);
  // const [groups, setGroups] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    dormant: false,
    noEmails: false,
    dormancyThreshold: 14, // days
  });
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchContacts();
    // fetchGroups();
  }, [filters.dormant, filters.noEmails, filters.dormancyThreshold]);

  // const fetchGroups = async () => {
  //   try {
  //     let url = "/api/groups";

  //     const response = await fetch(url);

  //     if (response.ok) {
  //       const data = await response.json();
  //       setGroups(data);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching groups:", error);
  //   }
  // };

  const fetchContacts = async () => {
    try {
      let url = "/api/contacts?";

      if (filters.dormant) {
        url += `&dormant=true&threshold=${filters.dormancyThreshold}`;
      }
      if (filters.noEmails) {
        url += "&noEmails=true";
      }

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setContacts(data);
      }
    } catch (error) {
      console.error("Error fetching contacts:", error);
    } finally {
      setLoading(false);
    }
  };

  const filteredContacts = useMemo(() => {
    return contacts.filter(
      (contact) =>
        contact.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.firstName
          ?.toLowerCase()
          .concat(" ", contact.lastName.toLowerCase())
          .includes(searchTerm.toLowerCase()) ||
        contact.lastName
          ?.toLowerCase()
          .concat(" ", contact.firstName.toLowerCase())
          .includes(searchTerm.toLowerCase()) ||
        contact.lastName
          ?.toLowerCase()
          .concat(", ", contact.firstName.toLowerCase())
          .includes(searchTerm.toLowerCase()) ||
        contact.email?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [contacts, searchTerm]);

  const toggleContact = (contactId) => {
    // If selected ids [] by default includes the selected contact id, then filter out the clicked id.
    // Else, create a new array, spread selectedIds, and add the clicked id.
    // Then, call onChange with the new array of selected Ids which will put it in the formData state.

    // Check to see if the selected contact is in the group
    for (const group of selectedGroups) {
      let foundContact = group.contacts?.findIndex((contact) => {
        return contact.contactId === contactId;
      });
      if (foundContact !== -1) {
        console.log(
          "Contact already exists in a selected group, preventing dupe contact."
        );
        return;
      } else {
        console.log(
          `Contact with ID ${contactId} not found in any selected group.`
        );
      }
    }
    const newSelectedIds = selectedIds.includes(contactId)
      ? selectedIds.filter((id) => id !== contactId)
      : [...selectedIds, contactId];
    onChange(newSelectedIds);
  };

  if (loading) {
    return <div className="text-center py-4">Loading contacts...</div>;
  }

  return (
    <div className="border rounded-md dark:border-gray-700">
      <div className="p-3 border-b dark:border-gray-700">
        <div className="flex items-center gap-2 mb-3">
          <input
            type="text"
            placeholder="Search contacts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
          />
          {/* <button
            onClick={(e) => {
              e.preventDefault();
              setShowFilters(!showFilters);
            }}
            className={`p-2 rounded-md border ${
              Object.values(filters).some(Boolean)
                ? "border-primary text-primary"
                : "border-gray-300 text-gray-600"
            } hover:bg-gray-50 dark:hover:bg-gray-800`}
            title="Toggle filters"
          >
            <FunnelIcon className="h-5 w-5" />
          </button> */}
        </div>

        {showFilters && (
          <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md space-y-3">
            <div className="flex items-center justify-between">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={filters.dormant}
                  onChange={(e) =>
                    setFilters({ ...filters, dormant: e.target.checked })
                  }
                  className="rounded border-gray-300"
                />
                <span>Dormant contacts</span>
              </label>
              {filters.dormant && (
                <select
                  value={filters.dormancyThreshold}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      dormancyThreshold: Number(e.target.value),
                    })
                  }
                  className="rounded-md border-gray-300 dark:border-gray-600 shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value={7}>7 days</option>
                  <option value={14}>14 days</option>
                  <option value={30}>30 days</option>
                  <option value={60}>60 days</option>
                  <option value={90}>90 days</option>
                </select>
              )}
            </div>
            <div className="flex items-center">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={filters.noEmails}
                  onChange={(e) =>
                    setFilters({ ...filters, noEmails: e.target.checked })
                  }
                  className="rounded border-gray-300"
                />
                <span>No previous emails</span>
              </label>
            </div>
          </div>
        )}
      </div>

      <div className="overflow-y-auto max-h-[400px]">
        {filteredContacts.map((contact) => {
          // Check to see if the contact is already in a selected group
          let foundContact = selectedGroups.find((group) => {
            return group.contacts?.find(
              (grpContact) => grpContact.contactId === contact.id
            );
          });

          if (foundContact !== undefined) {
            return (
              <div
                key={contact.id}
                className="flex items-center justify-between p-3 cursor-not-allowed opacity-60 bg-gray-50 dark:bg-gray-800/50"
              >
                <div>
                  <div className="font-medium text-gray-500">
                    {contact.firstName} {contact.lastName}{" "}
                    {contact?.company && `(${contact.company})`}
                  </div>
                  <div className="text-sm text-gray-500">
                    {contact.email ?? "No email address"}
                  </div>
                  <div className="text-xs text-amber-600 dark:text-amber-400 mt-1">
                    Already included in selected group: {foundContact.name}
                  </div>
                </div>
                <div className="text-gray-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            );
          }

          return (
            <div
              key={contact.id}
              onClick={() => toggleContact(contact.id)}
              className={`flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 ${
                selectedIds.includes(contact.id)
                  ? "bg-blue-50 dark:bg-blue-900/20"
                  : ""
              }`}
            >
              <div>
                <div className="font-medium">
                  {contact.firstName} {contact.lastName}{" "}
                  {contact?.company && `(${contact.company})`}
                </div>
                <div className="text-sm text-gray-500">
                  {contact.email ?? "No email address"}
                </div>
              </div>
              {selectedIds.includes(contact.id) && (
                <CheckIcon className="h-5 w-5 text-blue-500" />
              )}
            </div>
          );
        })}

        {filteredContacts.length === 0 && (
          <div className="text-center py-4 text-gray-500">
            No contacts found
          </div>
        )}
      </div>

      <div className="p-3 border-t dark:border-gray-700 text-sm text-gray-500">
        {selectedIds.length} contacts selected
      </div>
    </div>
  );
}
