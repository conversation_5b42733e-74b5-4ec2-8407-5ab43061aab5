'use client';

import { useState, useRef, useEffect } from 'react';
import { Info } from 'lucide-react';
import { dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import ContactCard from './ContactCard';
import Tooltip from '@/components/ui/Tooltip';
import './pipeline-styles.css';

export default function UnassignedColumn({
  contacts,
  onLoadMore,
  isMultiSelectMode = false,
  selectedContacts = [],
  onContactSelect
}) {
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const scrollRef = useRef(null);

  const unassignedStage = {
    id: 'unassigned',
    name: 'Unassigned',
    order: 0
  };

  // Set up drop target
  useEffect(() => {
    if (!scrollRef.current || isMultiSelectMode) return;

    const cleanup = dropTargetForElements({
      element: scrollRef.current,
      getData: () => ({ stageId: 'unassigned' }),
      onDragEnter: () => {
        if (scrollRef.current) {
          scrollRef.current.classList.add('bg-blue-50/50', 'dark:bg-blue-900/20');
        }
      },
      onDragLeave: () => {
        if (scrollRef.current) {
          scrollRef.current.classList.remove('bg-blue-50/50', 'dark:bg-blue-900/20');
        }
      },
      onDrop: () => {
        if (scrollRef.current) {
          scrollRef.current.classList.remove('bg-blue-50/50', 'dark:bg-blue-900/20');
        }
      }
    });

    return cleanup;
  }, [isMultiSelectMode]);

  const handleScroll = (e) => {
    if (isLoadingMore || !hasMore) return;

    const { scrollTop, scrollHeight, clientHeight } = e.target;
    // Load more when user scrolls to 80% of the way down
    if (scrollHeight - scrollTop <= clientHeight * 1.2) {
      loadMoreContacts();
    }
  };

  const loadMoreContacts = async () => {
    if (isLoadingMore || !hasMore || !onLoadMore) return;

    setIsLoadingMore(true);
    const nextPage = currentPage + 1;

    try {
      const result = await onLoadMore(nextPage);

      if (!result.contacts || result.contacts.length === 0) {
        setHasMore(false);
      } else {
        setCurrentPage(nextPage);
      }
    } catch (error) {
      console.error('Failed to load more contacts:', error);
      setHasMore(false);
    } finally {
      setIsLoadingMore(false);
    }
  };

  return (
    <div className="pipeline-column flex-none w-64 md:w-72 h-full flex flex-col bg-gray-100 dark:bg-gray-800/70 rounded-lg shadow-md border-2 border-dashed border-gray-400 dark:border-gray-600 transition-shadow duration-200">
      {/* Column Header */}
      <div className="p-4 border-b border-gray-300 dark:border-gray-700 bg-gray-300 dark:bg-gray-600 rounded-t-lg">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold text-gray-800 dark:text-white">
              {unassignedStage.name}
            </h3>
            <span className="px-2 py-0.5 text-xs font-medium bg-gray-300 dark:bg-gray-500 text-gray-700 dark:text-gray-200 rounded-full">
              {contacts.length}
            </span>
            <Tooltip content="Contacts in this column have not been assigned to any pipeline stage. Drag them to a stage to update their status.">
              <div className="text-gray-500 dark:text-gray-400 cursor-help">
                <Info className="h-4 w-4" />
              </div>
            </Tooltip>
          </div>
        </div>
      </div>

      {/* Column Content - Scrollable */}
      <div
        ref={scrollRef}
        data-stage-id="unassigned"
        className="pipeline-scroll flex-1 overflow-y-auto p-3 space-y-3 transition-colors bg-white dark:bg-gray-800/80"
        style={{ minHeight: '100px' }}
        onScroll={handleScroll}
      >
            {contacts.length === 0 && (
              <div className="h-full flex items-center justify-center text-center p-4">
                <div className="flex flex-col items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-gray-300 dark:text-gray-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <p className="text-sm text-gray-500 dark:text-gray-400">No unassigned contacts</p>
                  <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">All contacts are assigned to pipeline stages</p>
                </div>
              </div>
            )}

            {contacts && contacts.length > 0 ? (
              contacts.map((contact, index) => (
                <div key={contact.id} className="contact-card-wrapper">
                  <ContactCard
                    contact={contact}
                    isDragging={false}
                    isMultiSelectMode={isMultiSelectMode}
                    isSelected={selectedContacts.includes(contact.id)}
                    onSelect={onContactSelect}
                  />
                </div>
              ))
            ) : (
              <div className="text-center p-4 text-gray-500 dark:text-gray-400">
                <p>No unassigned contacts</p>
              </div>
            )}
            {isLoadingMore && (
              <div className="py-2 text-center text-gray-500 dark:text-gray-400">
                <div className="inline-block animate-spin h-4 w-4 border-2 border-gray-300 dark:border-gray-600 border-t-primary rounded-full mr-2"></div>
                Loading more...
              </div>
            )}
      </div>
    </div>
  );
}
