"use client";

import { useState, useEffect } from "react";
import * as Tabs from "@radix-ui/react-tabs";
import ProfilePanel from "@/components/contacts/profile/ProfilePanel";
import CalendarTab from "@/components/contacts/profile/CalendarTab";
import ActivityTabWithRefresh from "@/components/contacts/profile/ActivityTabWithRefresh";
import TasksTabWithRefresh from "@/components/contacts/profile/TasksTabWithRefresh";
import NotesTabWithRefresh from "@/components/contacts/profile/NotesTabWithRefresh";
import ChecklistsTabWithRefresh from "@/components/contacts/profile/ChecklistsTabWithRefresh";
import EmailTab from "@/components/contacts/profile/EmailTab";
import DocumentsTab from "@/components/contacts/profile/DocumentsTab";
import EMoneyTab from "@/components/contacts/profile/EMoneyTab";
import ContactStats from "@/components/contacts/profile/ContactStats";
import InsuranceTabWithRefresh from "@/components/contacts/profile/InsuranceTabWithRefresh";
// "use" is a React API that lets you read the value of a resource like a Promise or context.
import { use } from "react";

export default function ContactProfile({ params }) {
  const [contact, setContact] = useState(null);
  const [loading, setLoading] = useState(true);
  // 'params' is now a Promise and should be unwrapped with React.use() before accessing properties of the underlying params object.
  // We don't use "await" because "await" can only be used in Server Components, while this is a Client Component. React.use() works in both client and server comnponents.
  const { id: contactID } = use(params);

  useEffect(() => {
    const fetchContact = async () => {
      try {
        const response = await fetch(`/api/contacts/${contactID}`);
        if (response.ok) {
          const data = await response.json();
          setContact(data);
        }
      } catch (error) {
        console.error("Failed to fetch contact:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchContact();
  }, [contactID]);

  const handleContactUpdated = (updatedContact) => {
    setContact(updatedContact);
  };

  if (loading) return <div>Loading...</div>;
  if (!contact) return <div>Contact not found</div>;

  return (
    <div className="h-full w-full px-0 sm:px-1 lg:px-2 py-3 pb-6">
      <div className="flex flex-col lg:flex-row gap-3 h-full lg:overflow-hidden">
        {/* Left Panel - Profile Information */}
        <div className="lg:w-1/5 xl:w-64 lg:h-full mb-3 lg:mb-0 flex-shrink-0">
          <ProfilePanel
            contact={contact}
            onContactUpdated={handleContactUpdated}
          />
        </div>

        {/* Middle Panel - Tabs */}
        <div className="lg:flex-1 lg:h-full flex flex-col mb-3 lg:mb-0 lg:px-1">
          <Tabs.Root
            defaultValue="calendar"
            className="flex flex-col h-full min-h-[500px] lg:min-h-0 overflow-hidden"
          >
            <Tabs.List className="flex border-b border-gray-200 dark:border-gray-700 overflow-x-auto">
              <Tabs.Trigger
                value="calendar"
                className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary whitespace-nowrap"
              >
                Calendar
              </Tabs.Trigger>
              <Tabs.Trigger
                value="activities"
                className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary whitespace-nowrap"
              >
                Activity
              </Tabs.Trigger>
              <Tabs.Trigger
                value="tasks"
                className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary whitespace-nowrap"
              >
                Tasks
              </Tabs.Trigger>
              <Tabs.Trigger
                value="notes"
                className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary whitespace-nowrap"
              >
                Notes
              </Tabs.Trigger>
              <Tabs.Trigger
                value="email"
                className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary whitespace-nowrap"
              >
                Email
              </Tabs.Trigger>
              <Tabs.Trigger
                value="documents"
                className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary whitespace-nowrap"
              >
                Documents
              </Tabs.Trigger>
              <Tabs.Trigger
                value="emoney"
                className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary whitespace-nowrap"
              >
                eMoney
              </Tabs.Trigger>
              <Tabs.Trigger
                value="insurance"
                className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-primary whitespace-nowrap"
              >
                Insurance
              </Tabs.Trigger>
            </Tabs.List>

            <div className="flex-1 mt-3 min-h-[450px]">
              <Tabs.Content value="calendar" className="outline-none h-full">
                <CalendarTab contactId={contact.id} />
              </Tabs.Content>
              <Tabs.Content value="activities" className="outline-none h-full">
                <ActivityTabWithRefresh
                  key={`activity-${contact.id}`}
                  contactId={contact.id}
                />
              </Tabs.Content>
              <Tabs.Content value="tasks" className="outline-none h-full">
                <TasksTabWithRefresh contactId={contact.id} />
              </Tabs.Content>
              <Tabs.Content value="notes" className="outline-none h-full">
                <NotesTabWithRefresh contactId={contact.id} />
              </Tabs.Content>
              <Tabs.Content value="email" className="outline-none h-full">
                <EmailTab contactId={contact.id} />
              </Tabs.Content>
              <Tabs.Content value="documents" className="outline-none h-full">
                <DocumentsTab contactId={contact.id} />
              </Tabs.Content>
              <Tabs.Content value="emoney" className="outline-none h-full">
                <EMoneyTab contactId={contact.id} />
              </Tabs.Content>
              <Tabs.Content value="insurance" className="outline-none h-full">
                <InsuranceTabWithRefresh contactId={contact.id} />
              </Tabs.Content>
            </div>
          </Tabs.Root>
        </div>

        {/* Right Panel - Additional Information */}
        <div className="lg:w-1/5 xl:w-64 lg:h-full hidden lg:block flex-shrink-0">
          <ContactStats contactId={contact.id} />
        </div>
      </div>
    </div>
  );
}
