
import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";

export async function GET(request) {
  try {
    const { user } = await getSignedInUser(request);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Fetch all tasklists for the user (or all if admin)
    const tasklists = await prisma.tasklist.findMany({
      where: user.role === "admin" ? {} : { contact: { userId: user.id } },
      include: {
        items: true,
        contact: true,
      },
    });

    // Flatten all items
    const allItems = tasklists.flatMap(tl =>
      (tl.items || []).map(item => ({
        ...item,
        contact: tl.contact,
      }))
    );

    // Overdue: incomplete, dueDate before today
    const overdueTasks = allItems
      .filter(item => !item.completed && item.dueDate && new Date(item.dueDate) < today)
      .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate))
      .slice(0, 5);

    return NextResponse.json(overdueTasks);
  } catch (error) {
    console.error("Failed to fetch overdue task metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch overdue task metrics" },
      { status: 500 }
    );
  }
}
