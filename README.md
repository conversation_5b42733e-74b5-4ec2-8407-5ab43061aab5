

SlimCRM Project Summary
SlimCRM is a lightweight CRM (Customer Relationship Management) system with Microsoft integration built using Next.js 15. Key features include:

MongoDB database integration using Prisma ORM
Microsoft Azure AD authentication
Pipeline management with customizable stages
Contact management and tracking
Dashboard with data visualization using Chart.js
Dark/light theme support via Tailwind CSS
Brokerage account integration (Schwab and Interactive Brokers)
Responsive UI with Tailwind CSS and Geist font
The project uses Turbopack for faster local development and follows modern Next.js patterns with the App Router architecture. It's designed to be a streamlined CRM solution with financial services integrations.



--------------------------------------------------------------------------------------------------

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.js`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Database Setup

To set up a new MongoDB instance:

1. Update your `.env` file with your MongoDB connection string:
```bash
DATABASE_URL="mongodb+srv://username:<EMAIL>/slimcrm?retryWrites=true&w=majority"
```

2. Initialize and push the database schema:
```bash
npx prisma generate
npx prisma db push
```

3. Seed the pipeline stages:
```bash
npx prisma db seed
```

This will create the default pipeline stages: Lead, Meeting Scheduled, Proposal Sent, Negotiation, Closed Won, and Closed Lost.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
