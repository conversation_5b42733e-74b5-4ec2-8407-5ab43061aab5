'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import * as Dialog from '@radix-ui/react-dialog';

export default function NoteForm({
  contactId,
  note = null,
  isOpen,
  onClose,
}) {
  const [formData, setFormData] = useState({
    title: '',
    content: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // If editing an existing note, populate the form
  useEffect(() => {
    if (note) {
      setFormData({
        title: note.title,
        content: note.content
      });
    } else {
      // Reset form for new note
      setFormData({
        title: '',
        content: ''
      });
    }
  }, [note, isOpen]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      let url, method, body;

      if (note) {
        // Update existing note
        url = `/api/notes/${note.id}`;
        method = 'PATCH';
        body = formData;
      } else {
        // Create new note
        url = `/api/contacts/${contactId}/notes`;
        method = 'POST';
        body = formData;
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Note save error:', errorData);
        throw new Error(errorData.error || 'Failed to save note');
      }

      const savedNote = await response.json();
      toast.success('Note saved!');
      onClose();
    } catch (error) {
      console.error('Error saving note:', error);
      setError(error.message || 'An error occurred while saving the note');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-[100]" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md z-[110]">
          <Dialog.Title className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            {note ? 'Edit Note' : 'Add New Note'}
          </Dialog.Title>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Title
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Content
              </label>
              <textarea
                value={formData.content}
                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                rows={6}
                required
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover disabled:opacity-50"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : note ? 'Update' : 'Add'}
              </button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
