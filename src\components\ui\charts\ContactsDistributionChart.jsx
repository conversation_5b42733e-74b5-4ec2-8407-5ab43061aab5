'use client';

import { Doughnut } from 'react-chartjs-2';

export default function ContactsDistributionChart({ data }) {
  const chartData = {
    labels: data.map(item => item.type),
    datasets: [{
      data: data.map(item => item._count),
      backgroundColor: [
        '#3B82F6', // blue
        '#10B981', // green
        '#6366F1', // indigo
      ],
      borderWidth: 1,
    }],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      title: {
        display: false,
      },
    },
    cutout: '70%',
  };

  return (
    <div className="h-[300px]">
      <Doughnut data={chartData} options={options} />
    </div>
  );
}