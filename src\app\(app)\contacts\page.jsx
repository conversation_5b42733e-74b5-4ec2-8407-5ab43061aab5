"use client";


import { useState, useEffect } from "react";
import ContactList from "@/components/contacts/ContactList";
import ContactFilters from "@/components/contacts/ContactFilters";
import "./contacts.css";

// Fetch pipeline stages
async function fetchPipelineStages() {
  try {
    const response = await fetch("/api/pipeline/stages");
    if (response.ok) {
      const stages = await response.json();
      const stagesMap = {};
      stages.forEach(stage => {
        stagesMap[stage.id] = stage.name;
      });
      return stagesMap;
    }
  } catch (error) {
    console.error("Failed to fetch pipeline stages:", error);
  }
  return {};
}

// Fetch group contacts for a groupId
async function fetchGroupContacts(groupId) {
  if (!groupId) return [];
  try {
    const response = await fetch(`/api/groups/${groupId}/members`);
    if (response.ok) {
      const members = await response.json();
      if (members && members.length > 0) {
        return members.map(member => String(member.contactId));
      }
    }
  } catch (error) {
    console.error("Failed to fetch group contacts:", error);
  }
  return [];
}

export default function ContactsPage() {
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({});
  const [columnFilters, setColumnFilters] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [filtersExpanded, setFiltersExpanded] = useState(true);
  const [pipelineStages, setPipelineStages] = useState({});
  const [groupContactsCache, setGroupContactsCache] = useState({});
  const [pipelineStagesLoaded, setPipelineStagesLoaded] = useState(false);

  const fetchContacts = async () => {
    try {
      const response = await fetch("/api/contacts");
      console.log({ response });
      if (response.ok) {
        const data = await response.json();
        setContacts(data);
      } else {
        // const errorData = await response.json();
        // console.log({ errorData });
        // if (needsAuth(errorData)) {
        //   router.push("/api/auth/login");
        // }
      }
    } catch (error) {
      console.error("Failed to fetch contacts:", error);
    } finally {
      setLoading(false);
    }
  };


  // Fetch contacts and pipeline stages on mount
  useEffect(() => {
    fetchContacts();
    (async () => {
      const stages = await fetchPipelineStages();
      setPipelineStages(stages);
      setPipelineStagesLoaded(true);
    })();
  }, []);

  // Fetch group contacts when groupId filter changes
  useEffect(() => {
    if (filters.groupId) {
      (async () => {
        const contactIds = await fetchGroupContacts(filters.groupId);
        setGroupContactsCache(prev => ({ ...prev, [filters.groupId]: contactIds }));
      })();
    }
  }, [filters.groupId]);



  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    // Reset all filters to empty values
    setFilters({
      type: "",
      groupId: "",
      pipelineStage: "",
    });

    // Dispatch a custom event to notify components that filters have been cleared
    // This helps ensure all components are aware of the filter reset
    if (typeof window !== "undefined") {
      const clearEvent = new CustomEvent("clearAllFilters");
      window.dispatchEvent(clearEvent);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="fixed inset-0 flex flex-col bg-white dark:bg-gray-900">
      <div className="mb-4 px-4 pt-4 pb-2 bg-white dark:bg-gray-900 z-10">
        <h1 className="text-2xl font-semibold">Contacts</h1>
      </div>

      <div className="flex-1 flex min-h-0 px-4 pb-4" style={{ minHeight: 0 }}>
        {/* Collapsible Filters Panel */}
        <div
          className={`transition-all duration-300 ease-in-out ${filtersExpanded ? 'w-64' : 'w-12'} bg-gray-300 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 rounded-lg mr-3 relative flex flex-col h-full min-h-0`}
          style={{ minHeight: 0 }}
        >
          {/* Toggle Button */}
          <button
            onClick={() => setFiltersExpanded(!filtersExpanded)}
            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 bg-primary text-white border border-primary dark:bg-primary dark:text-white rounded-full p-1.5 shadow-lg z-20 hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
            title={filtersExpanded ? "Collapse sidebar" : "Expand sidebar"}
            style={{ boxShadow: '0 2px 8px 0 rgba(59,130,246,0.15)' }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-4 w-4 transition-transform duration-300 ${filtersExpanded ? 'rotate-0' : 'rotate-180'}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={2}
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          {/* Filters Content */}
          <div className={`flex-1 flex flex-col overflow-y-auto transition-opacity duration-300 ${filtersExpanded ? 'opacity-100' : 'opacity-0'}`}>
            {filtersExpanded && (
              <div className="flex-1 flex flex-col px-3 pt-3 pb-2 min-h-0">
                <ContactFilters
                  onFilterChange={handleFilterChange}
                  contacts={contacts}
                  columnFilters={columnFilters}
                  searchTerm={searchTerm}
                  pipelineStages={pipelineStages}
                  pipelineStagesLoaded={pipelineStagesLoaded}
                  groupContactsCache={groupContactsCache}
                  style={{ flex: 1, minHeight: 0 }}
                />
              </div>
            )}
          </div>

          {/* Collapsed View */}
          {!filtersExpanded && (
            <div className="absolute top-1/2 left-0 w-full flex flex-col items-center justify-center -translate-y-1/2">
              <div
                className="text-gray-700 dark:text-gray-400 font-bold tracking-wider text-base"
                style={{
                  transform: 'rotate(-90deg)',
                  letterSpacing: '0.15em',
                  width: 'max-content',
                  minWidth: '2.5rem',
                  minHeight: '180px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1.1rem',
                  lineHeight: 1.2,
                  textAlign: 'center',
                  userSelect: 'none',
                }}
              >
                Filters and Groups
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="flex-1 min-h-0 flex flex-col overflow-hidden" style={{ minHeight: 0 }}>
          <ContactList
            contacts={contacts}
            filters={filters}
            onRefresh={fetchContacts}
            onClearFilters={handleClearFilters}
            onColumnFiltersChange={setColumnFilters}
            onSearchTermChange={setSearchTerm}
            pipelineStages={pipelineStages}
            pipelineStagesLoaded={pipelineStagesLoaded}
            groupContactsCache={groupContactsCache}
          />
        </div>
      </div>
    </div>
  );
}
