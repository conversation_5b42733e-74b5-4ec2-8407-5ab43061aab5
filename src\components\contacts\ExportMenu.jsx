'use client';

import { useState, useRef, useEffect } from 'react';
import { exportToCSV, exportToPDF } from '@/utils/exportUtils';

export default function ExportMenu({ contacts, selectedFields }) {
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportType, setExportType] = useState('');
  const [pipelineStages, setPipelineStages] = useState({});
  const menuRef = useRef(null);

  // Fetch pipeline stages
  useEffect(() => {
    const fetchPipelineStages = async () => {
      try {
        const response = await fetch('/api/pipeline/stages');
        if (response.ok) {
          const stages = await response.json();
          const stagesMap = {};
          stages.forEach(stage => {
            stagesMap[stage.id] = stage.name;
          });
          setPipelineStages(stagesMap);
        }
      } catch (error) {
        console.error('Failed to fetch pipeline stages:', error);
      }
    };

    fetchPipelineStages();
  }, []);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Helper function to add pipeline stage names to contacts
  const addPipelineStageNames = (contactsToProcess) => {
    return contactsToProcess.map(contact => {
      if (contact.pipelineStage && pipelineStages[contact.pipelineStage]) {
        return {
          ...contact,
          _pipelineStageName: pipelineStages[contact.pipelineStage]
        };
      }
      return contact;
    });
  };

  const handleExportCSV = async () => {
    try {
      setIsExporting(true);
      setExportType('CSV');
      setIsOpen(false);

      // Small delay to allow UI to update
      await new Promise(resolve => setTimeout(resolve, 100));

      // Add pipeline stage names to contacts before export
      const contactsWithStageNames = addPipelineStageNames(contacts);

      exportToCSV(contactsWithStageNames, 'contacts.csv', selectedFields);
    } catch (error) {
      console.error('Error exporting to CSV:', error);
    } finally {
      setIsExporting(false);
      setExportType('');
    }
  };

  const handleExportPDF = async () => {
    try {
      setIsExporting(true);
      setExportType('PDF');
      setIsOpen(false);

      // Small delay to allow UI to update
      await new Promise(resolve => setTimeout(resolve, 100));

      // Add pipeline stage names to contacts before export
      const contactsWithStageNames = addPipelineStageNames(contacts);

      exportToPDF(contactsWithStageNames, 'contacts.pdf', selectedFields);
    } catch (error) {
      console.error('Error exporting to PDF:', error);
    } finally {
      setIsExporting(false);
      setExportType('');
    }
  };

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => !isExporting && setIsOpen(!isOpen)}
        disabled={isExporting}
        title="Export contacts to CSV or PDF"
        className={`px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 flex items-center text-sm min-w-[100px] justify-center ${isExporting ? 'opacity-75 cursor-not-allowed' : ''}`}
      >
        {isExporting ? (
          <>
            <svg className="animate-spin -ml-1 mr-1 h-4 w-4 text-gray-700 dark:text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Exporting...
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 border border-gray-200 dark:border-gray-700">
          <div className="py-1">
            <button
              onClick={handleExportCSV}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export to CSV
            </button>
            <button
              onClick={handleExportPDF}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              Export to PDF
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
