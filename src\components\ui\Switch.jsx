'use client';

import { useState, useEffect } from 'react';

export default function Switch({ id, checked, onChange, disabled, size = 'md', label }) {
  const [isChecked, setIsChecked] = useState(checked);

  // Update internal state when checked prop changes
  useEffect(() => {
    setIsChecked(checked);
  }, [checked]);

  const handleChange = () => {
    if (disabled) return;

    const newValue = !isChecked;
    setIsChecked(newValue);

    if (onChange) {
      // For compatibility with existing code that expects a checkbox-like onChange
      onChange();
    }
  };

  // Size variants
  const sizeClasses = {
    sm: {
      switch: 'w-8 h-4',
      knob: 'w-3 h-3',
      translate: 'translate-x-4',
    },
    md: {
      switch: 'w-10 h-5',
      knob: 'w-4 h-4',
      translate: 'translate-x-5',
    },
    lg: {
      switch: 'w-12 h-6',
      knob: 'w-5 h-5',
      translate: 'translate-x-6',
    },
  };

  const currentSize = sizeClasses[size] || sizeClasses.md;

  return (
    <div className="flex items-center">
      <button
        type="button"
        id={id}
        role="switch"
        aria-checked={isChecked}
        disabled={disabled}
        className={`${currentSize.switch} flex items-center rounded-full p-0.5 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
          isChecked
            ? 'bg-primary'
            : 'bg-gray-300 dark:bg-gray-600'
        } ${
          disabled
            ? 'opacity-50 cursor-not-allowed'
            : 'cursor-pointer'
        }`}
        onClick={handleChange}
      >
        <span
          className={`${currentSize.knob} ${
            isChecked ? currentSize.translate : 'translate-x-0'
          } rounded-full bg-white shadow-sm transform transition-transform duration-200 ease-in-out`}
        />
      </button>
      {label && (
        <label
          htmlFor={id}
          className={`ml-2 text-sm text-gray-700 dark:text-gray-300 ${disabled ? 'opacity-50' : ''} overflow-hidden`}
          onClick={disabled ? undefined : handleChange}
        >
          {typeof label === 'string' ? (
            <span className="truncate block" title={label}>{label}</span>
          ) : (
            label
          )}
        </label>
      )}
    </div>
  );
}
