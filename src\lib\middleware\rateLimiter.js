import rateLimit from 'express-rate-limit';
import { NextResponse } from 'next/server';

// Store for rate limiting attempts
const attemptStore = new Map();

// Clean up old entries periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of attemptStore.entries()) {
    if (now - data.timestamp > 15 * 60 * 1000) { // 15 minutes
      attemptStore.delete(key);
    }
  }
}, 60 * 1000); // Clean up every minute

export function getRateLimiter(options = {}) {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 5, // Limit each IP to 5 requests per windowMs
    message = 'Too many account opening attempts. Please try again later.'
  } = options;

  return async function rateLimiter(req) {
    const ip = req.headers.get('x-forwarded-for') || 'unknown';
    const now = Date.now();
    
    const currentAttempts = attemptStore.get(ip) || { count: 0, timestamp: now };
    
    // Reset count if window has passed
    if (now - currentAttempts.timestamp > windowMs) {
      currentAttempts.count = 0;
      currentAttempts.timestamp = now;
    }
    
    // Increment attempt count
    currentAttempts.count++;
    attemptStore.set(ip, currentAttempts);
    
    // Check if limit exceeded
    if (currentAttempts.count > max) {
      console.warn(`Rate limit exceeded for IP: ${ip}`);
      return NextResponse.json(
        { error: message },
        { status: 429 }
      );
    }
    
    return null; // Continue to next middleware/handler
  };
}