import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

export async function GET(request, { params }) {
  try {
    // Get the date 30 days ago
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get total count of activities in the last 30 days
    const { id } = await params;
    const totalCount = await prisma.activity.count({
      where: {
        contactId: id,
        date: {
          gte: thirtyDaysAgo
        }
      }
    });

    // Get count by activity type
    const byTypeResults = await prisma.activity.groupBy({
      by: ['type'],
      _count: true,
      where: {
        contactId: id,
        date: {
          gte: thirtyDaysAgo
        }
      }
    });

    // Convert the results to a more usable format
    const byType = {};
    byTypeResults.forEach(result => {
      byType[result.type] = result._count;
    });

    return NextResponse.json({
      total: totalCount,
      byType
    });
  } catch (error) {
    console.error('Failed to fetch activity stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activity stats' },
      { status: 500 }
    );
  }
}
