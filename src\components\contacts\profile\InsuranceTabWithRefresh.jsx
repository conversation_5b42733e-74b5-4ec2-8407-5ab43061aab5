import { useState, useEffect } from "react";
import InsuranceTab from "./InsuranceTab";

export default function InsuranceTabWithRefresh({ contactId }) {
  const [contact, setContact] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchContact = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/contacts/${contactId}`);
        if (!response.ok) throw new Error("Failed to fetch contact");
        const data = await response.json();
        setContact(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    fetchContact();
  }, [contactId]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-red-500">{error}</div>;
  if (!contact) return <div>Contact not found</div>;
  return <InsuranceTab contact={contact} />;
}
