import { useState } from 'react';
import { CheckSquare, Square, ChevronDown, ChevronUp, Edit, Trash2 } from 'lucide-react';

export default function TasklistCard({
  tasklist,
  isExpanded,
  onToggleExpand,
  onEdit,
  onDelete,
  onItemToggle,
  onItemEdit,
  onItemDelete
}) {
  const [isHovered, setIsHovered] = useState(false);

  // Calculate completion percentage
  const totalItems = tasklist.items.length;
  const completedItems = tasklist.items.filter(item => item.completed).length;
  const completionPercentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

  // Format dates
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get status badge color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'archived':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div 
      className={`bg-gray-100 dark:bg-gray-900 border-2 border-gray-300 dark:border-gray-500 rounded-lg shadow-sm overflow-hidden transition-all duration-200 ${isHovered ? 'shadow-md' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Tasklist Header */}
      <div className="p-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 border-b border-gray-300 dark:border-gray-500">
        <div className="flex-1">
          <div className="flex items-center">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">{tasklist.title}</h4>
            <span className={`ml-2 px-2 py-0.5 text-xs font-medium rounded-full ${getStatusColor(tasklist.status)}`}>{tasklist.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
          </div>
          {tasklist.description && (
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">{tasklist.description}</p>
          )}
          <div className="mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400">
            <span>Created: {formatDate(tasklist.createdAt)}</span>
            {tasklist.updatedAt && tasklist.updatedAt !== tasklist.createdAt && (
              <span className="ml-2">· Updated: {formatDate(tasklist.updatedAt)}</span>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button onClick={onEdit} className="p-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300" title="Edit tasklist"><Edit className="h-4 w-4" /></button>
          <button onClick={onDelete} className="p-1 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300" title="Delete tasklist"><Trash2 className="h-4 w-4" /></button>
          <button onClick={onToggleExpand} className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300" title={isExpanded ? "Collapse" : "Expand"}>{isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}</button>
        </div>
      </div>
      {/* Progress Bar */}
      <div className="px-4 py-2 bg-gray-50 dark:bg-gray-700/50 border-b border-gray-300 dark:border-gray-500">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Progress</span>
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">{completionPercentage}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
          <div className="bg-primary h-2.5 rounded-full transition-all duration-300" style={{ width: `${completionPercentage}%` }}></div>
        </div>
        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 text-right">{completedItems} of {totalItems} items completed</div>
      </div>
      {/* Tasklist Items */}
      {isExpanded && (
        <div className="p-4 space-y-2 bg-white dark:bg-gray-800">
          {tasklist.items.length > 0 ? (
            tasklist.items.map((item) => {
              // Overdue logic: not completed and dueDate in the past
              const isOverdue = !item.completed && item.dueDate && new Date(item.dueDate) < new Date(new Date().toDateString());
              return (
                <div key={item.id} className={`p-3 rounded-md border-2
                  ${item.completed
                    ? 'bg-green-300 dark:bg-green-900/60 border-green-600 dark:border-green-700'
                    : isOverdue
                      ? 'bg-red-100 dark:bg-red-900/40 border-red-400 dark:border-red-700'
                      : 'bg-yellow-100 dark:bg-yellow-900/40 border-yellow-400 dark:border-yellow-700'}
                `}>
                <div className="flex items-start gap-2">
                  <button onClick={() => onItemToggle(tasklist.id, item.id, !item.completed)} className={`mt-0.5 ${item.completed ? 'text-green-600 dark:text-green-500' : 'text-gray-400 dark:text-gray-500 hover:text-primary dark:hover:text-primary-light'}`}>{item.completed ? <CheckSquare className="h-5 w-5" /> : <Square className="h-5 w-5" />}</button>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <p className={`text-sm font-medium ${item.completed ? 'text-gray-600 dark:text-gray-400 line-through' : 'text-gray-900 dark:text-white'}`}>{item.title}</p>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border
                        ${item.priority === 'high'
                          ? 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30'
                        : item.priority === 'medium'
                          ? 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30'
                        : 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30'}`}>
                        {item.priority === 'high' ? '🔴 High' : item.priority === 'medium' ? '🟡 Medium' : '🔵 Low'}
                      </span>
                      {item.dueDate && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-gray-100 text-gray-700 border border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700">
                          📅 Due: {new Date(item.dueDate).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                    {item.description && (
                      <p className={`mt-1 text-xs ${item.completed ? 'text-gray-500 dark:text-gray-500 line-through' : 'text-gray-600 dark:text-gray-400'}`}>{item.description}</p>
                    )}
                    {item.completed && item.completedAt && (
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">Completed: {formatDate(item.completedAt)}</p>
                    )}
                  </div>
                  <div className="flex flex-col gap-1 ml-2">
                    <button onClick={() => onItemEdit(tasklist.id, item)} className="p-1 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300" title="Edit item"><Edit className="h-4 w-4" /></button>
                    <button onClick={() => onItemDelete(tasklist.id, item.id)} className="p-1 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300" title="Delete item"><Trash2 className="h-4 w-4" /></button>
                  </div>
                </div>
              </div>
                );
            })
          ) : (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400">No items in this tasklist</div>
          )}
        </div>
      )}
    </div>
  );
}
