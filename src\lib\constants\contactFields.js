// Define all available contact fields that can be displayed in the table

export const CONTACT_FIELDS = [
  // --- Compliance ---
  {
    id: "privacyPolicySent",
    label: "Privacy Policy Sent",
    render: (c) => (c.privacyPolicySent ? "Yes" : "No"),
    sortKey: "privacyPolicySent",
    isDefault: false,
  },
  {
    id: "privacyPolicyDate",
    label: "Privacy Policy Date",
    render: (c) =>
      c.privacyPolicyDate
        ? new Date(c.privacyPolicyDate).toLocaleDateString()
        : "",
    sortKey: "privacyPolicyDate",
    isDefault: false,
  },
  {
    id: "formAdvSent",
    label: "Form ADV Sent",
    render: (c) => (c.formAdvSent ? "Yes" : "No"),
    sortKey: "formAdvSent",
    isDefault: false,
  },
  {
    id: "formAdvDate",
    label: "Form ADV Date",
    render: (c) =>
      c.formAdvDate ? new Date(c.formAdvDate).toLocaleDateString() : "",
    sortKey: "formAdvDate",
    isDefault: false,
  },
  {
    id: "schwabEnvelopeStatus",
    label: "Schwab Envelope Status",
    render: (c) => c.schwabEnvelopeStatus || "",
    sortKey: "schwabEnvelopeStatus",
    isDefault: false,
  },
  {
    id: "schwabEnvelopeDate",
    label: "Schwab Envelope Date",
    render: (c) =>
      c.schwabEnvelopeDate
        ? new Date(c.schwabEnvelopeDate).toLocaleDateString()
        : "",
    sortKey: "schwabEnvelopeDate",
    isDefault: false,
  },
  {
    id: "advisoryAgreementStatus",
    label: "Advisory Agreement Status",
    render: (c) => c.advisoryAgreementStatus || "",
    sortKey: "advisoryAgreementStatus",
    isDefault: false,
  },
  {
    id: "advisoryAgreementDate",
    label: "Advisory Agreement Date",
    render: (c) =>
      c.advisoryAgreementDate
        ? new Date(c.advisoryAgreementDate).toLocaleDateString()
        : "",
    sortKey: "advisoryAgreementDate",
    isDefault: false,
  },
  {
    id: "onboardingStatus",
    label: "Onboarding Status",
    render: (c) => c.onboardingStatus || "",
    sortKey: "onboardingStatus",
    isDefault: false,
  },
  {
    id: "onboardingDate",
    label: "Onboarding Date",
    render: (c) =>
      c.onboardingDate ? new Date(c.onboardingDate).toLocaleDateString() : "",
    sortKey: "onboardingDate",
    isDefault: false,
  },
  {
    id: "governmentIssuedIdType",
    label: "Government Issued Id Type",
    render: (c) => c.governmentIssuedIdType || "",
    sortKey: "governmentIssuedIdType",
    isDefault: false,
  },
  {
    id: "governmentIssuedId",
    label: "Government Issued Id",
    render: (c) => c.governmentIssuedId || "",
    sortKey: "governmentIssuedId",
    isDefault: false,
  },
  {
    id: "lastReviewDate",
    label: "Last Review Date",
    render: (c) =>
      c.lastReviewDate ? new Date(c.lastReviewDate).toLocaleDateString() : "",
    sortKey: "lastReviewDate",
    isDefault: false,
  },
  {
    id: "nextReviewDate",
    label: "Next Review Date",
    render: (c) =>
      c.nextReviewDate ? new Date(c.nextReviewDate).toLocaleDateString() : "",
    sortKey: "nextReviewDate",
    isDefault: false,
  },
  {
    id: "lastContactDate",
    label: "Last Contact Date",
    render: (c) =>
      c.lastContactDate ? new Date(c.lastContactDate).toLocaleDateString() : "",
    sortKey: "lastContactDate",
    isDefault: false,
  },

  // --- Contact Info ---
  {
    id: "company",
    label: "Company",
    render: (c) => c.company || "",
    sortKey: "company",
    isDefault: true,
  },
  {
    id: "gender",
    label: "Gender",
    render: (c) => c.gender || "",
    sortKey: "gender",
    isDefault: false,
  },
  {
    id: "dateOfBirth",
    label: "Date of Birth",
    render: (c) =>
      c.dateOfBirth ? new Date(c.dateOfBirth).toLocaleDateString() : "",
    sortKey: "dateOfBirth",
    isDefault: false,
  },
  {
    id: "maritalStatus",
    label: "Marital Status",
    render: (c) => c.maritalStatus || "",
    sortKey: "maritalStatus",
    isDefault: false,
  },
  {
    id: "addressLine1",
    label: "Address Line 1",
    render: (c) => c.addressLine1 || "",
    sortKey: "addressLine1",
    isDefault: false,
  },
  {
    id: "addressLine2",
    label: "Address Line 2",
    render: (c) => c.addressLine2 || "",
    sortKey: "addressLine2",
    isDefault: false,
  },
  {
    id: "city",
    label: "City",
    render: (c) => c.city || "",
    sortKey: "city",
    isDefault: false,
  },
  {
    id: "state",
    label: "State",
    render: (c) => c.state || "",
    sortKey: "state",
    isDefault: false,
  },
  {
    id: "postalCode",
    label: "Postal Code",
    render: (c) => c.postalCode || "",
    sortKey: "postalCode",
    isDefault: false,
  },
  {
    id: "country",
    label: "Country",
    render: (c) => c.country || "",
    sortKey: "country",
    isDefault: false,
  },
  {
    id: "jobTitle",
    label: "Job Title",
    render: (c) => c.jobTitle || "",
    sortKey: "jobTitle",
    isDefault: false,
  },
  {
    id: "department",
    label: "Department",
    render: (c) => c.department || "",
    sortKey: "department",
    isDefault: false,
  },
  {
    id: "trustedContactName",
    label: "Trusted Contact Name",
    render: (c) => c.trustedContactName || "",
    sortKey: "trustedContactName",
    isDefault: false,
  },
  {
    id: "trustedContactPhone",
    label: "Trusted Contact Phone",
    render: (c) => c.trustedContactPhone || "",
    sortKey: "trustedContactPhone",
    isDefault: false,
  },
  {
    id: "trustedContactEmail",
    label: "Trusted Contact Email",
    render: (c) => c.trustedContactEmail || "",
    sortKey: "trustedContactEmail",
    isDefault: false,
  },
  {
    id: "middleName",
    label: "Middle Name",
    render: (c) => c.middleName || "",
    sortKey: "middleName",
    isDefault: false,
  },
  {
    id: "preferredName",
    label: "Preferred Name",
    render: (c) => c.preferredName || "",
    sortKey: "preferredName",
    isDefault: false,
  },

  // --- CRM ---
  {
    id: "communicationPreferences",
    label: "Communication Preferences",
    render: (c) => c.communicationPreferences || "",
    sortKey: "communicationPreferences",
    isDefault: false,
  },
  {
    id: "preferredLanguage",
    label: "Preferred Language",
    render: (c) => c.preferredLanguage || "",
    sortKey: "preferredLanguage",
    isDefault: false,
  },
  {
    id: "referredBy",
    label: "Referred By",
    render: (c) => c.referredBy || "",
    sortKey: "referredBy",
    isDefault: false,
  },
  {
    id: "referralSource",
    label: "Referral Source",
    render: (c) => c.referralSource || "",
    sortKey: "referralSource",
    isDefault: false,
  },
  {
    id: "leadSource",
    label: "Lead Source",
    render: (c) => c.leadSource || "",
    sortKey: "leadSource",
    isDefault: false,
  },
  {
    id: "leadScore",
    label: "Lead Score",
    render: (c) => (c.leadScore != null ? c.leadScore : ""),
    sortKey: "leadScore",
    isDefault: false,
  },
  {
    id: "lastActivityDate",
    label: "Last Activity Date",
    render: (c) =>
      c.lastActivityDate
        ? new Date(c.lastActivityDate).toLocaleDateString()
        : "",
    sortKey: "lastActivityDate",
    isDefault: false,
  },
  {
    id: "preferredContactMethod",
    label: "Preferred Contact Method",
    render: (c) => c.preferredContactMethod || "",
    sortKey: "preferredContactMethod",
    isDefault: false,
  },
  {
    id: "doNotContact",
    label: "Do Not Contact",
    render: (c) => (c.doNotContact ? "Yes" : "No"),
    sortKey: "doNotContact",
    isDefault: false,
  },

  // --- Essential ---
  {
    id: "tags",
    label: "Tags",
    render: (c) => (c.tags || []).join(", "),
    sortKey: "tags",
    isDefault: false,
  },
  {
    id: "lastName",
    label: "Last Name",
    render: (c) => c.lastName || "",
    sortKey: "lastName",
    isDefault: true,
  },
  {
    id: "firstName",
    label: "First Name",
    render: (c) => c.firstName || "",
    sortKey: "firstName",
    isDefault: true,
  },
  {
    id: "phone",
    label: "Phone",
    render: (c) => c.phone || "",
    sortKey: "phone",
    isDefault: true,
  },
  {
    id: "type",
    label: "Type",
    render: (c) => c.type,
    sortKey: "type",
    isDefault: true,
  },
  {
    id: "email",
    label: "Email",
    render: (c) => c.email || "",
    sortKey: "email",
    isDefault: true,
  },
  {
    id: "pipelineStage",
    label: "Pipeline Stage",
    render: (c) => c.pipelineStage || "",
    sortKey: "pipelineStage",
    isDefault: false,
  },
  {
    id: "updated",
    label: "Updated",
    render: (c) => (c.updated ? new Date(c.updated).toLocaleDateString() : ""),
    sortKey: "updated",
    isDefault: false,
  },
  {
    id: "created",
    label: "Created",
    render: (c) => (c.created ? new Date(c.created).toLocaleDateString() : ""),
    sortKey: "created",
    isDefault: false,
  },

  // --- Investment Advisory ---
  {
    id: "clientType",
    label: "Client Type",
    render: (c) => c.clientType || "",
    sortKey: "clientType",
    isDefault: false,
  },
  {
    id: "clientStatus",
    label: "Client Status",
    render: (c) => c.clientStatus || "",
    sortKey: "clientStatus",
    isDefault: false,
  },
  {
    id: "riskTolerance",
    label: "Risk Tolerance",
    render: (c) => c.riskTolerance || "",
    sortKey: "riskTolerance",
    isDefault: false,
  },
  {
    id: "investmentObjective",
    label: "Investment Objective",
    render: (c) => (c.investmentObjective || []).join(", "),
    sortKey: "investmentObjective",
    isDefault: false,
  },
  {
    id: "investmentExperience",
    label: "Investment Experience",
    render: (c) => c.investmentExperience || "",
    sortKey: "investmentExperience",
    isDefault: false,
  },
  {
    id: "annualIncome",
    label: "Annual Income",
    render: (c) => (c.annualIncome != null ? c.annualIncome : ""),
    sortKey: "annualIncome",
    isDefault: false,
  },
  {
    id: "netWorth",
    label: "Net Worth",
    render: (c) => (c.netWorth != null ? c.netWorth : ""),
    sortKey: "netWorth",
    isDefault: false,
  },
  {
    id: "liquidNetWorth",
    label: "Liquid Net Worth",
    render: (c) => (c.liquidNetWorth != null ? c.liquidNetWorth : ""),
    sortKey: "liquidNetWorth",
    isDefault: false,
  },
  {
    id: "sourceOfFunds",
    label: "Source of Funds",
    render: (c) => c.sourceOfFunds || "",
    sortKey: "sourceOfFunds",
    isDefault: false,
  },
  {
    id: "employmentStatus",
    label: "Employment Status",
    render: (c) => c.employmentStatus || "",
    sortKey: "employmentStatus",
    isDefault: false,
  },
  {
    id: "employerName",
    label: "Employer Name",
    render: (c) => c.employerName || "",
    sortKey: "employerName",
    isDefault: false,
  },
  {
    id: "employerAddress",
    label: "Employer Address",
    render: (c) => c.employerAddress || "",
    sortKey: "employerAddress",
    isDefault: false,
  },
  {
    id: "occupation",
    label: "Occupation",
    render: (c) => c.occupation || "",
    sortKey: "occupation",
    isDefault: false,
  },
  {
    id: "yearsWithEmployer",
    label: "Years with Employer",
    render: (c) => (c.yearsWithEmployer != null ? c.yearsWithEmployer : ""),
    sortKey: "yearsWithEmployer",
    isDefault: false,
  },
  {
    id: "taxBracket",
    label: "Tax Bracket",
    render: (c) => c.taxBracket || "",
    sortKey: "taxBracket",
    isDefault: false,
  },
  {
    id: "citizenship",
    label: "Citizenship",
    render: (c) => c.citizenship || "",
    sortKey: "citizenship",
    isDefault: false,
  },
  {
    id: "ssnOrTaxId",
    label: "SSN or Tax ID",
    render: (c) => c.ssnOrTaxId || "",
    sortKey: "ssnOrTaxId",
    isDefault: false,
  },
  {
    id: "regulatoryStatus",
    label: "Regulatory Status",
    render: (c) => c.regulatoryStatus || "",
    sortKey: "regulatoryStatus",
    isDefault: false,
  },
  {
    id: "complianceFlags",
    label: "Compliance Flags",
    render: (c) => (c.complianceFlags || []).join(", "),
    sortKey: "complianceFlags",
    isDefault: false,
  },
  {
    id: "accountNumbers",
    label: "Account Numbers",
    render: (c) => (c.accountNumbers || []).join(", "),
    sortKey: "accountNumbers",
    isDefault: false,
  },
  {
    id: "custodian",
    label: "Custodian",
    render: (c) => c.custodian || "",
    sortKey: "custodian",
    isDefault: false,
  },
  {
    id: "accountType",
    label: "Account Type",
    render: (c) => c.accountType || "",
    sortKey: "accountType",
    isDefault: false,
  },
  {
    id: "accountOpenDate",
    label: "Account Open Date",
    render: (c) =>
      c.accountOpenDate ? new Date(c.accountOpenDate).toLocaleDateString() : "",
    sortKey: "accountOpenDate",
    isDefault: false,
  },
  {
    id: "accountCloseDate",
    label: "Account Close Date",
    render: (c) =>
      c.accountCloseDate
        ? new Date(c.accountCloseDate).toLocaleDateString()
        : "",
    sortKey: "accountCloseDate",
    isDefault: false,
  },
  {
    id: "accountStatus",
    label: "Account Status",
    render: (c) => c.accountStatus || "",
    sortKey: "accountStatus",
    isDefault: false,
  },
  {
    id: "beneficiaryNames",
    label: "Beneficiary Names",
    render: (c) => (c.beneficiaryNames || []).join(", "),
    sortKey: "beneficiaryNames",
    isDefault: false,
  },
  // { id: 'trustedContactName', label: 'Trusted Contact Name', render: c => c.trustedContactName || '', sortKey: 'trustedContactName', isDefault: false },
  // { id: 'trustedContactPhone', label: 'Trusted Contact Phone', render: c => c.trustedContactPhone || '', sortKey: 'trustedContactPhone', isDefault: false },
  // { id: 'trustedContactEmail', label: 'Trusted Contact Email', render: c => c.trustedContactEmail || '', sortKey: 'trustedContactEmail', isDefault: false },
  {
    id: "estatePlanOnFile",
    label: "Estate Plan On File",
    render: (c) => (c.estatePlanOnFile ? "Yes" : "No"),
    sortKey: "estatePlanOnFile",
    isDefault: false,
  },
  {
    id: "attorneyName",
    label: "Attorney Name",
    render: (c) => c.attorneyName || "",
    sortKey: "attorneyName",
    isDefault: false,
  },
  {
    id: "attorneyEmail",
    label: "Attorney Email",
    render: (c) => c.attorneyEmail || "",
    sortKey: "attorneyEmail",
    isDefault: false,
  },
  {
    id: "cpaName",
    label: "CPA Name",
    render: (c) => c.cpaName || "",
    sortKey: "cpaName",
    isDefault: false,
  },
  {
    id: "cpaEmail",
    label: "CPA Email",
    render: (c) => c.cpaEmail || "",
    sortKey: "cpaEmail",
    isDefault: false,
  },
  {
    id: "financialAdvisorName",
    label: "Financial Advisor Name",
    render: (c) => c.financialAdvisorName || "",
    sortKey: "financialAdvisorName",
    isDefault: false,
  },
  {
    id: "financialAdvisorEmail",
    label: "Financial Advisor Email",
    render: (c) => c.financialAdvisorEmail || "",
    sortKey: "financialAdvisorEmail",
    isDefault: false,
  },

  // --- Insurance ---
  {
    id: "weightLb",
    label: "Weight (lb)",
    render: (c) => (c.weightLb != null ? c.weightLb : ""),
    sortKey: "weightLb",
    isDefault: false,
  },
  {
    id: "heightInches",
    label: "Height (in)",
    render: (c) => (c.heightInches != null ? c.heightInches : ""),
    sortKey: "heightInches",
    isDefault: false,
  },
  {
    id: "insuranceType",
    label: "Insurance Type",
    render: (c) => c.insuranceType || "",
    sortKey: "insuranceType",
    isDefault: false,
  },
  {
    id: "insuranceEstimatedAnnualPremiumBudget",
    label: "Insurance Estimated Annual Premium Budget",
    render: (c) =>
      c.insuranceEstimatedAnnualPremiumBudget != null
        ? c.insuranceEstimatedAnnualPremiumBudget
        : "",
    sortKey: "insuranceEstimatedAnnualPremiumBudget",
    isDefault: false,
  },
  {
    id: "insuranceClientInfoSubmittedDate",
    label: "Insurance Client Info Submitted Date",
    render: (c) =>
      c.insuranceClientInfoSubmittedDate
        ? new Date(c.insuranceClientInfoSubmittedDate).toLocaleDateString()
        : "",
    sortKey: "insuranceClientInfoSubmittedDate",
    isDefault: false,
  },
  {
    id: "insuranceQuoteReceivedDate",
    label: "Insurance Quote Received Date",
    render: (c) =>
      c.insuranceQuoteReceivedDate
        ? new Date(c.insuranceQuoteReceivedDate).toLocaleDateString()
        : "",
    sortKey: "insuranceQuoteReceivedDate",
    isDefault: false,
  },
  {
    id: "insuranceQuestionnaireSentToClientDate",
    label: "Insurance Questionnaire Sent To Client Date",
    render: (c) =>
      c.insuranceQuestionnaireSentToClientDate
        ? new Date(
            c.insuranceQuestionnaireSentToClientDate
          ).toLocaleDateString()
        : "",
    sortKey: "insuranceQuestionnaireSentToClientDate",
    isDefault: false,
  },
  {
    id: "insuranceDocumentsSubmittedToCarrierDate",
    label: "Insurance Documents Submitted to Carrier Date",
    render: (c) =>
      c.insuranceDocumentsSubmittedToCarrierDate
        ? new Date(
            c.insuranceDocumentsSubmittedToCarrierDate
          ).toLocaleDateString()
        : "",
    sortKey: "insuranceDocumentsSubmittedToCarrierDate",
    isDefault: false,
  },
  {
    id: "insuranceCompletionDate",
    label: "Insurance Completion Date",
    render: (c) =>
      c.insuranceCompletionDate
        ? new Date(c.insuranceCompletionDate).toLocaleDateString()
        : "",
    sortKey: "insuranceCompletionDate",
    isDefault: false,
  },
  {
    id: "insuranceStatus",
    label: "Insurance Status",
    render: (c) => c.insuranceStatus || "",
    sortKey: "insuranceStatus",
    isDefault: false,
  },
];

// Get default fields
export const DEFAULT_FIELDS = CONTACT_FIELDS.filter(
  (field) => field.isDefault
).map((field) => field.id);

// Get field by ID
export const getFieldById = (id) => {
  if (!id) {
    console.warn("getFieldById called with undefined or null id");
    return null;
  }
  const field = CONTACT_FIELDS.find((field) => field.id === id);
  if (!field) {
    console.warn(`Field with id '${id}' not found`);
  }
  return field;
};
