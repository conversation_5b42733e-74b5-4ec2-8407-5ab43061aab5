'use client';

import React, { useState, useRef, useEffect } from 'react';

export default function Tooltip({ children, content, position = 'bottom', className = '' }) {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const childRef = useRef(null);
  const tooltipRef = useRef(null);

  const showTooltip = () => {
    setIsVisible(true);
  };

  const hideTooltip = () => {
    setIsVisible(false);
  };

  // Calculate tooltip position when it becomes visible
  useEffect(() => {
    if (isVisible && childRef.current && tooltipRef.current) {
      const childRect = childRef.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();

      let top, left;

      switch (position) {
        case 'top':
          top = childRect.top - tooltipRect.height - 8;
          left = childRect.left + (childRect.width / 2) - (tooltipRect.width / 2);
          break;
        case 'bottom':
          top = childRect.bottom + 8;
          left = childRect.left + (childRect.width / 2) - (tooltipRect.width / 2);
          break;
        case 'left':
          top = childRect.top + (childRect.height / 2) - (tooltipRect.height / 2);
          left = childRect.left - tooltipRect.width - 8;
          break;
        case 'right':
          top = childRect.top + (childRect.height / 2) - (tooltipRect.height / 2);
          left = childRect.right + 8;
          break;
        default:
          top = childRect.bottom + 8;
          left = childRect.left + (childRect.width / 2) - (tooltipRect.width / 2);
      }

      // Ensure tooltip stays within viewport
      if (top < 0) top = 0;
      if (left < 0) left = 0;
      if (top + tooltipRect.height > window.innerHeight) {
        top = window.innerHeight - tooltipRect.height;
      }
      if (left + tooltipRect.width > window.innerWidth) {
        left = window.innerWidth - tooltipRect.width;
      }

      setTooltipPosition({ top, left });
    }
  }, [isVisible, position]);

  return (
    <div
      className={`inline-block ${className}`}
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      ref={childRef}
    >
      {children}

      {isVisible && (
        <div
          ref={tooltipRef}
          className="fixed z-50 px-3 py-2 text-xs font-normal text-white bg-gray-800 dark:bg-gray-900 rounded shadow-lg pointer-events-none whitespace-normal"
          style={{
            top: `${tooltipPosition.top}px`,
            left: `${tooltipPosition.left}px`,
            minWidth: '200px',
            maxWidth: '300px',
            lineHeight: '1.4'
          }}
        >
          {content}
          <div
            className={`absolute w-2 h-2 bg-gray-800 dark:bg-gray-900 transform rotate-45
              ${position === 'top' ? 'bottom-0 -mb-1 left-1/2 -translate-x-1/2' :
                position === 'bottom' ? 'top-0 -mt-1 left-1/2 -translate-x-1/2' :
                position === 'left' ? 'right-0 -mr-1 top-1/2 -translate-y-1/2' :
                'left-0 -ml-1 top-1/2 -translate-y-1/2'}`}
          />
        </div>
      )}
    </div>
  );
}
