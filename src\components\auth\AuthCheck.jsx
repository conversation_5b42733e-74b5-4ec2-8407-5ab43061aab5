"use client";
import { useAuth } from "@/components/providers/AuthContext";
import LoginButton from "./LoginButton";
import styles from "./authCheck.module.css";
import Image from "next/image";

export default function AuthCheck({ children }) {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <section className={styles.landing}>
        <div className={styles.flexSplit}>
          <div className={styles.left}>
            <div className={styles.copyContainer}>
              <div className={styles.copy}>
                <h1>Great Wealth Investments</h1>
                <p>Own Your Future</p>
              </div>
            </div>
          </div>
          <div className={styles.right}>
            <div className={styles.loginBox}>
              <div className={styles.logoContainer}>
                <Image
                  src="/brand-assets/great-wealth-investments-logo.png"
                  alt="Great Wealth Investments"
                  width={200}
                  height={53}
                  priority
                />
              </div>
              <h2>Welcome Back</h2>
              <p>
                Sign in to access your
                <br />
                client relationship dashboard
              </p>
              <div className={styles.loginButtonContainer}>
                <LoginButton />
              </div>
              <div className={styles.securityNote}>
                <small>Secure login powered by Microsoft Azure AD</small>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return children;
}
