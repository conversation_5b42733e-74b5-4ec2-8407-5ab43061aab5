import { NextResponse } from 'next/server';
import { getSignedInUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma/client';

// GET /api/admin/overview - Get comprehensive overview analytics
export async function GET(request) {
  try {
    // Check authentication and authorization
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Get query parameters for date filtering
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '30'; // days
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Calculate date range
    const now = new Date();
    const daysAgo = parseInt(timeframe);
    const dateFilter = startDate && endDate 
      ? { gte: new Date(startDate), lte: new Date(endDate) }
      : { gte: new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000) };

    // Fetch all data in parallel
    const [
      executiveSummary,
      pipelineWaterfall,
      contactHealth,
      stagePerformance,
      activityTrends,
      topPerformers,
      riskAnalysis
    ] = await Promise.all([
      getExecutiveSummary(dateFilter),
      getPipelineWaterfall(dateFilter),
      getContactHealth(),
      getStagePerformance(dateFilter),
      getActivityTrends(dateFilter),
      getTopPerformers(),
      getRiskAnalysis()
    ]);

    return NextResponse.json({
      executiveSummary,
      pipelineWaterfall,
      contactHealth,
      stagePerformance,
      activityTrends,
      topPerformers,
      riskAnalysis,
      metadata: {
        timeframe: daysAgo,
        startDate: dateFilter.gte,
        endDate: dateFilter.lte || now,
        generatedAt: now
      }
    });

  } catch (error) {
    console.error('Error fetching overview analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch overview analytics' },
      { status: 500 }
    );
  }
}

async function getExecutiveSummary(dateFilter) {
  // Total contacts
  const totalContacts = await prisma.contact.count();
  
  // Contacts created in timeframe
  const newContacts = await prisma.contact.count({
    where: { createdAt: dateFilter }
  });
  
  // Active pipeline contacts (have a pipeline stage)
  const activePipelineContacts = await prisma.contact.count({
    where: { pipelineStage: { not: null } }
  });
  
  // Activities in timeframe
  const totalActivities = await prisma.activity.count({
    where: { createdAt: dateFilter }
  });
  
  // Previous period for comparison
  const previousPeriodStart = new Date(dateFilter.gte.getTime() - (dateFilter.gte.getTime() - (dateFilter.lte || new Date()).getTime()));
  const previousPeriodEnd = dateFilter.gte;
  
  const previousNewContacts = await prisma.contact.count({
    where: { 
      createdAt: { 
        gte: previousPeriodStart, 
        lte: previousPeriodEnd 
      } 
    }
  });
  
  const previousActivities = await prisma.activity.count({
    where: { 
      createdAt: { 
        gte: previousPeriodStart, 
        lte: previousPeriodEnd 
      } 
    }
  });
  
  // Calculate growth rates
  const contactGrowthRate = previousNewContacts > 0 
    ? ((newContacts - previousNewContacts) / previousNewContacts) * 100 
    : 0;
    
  const activityGrowthRate = previousActivities > 0 
    ? ((totalActivities - previousActivities) / previousActivities) * 100 
    : 0;
  
  // Calculate conversion rate (contacts that moved to final stage)
  const finalStages = await prisma.pipelineStage.findMany({
    where: { name: { in: ['Closed Won', 'Client', 'Converted'] } }
  });
  
  const conversions = await prisma.contactStageHistory.count({
    where: {
      stageId: { in: finalStages.map(s => s.id) },
      enteredAt: dateFilter
    }
  });
  
  const conversionRate = activePipelineContacts > 0 
    ? (conversions / activePipelineContacts) * 100 
    : 0;

  return {
    totalContacts,
    newContacts,
    activePipelineContacts,
    totalActivities,
    conversionRate,
    contactGrowthRate,
    activityGrowthRate,
    averageActivitiesPerContact: totalContacts > 0 ? totalActivities / totalContacts : 0
  };
}

async function getPipelineWaterfall(dateFilter) {
  // Get all pipeline stages
  const stages = await prisma.pipelineStage.findMany({
    orderBy: { order: 'asc' }
  });
  
  const waterfallData = [];
  
  for (let i = 0; i < stages.length; i++) {
    const stage = stages[i];
    const nextStage = stages[i + 1];
    
    // Contacts that entered this stage in the timeframe
    const entered = await prisma.contactStageHistory.count({
      where: {
        stageId: stage.id,
        enteredAt: dateFilter
      }
    });
    
    // Contacts currently in this stage
    const current = await prisma.contact.count({
      where: { pipelineStage: stage.id }
    });
    
    // Contacts that moved to next stage
    const converted = nextStage ? await prisma.contactStageHistory.count({
      where: {
        stageId: nextStage.id,
        enteredAt: dateFilter,
        contact: {
          stageHistory: {
            some: {
              stageId: stage.id,
              exitedAt: { not: null }
            }
          }
        }
      }
    }) : 0;
    
    // Contacts that dropped off from this stage
    const dropped = await prisma.contactStageHistory.count({
      where: {
        stageId: stage.id,
        exitedAt: dateFilter,
        contact: {
          pipelineStage: null // No longer in pipeline
        }
      }
    });
    
    const conversionRate = entered > 0 ? (converted / entered) * 100 : 0;
    const dropOffRate = entered > 0 ? (dropped / entered) * 100 : 0;
    
    waterfallData.push({
      stage: stage.name,
      stageId: stage.id,
      order: stage.order,
      entered,
      current,
      converted,
      dropped,
      conversionRate,
      dropOffRate
    });
  }
  
  return waterfallData;
}

async function getContactHealth() {
  // Get lifecycle metrics summary
  const healthMetrics = await prisma.contactLifecycleMetrics.groupBy({
    by: ['riskLevel'],
    _count: true
  });
  
  const dormantCount = await prisma.contactLifecycleMetrics.count({
    where: { isDormant: true }
  });
  
  const highValueCount = await prisma.contactLifecycleMetrics.count({
    where: { isHighValue: true }
  });
  
  const totalWithMetrics = await prisma.contactLifecycleMetrics.count();
  
  // Engagement score distribution
  const engagementDistribution = await prisma.contactLifecycleMetrics.groupBy({
    by: ['engagementScore'],
    _count: true,
    where: {
      engagementScore: { not: null }
    }
  });
  
  // Group engagement scores into ranges
  const engagementRanges = {
    'High (70-100)': 0,
    'Medium (40-69)': 0,
    'Low (0-39)': 0
  };
  
  engagementDistribution.forEach(item => {
    const score = item.engagementScore;
    if (score >= 70) engagementRanges['High (70-100)'] += item._count;
    else if (score >= 40) engagementRanges['Medium (40-69)'] += item._count;
    else engagementRanges['Low (0-39)'] += item._count;
  });
  
  return {
    riskDistribution: healthMetrics.reduce((acc, item) => {
      acc[item.riskLevel] = item._count;
      return acc;
    }, { low: 0, medium: 0, high: 0 }),
    dormantCount,
    highValueCount,
    totalWithMetrics,
    engagementRanges,
    dormantPercentage: totalWithMetrics > 0 ? (dormantCount / totalWithMetrics) * 100 : 0,
    highValuePercentage: totalWithMetrics > 0 ? (highValueCount / totalWithMetrics) * 100 : 0
  };
}

async function getStagePerformance(dateFilter) {
  const stages = await prisma.pipelineStage.findMany({
    orderBy: { order: 'asc' }
  });
  
  const performance = [];
  
  for (const stage of stages) {
    // Average time in stage
    const completedStageEntries = await prisma.contactStageHistory.findMany({
      where: {
        stageId: stage.id,
        exitedAt: { not: null },
        enteredAt: dateFilter
      },
      select: {
        duration: true
      }
    });
    
    const averageDuration = completedStageEntries.length > 0
      ? completedStageEntries.reduce((sum, entry) => sum + (entry.duration || 0), 0) / completedStageEntries.length
      : null;
    
    // Current contacts in stage
    const currentContacts = await prisma.contact.count({
      where: { pipelineStage: stage.id }
    });
    
    // Activities in this stage
    const stageActivities = await prisma.activity.count({
      where: {
        createdAt: dateFilter,
        contact: {
          pipelineStage: stage.id
        }
      }
    });
    
    performance.push({
      stage: stage.name,
      stageId: stage.id,
      order: stage.order,
      averageDuration,
      currentContacts,
      stageActivities,
      activitiesPerContact: currentContacts > 0 ? stageActivities / currentContacts : 0
    });
  }
  
  return performance;
}

async function getActivityTrends(dateFilter) {
  // Daily activity counts for the timeframe
  const activities = await prisma.activity.findMany({
    where: { createdAt: dateFilter },
    select: {
      createdAt: true,
      type: true
    }
  });
  
  // Group by date
  const dailyTrends = {};
  activities.forEach(activity => {
    const date = activity.createdAt.toISOString().split('T')[0];
    if (!dailyTrends[date]) {
      dailyTrends[date] = { total: 0, byType: {} };
    }
    dailyTrends[date].total++;
    dailyTrends[date].byType[activity.type] = (dailyTrends[date].byType[activity.type] || 0) + 1;
  });
  
  return Object.entries(dailyTrends).map(([date, data]) => ({
    date,
    ...data
  })).sort((a, b) => a.date.localeCompare(b.date));
}

async function getTopPerformers() {
  // Top contacts by engagement score
  const topEngaged = await prisma.contactLifecycleMetrics.findMany({
    where: {
      engagementScore: { not: null }
    },
    orderBy: { engagementScore: 'desc' },
    take: 10,
    include: {
      contact: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          pipelineStage: true
        }
      }
    }
  });
  
  // Most active contacts (by activity count)
  const mostActive = await prisma.contactLifecycleMetrics.findMany({
    where: {
      totalActivities: { gt: 0 }
    },
    orderBy: { totalActivities: 'desc' },
    take: 10,
    include: {
      contact: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          pipelineStage: true
        }
      }
    }
  });
  
  return {
    topEngaged: topEngaged.map(item => ({
      contact: item.contact,
      engagementScore: item.engagementScore,
      totalActivities: item.totalActivities
    })),
    mostActive: mostActive.map(item => ({
      contact: item.contact,
      totalActivities: item.totalActivities,
      engagementScore: item.engagementScore
    }))
  };
}

async function getRiskAnalysis() {
  // Get all pipeline stages for lookup
  const pipelineStages = await prisma.pipelineStage.findMany({
    select: {
      id: true,
      name: true,
      order: true
    }
  });

  const stageMap = pipelineStages.reduce((map, stage) => {
    map[stage.id] = stage;
    return map;
  }, {});

  // High risk contacts (engagement score < 30 OR no activity in 30+ days OR stuck in stage 90+ days)
  const highRiskContacts = await prisma.contactLifecycleMetrics.findMany({
    where: { riskLevel: 'high' },
    include: {
      contact: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          company: true,
          pipelineStage: true
        }
      }
    },
    orderBy: { lastActivityDate: 'asc' },
    take: 50
  });
  
  // Dormant contacts (no activity in 30+ days)
  const dormantContacts = await prisma.contactLifecycleMetrics.findMany({
    where: { isDormant: true },
    include: {
      contact: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          company: true,
          pipelineStage: true
        }
      }
    },
    orderBy: { lastActivityDate: 'asc' },
    take: 50
  });
  
  // Contacts stuck in stages (more than 60 days in current stage)
  const stuckContacts = await prisma.contactLifecycleMetrics.findMany({
    where: {
      currentStageDays: { gt: 60 },
      contact: { pipelineStage: { not: null } }
    },
    include: {
      contact: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          company: true,
          pipelineStage: true
        }
      }
    },
    orderBy: { currentStageDays: 'desc' },
    take: 50
  });
  
  return {
    highRiskContacts: highRiskContacts.map(item => ({
      contact: item.contact,
      riskLevel: item.riskLevel,
      lastActivityDate: item.lastActivityDate,
      currentStageDays: item.currentStageDays,
      engagementScore: item.engagementScore,
      stageName: item.contact.pipelineStage ? stageMap[item.contact.pipelineStage]?.name || 'Unknown Stage' : 'No Stage'
    })),
    dormantContacts: dormantContacts.map(item => ({
      contact: item.contact,
      lastActivityDate: item.lastActivityDate,
      daysInPipeline: item.daysInPipeline,
      engagementScore: item.engagementScore,
      stageName: item.contact.pipelineStage ? stageMap[item.contact.pipelineStage]?.name || 'Unknown Stage' : 'No Stage'
    })),
    stuckContacts: stuckContacts.map(item => ({
      contact: item.contact,
      currentStageDays: item.currentStageDays,
      engagementScore: item.engagementScore,
      stageName: item.contact.pipelineStage ? stageMap[item.contact.pipelineStage]?.name || 'Unknown Stage' : 'No Stage'
    }))
  };
}
