"use client";

import { useState, useEffect } from "react";
import { Plus, Edit2, Trash2, Eye, Search, Filter } from "lucide-react";
import { EditorProvider } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Image from "@tiptap/extension-image";
import { Link as LinkExtension } from "@tiptap/extension-link";
import TextAlign from "@tiptap/extension-text-align";
import TextStyle from "@tiptap/extension-text-style";
import Toolbar from "@/components/ui/editor/Toolbar";
import "@/components/ui/editor/editor.css";

const EmailTemplates = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCategory, setFilterCategory] = useState("all");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    subject: "",
    content: "",
  });
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  // Mock data - replace with actual API call
  useEffect(() => {
    const fetchTemplates = async () => {
      setLoading(true);
      // Simulate API call
      const response = await fetch("/api/email-templates?scope=global");
      const data = await response.json();
      setTemplates(data);
      setLoading(false);
    };

    fetchTemplates();
  }, []);

  console.log("Templates:", templates);

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.subject.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      filterCategory === "all" || template.category === filterCategory;
    return matchesSearch && matchesCategory;
  });

  const handleClosePreview = () => {
    setPreviewTemplate(null);
    setIsPreviewModalOpen(false);
  };

  const handlePreviewTemplate = (template) => {
    setPreviewTemplate(template);
    setIsPreviewModalOpen(true);
  };

  const handleEditTemplate = (template) => {
    setEditingTemplate(template);
    setFormData({
      name: template.name,
      description: template.description,
      subject: template.subject,
      content: template.content,
    });
    setIsEditModalOpen(true);
  };

  const handleDeleteTemplate = async (templateId) => {
    if (window.confirm("Are you sure you want to delete this template?")) {
      try {
        setLoading(true);
        const response = await fetch(`/api/email-templates/${templateId}`, {
          method: "DELETE",
        });
        if (!response.ok) {
          throw new Error("Failed to delete template");
        }
        // Fetch updated templates after deletion

        const res = await fetch("/api/email-templates?scope=global");
        const updatedTemplates = await res.json();
        setTemplates(updatedTemplates);
        setLoading(false);
      } catch (error) {
        console.error("Failed to delete template:", error);
        setError("Failed to delete template");
      }
    }
  };

  const handleToggleActive = async (template) => {
    try {
      const response = await fetch(
        `/api/email-templates/${template.id}/toggle-active`,
        {
          method: "PATCH",
          body: JSON.stringify(template),
        }
      );
      if (!response.ok) {
        throw new Error("Failed to toggle active status");
      }

      const updatedTemplates = await response.json();

      setTemplates(updatedTemplates);
    } catch (error) {
      console.error("Failed to toggle active status:", error);
      setError("Failed to toggle active status");
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");

    try {
      const response = await fetch("/api/email-templates?scope=global", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData), // Send formData directly without combining title and content
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to create template");
      }

      // router.push("/emails?tab=templates");
      const newTemplates = await response.json();
      setTemplates(newTemplates);
      // reset the form data and close the modal
      setFormData({
        name: "",
        description: "",
        subject: "",
        content: "",
      });
      setIsCreateModalOpen(false);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");

    try {
      const response = await fetch(
        `/api/email-templates/${editingTemplate.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        }
      );

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to update template");
      }

      // Fetch updated templates after editing
      const res = await fetch("/api/email-templates?scope=global");
      const updatedTemplates = await res.json();

      setTemplates(updatedTemplates);
      setIsEditModalOpen(false);
      setFormData({
        name: "",
        description: "",
        subject: "",
        content: "",
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Global Email Templates
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Create and manage email templates that all users can access
            </p>
          </div>
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>New Template</span>
          </button>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredTemplates.map((template) => (
          <div
            key={template.id}
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-lg transition-shadow"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                  {template.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {template.subject}
                </p>
                <div className="flex items-center gap-2">
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      template.isActiveGlobal
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                        : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                    }`}
                  >
                    {template.isActiveGlobal ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </div>

            <div className="text-xs text-gray-500 dark:text-gray-400 mb-3">
              <p>
                Created: {new Date(template.createdAt).toLocaleDateString()}
              </p>
              <p>
                Updated: {new Date(template.updatedAt).toLocaleDateString()}
              </p>
              <p>
                Created By: {template.user.email} ({template.user.name})
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handlePreviewTemplate(template)}
                  className="p-1 text-gray-500 hover:text-primary dark:text-gray-400 dark:hover:text-primary"
                  title="Preview"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleEditTemplate(template)}
                  className="p-1 text-gray-500 hover:text-primary dark:text-gray-400 dark:hover:text-primary"
                  title="Edit"
                >
                  <Edit2 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeleteTemplate(template.id)}
                  className="p-1 text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
                  title="Delete"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
              <button
                onClick={() => handleToggleActive(template)}
                className={`text-xs px-2 py-1 rounded ${
                  template.isActiveGlobal
                    ? "text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
                    : "text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:bg-green-900/20"
                }`}
              >
                {template.isActiveGlobal ? "Deactivate" : "Activate"}
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No templates found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {searchTerm || filterCategory !== "all"
              ? "Try adjusting your search or filter criteria"
              : "Get started by creating your first email template"}
          </p>
        </div>
      )}

      {/* Modals would go here - CreateTemplateModal, EditTemplateModal, PreviewModal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-4xl max-h-[95vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">
              Create New Global Template
            </h2>

            <form onSubmit={handleSubmit} className="space-y-x">
              <div className="mb-4">
                <label
                  htmlFor="name"
                  className="block text-sm font-medium mb-2"
                >
                  Template Name
                </label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                  required
                />
              </div>
              <div className="mb-4">
                <label
                  htmlFor="description"
                  className="block text-sm font-medium mb-2"
                >
                  Description
                </label>
                <input
                  type="text"
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                />
              </div>
              <div className="mb-4">
                <label
                  htmlFor="subject"
                  className="block text-sm font-medium mb-2"
                >
                  Email Subject
                </label>
                <input
                  type="text"
                  id="subject"
                  value={formData.subject}
                  onChange={(e) =>
                    setFormData({ ...formData, subject: e.target.value })
                  }
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                  required
                />
              </div>

              <div className="mb-4">
                <label
                  htmlFor="content"
                  className="block text-sm font-medium mb-2"
                >
                  Email Content
                </label>
                <div className="rounded-lg border dark:border-gray-700 overflow-hidden">
                  <EditorProvider
                    extensions={[
                      StarterKit,
                      Image.configure({
                        HTMLAttributes: {
                          class: "rounded-lg max-w-full h-auto",
                        },
                        allowBase64: true,
                      }),
                      LinkExtension.configure({
                        openOnClick: false,
                        HTMLAttributes: {
                          class:
                            "text-primary hover:text-primary-hover underline",
                        },
                      }),
                      TextAlign.configure({
                        types: ["heading", "paragraph"],
                      }),
                      TextStyle,
                    ]}
                    content={formData.content}
                    editorProps={{
                      attributes: {
                        class:
                          "ProseMirror p-4 min-h-[300px] focus:outline-none",
                      },
                    }}
                    onUpdate={({ editor }) => {
                      setFormData((prev) => ({
                        ...prev,
                        content: editor.getHTML(),
                      }));
                    }}
                    slotBefore={<Toolbar />}
                  ></EditorProvider>
                </div>
              </div>
              <div className="flex justify-end space-x-2 mt-6">
                <button
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 rounded"
                >
                  Cancel
                </button>
                <button className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                  Create Template
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {isEditModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-4xl max-h-[95vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">Edit Global Template</h2>

            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            <form onSubmit={handleEditSubmit} className="space-y-4">
              <div className="mb-4">
                <label
                  htmlFor="edit-name"
                  className="block text-sm font-medium mb-2"
                >
                  Template Name
                </label>
                <input
                  type="text"
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                  required
                />
              </div>

              <div className="mb-4">
                <label
                  htmlFor="edit-description"
                  className="block text-sm font-medium mb-2"
                >
                  Description
                </label>
                <input
                  type="text"
                  id="edit-description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      description: e.target.value,
                    })
                  }
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                />
              </div>

              <div className="mb-4">
                <label
                  htmlFor="edit-subject"
                  className="block text-sm font-medium mb-2"
                >
                  Email Subject
                </label>
                <input
                  type="text"
                  id="edit-subject"
                  value={formData.subject}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      subject: e.target.value,
                    })
                  }
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                  required
                />
              </div>

              <div className="mb-4">
                <label
                  htmlFor="edit-content"
                  className="block text-sm font-medium mb-2"
                >
                  Email Content
                </label>
                <div className="rounded-lg border dark:border-gray-700 overflow-hidden">
                  <EditorProvider
                    key={editingTemplate.id} // Force re-render when editing different template
                    extensions={[
                      StarterKit,
                      Image.configure({
                        HTMLAttributes: {
                          class: "rounded-lg max-w-full h-auto",
                        },
                        allowBase64: true,
                      }),
                      LinkExtension.configure({
                        openOnClick: false,
                        HTMLAttributes: {
                          class:
                            "text-primary hover:text-primary-hover underline",
                        },
                      }),
                      TextAlign.configure({
                        types: ["heading", "paragraph"],
                      }),
                      TextStyle,
                    ]}
                    content={formData.content}
                    editorProps={{
                      attributes: {
                        class:
                          "ProseMirror p-4 min-h-[300px] focus:outline-none",
                      },
                    }}
                    onUpdate={({ editor }) => {
                      setFormData((prev) => ({
                        ...prev,
                        content: editor.getHTML(),
                      }));
                    }}
                    slotBefore={<Toolbar />}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setEditingTemplate(null);
                    setFormData({
                      id: "",
                      name: "",
                      description: "",
                      subject: "",
                      content: "",
                    });
                    setError("");
                    setIsEditModalOpen(false);
                  }}
                  disabled={isSubmitting}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 rounded disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 disabled:opacity-50"
                >
                  {isSubmitting ? "Updating..." : "Update Template"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {isPreviewModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-4xl max-h-[95vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Preview Template
              </h2>
              <button
                onClick={handleClosePreview}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Template Info */}
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Template Name
                  </h3>
                  <p className="text-gray-900 dark:text-white">
                    {previewTemplate.name}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Status
                  </h3>
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      previewTemplate.isActiveGlobal
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                        : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                    }`}
                  >
                    {previewTemplate.isActiveGlobal ? "Active" : "Inactive"}
                  </span>
                </div>
                {previewTemplate.description && (
                  <div className="md:col-span-2">
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Description
                    </h3>
                    <p className="text-gray-900 dark:text-white">
                      {previewTemplate.description}
                    </p>
                  </div>
                )}
                <div className="md:col-span-2">
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Created By
                  </h3>
                  <p className="text-gray-900 dark:text-white">
                    {previewTemplate.user?.name || "Unknown"} (
                    {previewTemplate.user?.email || "N/A"})
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Created
                  </h3>
                  <p className="text-gray-900 dark:text-white">
                    {new Date(previewTemplate.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Last Updated
                  </h3>
                  <p className="text-gray-900 dark:text-white">
                    {new Date(previewTemplate.updatedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>

            {/* Email Preview */}
            <div className="space-y-4">
              {/* Email Subject */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email Subject
                </h3>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border">
                  <p className="text-gray-900 dark:text-white font-medium">
                    {previewTemplate.subject}
                  </p>
                </div>
              </div>

              {/* Email Content */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email Content
                </h3>
                <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                  {/* Email Header (simulated) */}
                  <div className="bg-gray-100 dark:bg-gray-700 px-4 py-3 border-b border-gray-300 dark:border-gray-600">
                    <div className="flex items-center space-x-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">
                          From:
                        </span>
                        <span className="text-gray-900 dark:text-white ml-1">
                          <EMAIL>
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">
                          To:
                        </span>
                        <span className="text-gray-900 dark:text-white ml-1">
                          <EMAIL>
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 text-sm">
                      <span className="font-medium text-gray-700 dark:text-gray-300">
                        Subject:
                      </span>
                      <span className="text-gray-900 dark:text-white ml-1">
                        {previewTemplate.subject}
                      </span>
                    </div>
                  </div>

                  {/* Email Body */}
                  <div className="bg-white dark:bg-gray-800 p-6">
                    <div
                      className="prose prose-sm max-w-none dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-white prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-a:text-primary prose-strong:text-gray-900 dark:prose-strong:text-white"
                      dangerouslySetInnerHTML={{
                        __html: previewTemplate.content,
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
              <div className="flex space-x-2">
                {/* <button
                  onClick={() => {
                    handleClosePreview();
                    handleEditTemplate(previewTemplate);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 text-sm bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                >
                  <Edit2 className="h-4 w-4" />
                  <span>Edit Template</span>
                </button> */}
                <button
                  onClick={() => {
                    handleToggleActive(previewTemplate);
                    handleClosePreview();
                  }}
                  className={`flex items-center space-x-2 px-4 py-2 text-sm rounded-lg transition-colors ${
                    previewTemplate.isActiveGlobal
                      ? "bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/40"
                      : "bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/40"
                  }`}
                >
                  <span>
                    {previewTemplate.isActiveGlobal ? "Deactivate" : "Activate"}
                  </span>
                </button>
              </div>
              <button
                onClick={handleClosePreview}
                className="px-4 py-2 text-sm text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailTemplates;
