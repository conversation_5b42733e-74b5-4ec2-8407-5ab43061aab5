import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";
import { auditLogger } from "@/lib/services/auditLogger";

// GET /api/documents - Get all documents
export async function GET(request) {
  try {
    // Get the current user
    const { user } = await getSignedInUser(request);

    const searchParams = request.nextUrl.searchParams;
    const contactId = searchParams.get("contactId");
    const source = searchParams.get("source");

    // Build the where clause based on query parameters
    const where = {};

    if (contactId) {
      // Build where clause for contact verification
      const contactWhereClause = {
        id: contactId,
      };

      // Only filter by user ID if not an admin
      if (user.role !== "admin") {
        contactWhereClause.userId = user.id;
      }

      // Verify the contact exists
      const contact = await prisma.contact.findFirst({
        where: contactWhereClause,
      });

      if (!contact) {
        return NextResponse.json(
          { error: "Contact not found" },
          { status: 404 }
        );
      }

      where.contactId = contactId;
    } else {
      // If no specific contact is requested
      if (user.role !== "admin") {
        // For non-admin users, only show documents for their contacts
        where.contact = {
          userId: user.id,
        };
      }
      // For admin users, don't filter by user ID to show all documents
    }

    if (source) {
      where.source = source;
    }

    const documents = await prisma.document.findMany({
      where,
      orderBy: {
        uploadedAt: "desc",
      },
      include: {
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });



    // Group documents by externalId to find duplicates (same document associated with multiple contacts)
    const documentsByExternalId = {};

    documents.forEach(doc => {
      const key = `${doc.externalId || doc.id}-${doc.source}`;

      if (!documentsByExternalId[key]) {
        documentsByExternalId[key] = {
          doc,
          contacts: []
        };
      }

      if (doc.contact) {
        documentsByExternalId[key].contacts.push({
          id: doc.contact.id,
          name: `${doc.contact.firstName || ''} ${doc.contact.lastName || ''}`.trim() || `Contact ID: ${doc.contact.id}`
        });
      }
    });

    // Format the documents for the response
    const formattedDocuments = Object.values(documentsByExternalId).map(({ doc, contacts }) => {
      // Determine contact display
      let contactId = null;
      let contactName = null;

      if (contacts.length === 1) {
        contactId = contacts[0].id;
        contactName = contacts[0].name;
      } else if (contacts.length > 1) {
        contactId = contacts.map(c => c.id).join(',');
        // Show "Multiple (X)" where X is the number of contacts
        contactName = `Multiple (${contacts.length})`;
      }

      return {
        id: doc.id,
        name: doc.name,
        type: doc.fileType,
        size: doc.fileSize,
        tags: doc.tags || [],
        uploadedAt: doc.uploadedAt,
        uploadedBy: doc.uploadedBy,
        contactId: contactId,
        contactName: contactName,
        contactsData: contacts, // Always include full contacts data for tooltip
        source: doc.source,
        folder: doc.folder,
        url: doc.url,
        externalId: doc.externalId,
        driveId: doc.driveId,
      };
    });

    return NextResponse.json(formattedDocuments);
  } catch (error) {
    console.error("Failed to fetch documents:", error);
    return NextResponse.json(
      { error: "Failed to fetch documents: " + error.message },
      { status: 500 }
    );
  }
}

// POST /api/documents - Create a new document
export async function POST(request) {
  try {
    const data = await request.json();

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Validate required fields
    if (!data.name || !data.contactId) {
      return NextResponse.json(
        { error: "Name and contactId are required" },
        { status: 400 }
      );
    }

    // Create the document
    const document = await prisma.document.create({
      data: {
        name: data.name,
        fileType: data.fileType || "unknown",
        fileSize: data.fileSize || "0 KB",
        tags: data.tags || [],
        uploadedAt: new Date(),
        uploadedBy: data.uploadedBy || "System",
        contactId: data.contactId,
        source: data.source || "local",
        folder: data.folder || "/",
        url: data.url || null,
        externalId: data.externalId || null,
        driveId: data.driveId || null,
      },
    });

    // Log the document creation
    await auditLogger.logDocumentCreate({
      userId: user.id,
      document,
      request
    });

    return NextResponse.json(document, { status: 201 });
  } catch (error) {
    console.error("Failed to create document:", error);
    return NextResponse.json(
      { error: "Failed to create document: " + error.message },
      { status: 500 }
    );
  }
}
