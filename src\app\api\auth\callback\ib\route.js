import { NextResponse } from 'next/server';
import { brokerageAccountOpener } from '@/lib/brokerage/accountOpening';
import { getRateLimiter } from '@/lib/middleware/rateLimiter';
import { auditLogger } from '@/lib/services/auditLogger';

const rateLimiter = getRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many Interactive Brokers authentication attempts. Please try again later.'
});

export async function GET(request) {
  // Apply rate limiting
  const rateLimitResult = await rateLimiter(request);
  if (rateLimitResult) return rateLimitResult;

  const ip = request.headers.get('x-forwarded-for') || 'unknown';
  const userAgent = request.headers.get('user-agent');

  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    
    if (!code) {
      await auditLogger.logAuthCallback({
        provider: 'ib',
        status: 'error',
        errorMessage: 'Authorization code is missing',
        ip
      });
      throw new Error('Authorization code is missing');
    }

    // Validate state parameter to prevent CSRF attacks
    try {
      brokerageAccountOpener.validateCallback(state, searchParams.get('state'));
    } catch (error) {
      await auditLogger.logAuthCallback({
        provider: 'ib',
        status: 'error',
        errorMessage: 'Invalid authentication state',
        ip
      });
      return NextResponse.redirect(
        new URL('/error?message=Invalid authentication state&source=ib', request.url)
      );
    }

    // Store the auth code securely in an HTTP-only cookie
    const response = NextResponse.redirect(new URL('/contacts', request.url));
    response.cookies.set('ib_auth_code', code, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 3600, // 1 hour
      path: '/'
    });

    // Set a success message cookie that the client can read
    response.cookies.set('account_opening_status', 'success_ib', {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30, // 30 seconds
      path: '/'
    });

    // Log successful authentication
    await auditLogger.logAuthCallback({
      provider: 'ib',
      status: 'success',
      ip
    });

    return response;
  } catch (error) {
    console.error('IB auth callback error:', error);
    
    // Log the error
    await auditLogger.logAuthCallback({
      provider: 'ib',
      status: 'error',
      errorMessage: error.message || 'Failed to complete Interactive Brokers account setup',
      ip
    });
    
    // Construct error URL with specific error message
    const errorUrl = new URL('/error', request.url);
    errorUrl.searchParams.set('source', 'ib');
    errorUrl.searchParams.set('message', error.message || 'Failed to complete Interactive Brokers account setup');
    
    return NextResponse.redirect(errorUrl);
  }
}