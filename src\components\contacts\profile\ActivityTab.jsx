'use client';

import { useState, useEffect } from 'react';
import { Settings } from 'lucide-react';
import { useAuth } from '@/components/providers/AuthContext';
import { hasRole } from '@/lib/auth';
import { ROLE_ADMIN } from '@/constants/permissions';
import ActivityForm from './ActivityForm';
import ActivityFilter from './ActivityFilter';
import ActivityTimeline from './ActivityTimeline';
import SearchInput from '@/components/common/SearchInput';
import ActivityTypeManager from '@/components/admin/ActivityTypeManager';
import * as Dialog from '@radix-ui/react-dialog';
import { X } from 'lucide-react';

export default function ActivityTab({ contactId, onActivityChange }) {
  const { user } = useAuth();
  const [allActivities, setAllActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isActivityFormOpen, setIsActivityFormOpen] = useState(false);
  const [currentActivity, setCurrentActivity] = useState(null);
  const [isActivityTypeManagerOpen, setIsActivityTypeManagerOpen] = useState(false);
  const [filters, setFilters] = useState({
    types: [], // Changed to array for multiselect
    sortBy: 'date',
    sortOrder: 'desc',
    searchTerm: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  });
  const [activityTypes, setActivityTypes] = useState([]);

  // Fetch activity types once for filtering
  useEffect(() => {
    const fetchActivityTypes = async () => {
      try {
        const response = await fetch('/api/activity-types');
        if (response.ok) {
          const types = await response.json();
          setActivityTypes(types);
          console.log('Fetched activity types for filtering:', types);
        }
      } catch (error) {
        console.error('Error fetching activity types:', error);
      }
    };

    fetchActivityTypes();
  }, []);

  const fetchAllActivities = async (page = 1) => {
    setLoading(true);
    setError('');
    try {
      console.log('Fetching all activities with filters:', filters);

      // Fetch regular activities
      // Only send API-supported sort parameters, handle title sorting client-side
      const apiSortBy = filters.sortBy === 'title' ? 'date' : filters.sortBy;
      const apiSortOrder = filters.sortBy === 'title' ? 'desc' : filters.sortOrder;

      const queryParams = new URLSearchParams({
        page: page.toString(),
        pageSize: pagination.pageSize.toString(),
        sortBy: apiSortBy,
        sortOrder: apiSortOrder
      });

      // Note: Type filtering and title sorting are done client-side

      const activitiesUrl = `/api/contacts/${contactId}/activities?${queryParams}`;
      console.log('Fetching activities from URL:', activitiesUrl);
      const activitiesResponse = await fetch(activitiesUrl);

      if (!activitiesResponse.ok) {
        const errorText = await activitiesResponse.text();
        console.error('Activities API response not OK:', activitiesResponse.status, errorText);
        throw new Error(`Failed to fetch activities: ${activitiesResponse.status} ${errorText}`);
      }

      const activitiesData = await activitiesResponse.json();
      const activities = activitiesData.activities || [];

      // Fetch tasks
      // Fetch tasklists and flatten items as tasks
      const tasklistsUrl = `/api/contacts/${contactId}/tasklists`;
      const tasklistsResponse = await fetch(tasklistsUrl);
      if (!tasklistsResponse.ok) {
        const errorText = await tasklistsResponse.text();
        console.error('Tasklists API response not OK:', tasklistsResponse.status, errorText);
        throw new Error(`Failed to fetch tasklists: ${tasklistsResponse.status} ${errorText}`);
      }
      const tasklists = await tasklistsResponse.json();
      // Flatten all items from all tasklists
      const tasks = tasklists.flatMap(tl => (tl.items || []).map(item => ({
        ...item,
        tasklistTitle: tl.title
      })));
      // Convert tasks to activity format
      const taskActivities = tasks.map(task => ({
        id: task.id,
        type: 'task',
        description: task.description || '',
        date: task.dueDate,
        createdAt: task.createdAt,
        title: task.title,
        priority: task.priority,
        completed: task.completed,
        completedAt: task.completedAt,
        originalType: 'task',
        originalData: task
      }));

      // Fetch notes
      const notesUrl = `/api/contacts/${contactId}/notes`;
      console.log('Fetching notes from URL:', notesUrl);
      const notesResponse = await fetch(notesUrl);

      if (!notesResponse.ok) {
        const errorText = await notesResponse.text();
        console.error('Notes API response not OK:', notesResponse.status, errorText);
        throw new Error(`Failed to fetch notes: ${notesResponse.status} ${errorText}`);
      }

      const notes = await notesResponse.json();

      // Convert notes to activity format
      const noteActivities = notes.map(note => ({
        id: note.id,
        type: 'note',
        description: note.content,
        date: note.createdAt,
        createdAt: note.createdAt,
        title: note.title,
        originalType: 'note',
        originalData: note
      }));

      // Mark regular activities
      const markedActivities = activities.map(activity => ({
        ...activity,
        originalType: 'activity',
        originalData: activity
      }));

      // Combine all activities
      let combinedActivities = [...markedActivities, ...taskActivities, ...noteActivities];

      // Apply client-side filtering for activity types (multiselect)
      if (filters.types && filters.types.length > 0 && activityTypes.length > 0) {
        console.log('Filtering activities by types:', filters.types);
        console.log('Available activity types:', activityTypes.map(t => ({ id: t.id, name: t.name })));
        console.log('Sample activity types in data:', combinedActivities.slice(0, 3).map(a => ({ id: a.id, type: a.type })));

        // Create mapping from ObjectId to name and name to name
        const activityTypeMap = {};
        activityTypes.forEach(type => {
          activityTypeMap[type.id] = type.name.toLowerCase();
          activityTypeMap[type.name.toLowerCase()] = type.name.toLowerCase();
        });

        combinedActivities = combinedActivities.filter(activity => {
          const activityType = activity.type?.toLowerCase();

          // Check if any selected filter type matches this activity
          const matches = filters.types.some(selectedTypeId => {
            // Direct string match (e.g., 'call' === 'call')
            if (selectedTypeId.toLowerCase() === activityType) {
              return true;
            }

            // ObjectId to name mapping (e.g., ObjectId maps to 'call')
            const mappedTypeName = activityTypeMap[selectedTypeId];
            if (mappedTypeName && mappedTypeName === activityType) {
              return true;
            }

            return false;
          });

          console.log(`Activity ${activity.id} (${activityType}) matches selected types: ${matches}`);
          return matches;
        });
        console.log(`Filtered to ${combinedActivities.length} activities by type`);
      }

      // Sort combined activities
      combinedActivities = sortActivities(combinedActivities, filters.sortBy, filters.sortOrder);

      // Apply client-side filtering for search
      if (filters.searchTerm && filters.searchTerm.trim() !== '') {
        try {
          const searchTerm = filters.searchTerm.toLowerCase().trim();
          console.log('Filtering activities by search term:', searchTerm);

          combinedActivities = combinedActivities.filter(activity => {
            // Skip invalid activities
            if (!activity) return false;

            // Safely check if title exists and includes search term
            const titleMatch = activity.title ?
              activity.title.toLowerCase().includes(searchTerm) : false;

            // Safely check if description exists and includes search term
            const descriptionMatch = activity.description ?
              activity.description.toLowerCase().includes(searchTerm) : false;

            return titleMatch || descriptionMatch;
          });

          console.log(`Filtered to ${combinedActivities.length} activities`);
        } catch (searchError) {
          console.error('Error during search filtering:', searchError);
        }
      }

      // Apply pagination
      const totalCount = combinedActivities.length;
      const startIndex = (page - 1) * pagination.pageSize;
      const endIndex = startIndex + pagination.pageSize;
      const paginatedActivities = combinedActivities.slice(startIndex, endIndex);

      setAllActivities(paginatedActivities);
      setPagination({
        ...pagination,
        page,
        total: totalCount,
        totalPages: Math.ceil(totalCount / pagination.pageSize)
      });
    } catch (error) {
      console.error('Failed to fetch activities:', error);
      setError(`Failed to load activities: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to sort activities
  const sortActivities = (activities, sortBy, sortOrder) => {
    return [...activities].sort((a, b) => {
      let valueA, valueB;

      if (sortBy === 'date') {
        valueA = new Date(a.date);
        valueB = new Date(b.date);
      } else if (sortBy === 'title') {
        // For title sorting, use title if available, otherwise use description
        valueA = a.title || a.description || '';
        valueB = b.title || b.description || '';
      } else if (sortBy === 'type') {
        valueA = a.type;
        valueB = b.type;
      } else if (sortBy === 'createdAt') {
        valueA = new Date(a.createdAt);
        valueB = new Date(b.createdAt);
      } else {
        valueA = a[sortBy];
        valueB = b[sortBy];
      }

      // Handle null or undefined values
      if (valueA === null || valueA === undefined) return sortOrder === 'asc' ? -1 : 1;
      if (valueB === null || valueB === undefined) return sortOrder === 'asc' ? 1 : -1;

      // Compare dates
      if (valueA instanceof Date && valueB instanceof Date) {
        return sortOrder === 'asc' ? valueA - valueB : valueB - valueA;
      }

      // Compare strings
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return sortOrder === 'asc'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      }

      // Compare other values
      return sortOrder === 'asc' ? valueA - valueB : valueB - valueA;
    });
  };

  // Fetch activities when component mounts or filters change (except search)
  useEffect(() => {
    console.log('useEffect triggered, fetching all activities');
    fetchAllActivities(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contactId, filters.types, filters.sortBy, filters.sortOrder]);

  // Handle search separately to avoid API calls on every keystroke
  useEffect(() => {
    // Only refetch if we already have activities loaded
    if (allActivities.length > 0 && !loading) {
      console.log('Search term changed, filtering existing activities');
      fetchAllActivities(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters.searchTerm]);

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleSearch = (searchTerm) => {
    console.log('Search term changed:', searchTerm);
    setFilters(prev => ({
      ...prev,
      searchTerm
    }));
  };

  const handleAddActivity = () => {
    setCurrentActivity(null);
    setIsActivityFormOpen(true);
  };

  const handleEditItem = (item) => {
    // For all item types, we'll use the ActivityForm
    setCurrentActivity(item);
    setIsActivityFormOpen(true);
  };

  const handleDeleteItem = async (item) => {
    if (!window.confirm(`Are you sure you want to delete this ${item.originalType}?`)) {
      return;
    }

    try {
      let response;

      if (item.originalType === 'activity') {
        // Delete regular activity
        response = await fetch(`/api/contacts/${contactId}/activities?activityId=${item.id}`, {
          method: 'DELETE'
        });
      } else if (item.originalType === 'task') {
        // Delete task record
        response = await fetch(`/api/tasks/${item.id}`, {
          method: 'DELETE'
        });

        // Also try to delete any associated activity records
        // This is a best-effort approach - we don't want to fail if this fails
        try {
          // We don't have a direct way to find the activity record for this task,
          // so we'll just refresh the activities list after deletion
          console.log('Task deleted, will refresh activities list');
        } catch (activityError) {
          console.error('Error deleting associated activity record:', activityError);
        }
      } else if (item.originalType === 'note') {
        // Delete note record
        response = await fetch(`/api/notes/${item.id}`, {
          method: 'DELETE'
        });

        // Also try to delete any associated activity records
        // This is a best-effort approach - we don't want to fail if this fails
        try {
          // We don't have a direct way to find the activity record for this note,
          // so we'll just refresh the activities list after deletion
          console.log('Note deleted, will refresh activities list');
        } catch (activityError) {
          console.error('Error deleting associated activity record:', activityError);
        }
      }

      if (!response.ok) {
        throw new Error(`Failed to delete ${item.originalType}`);
      }

      // Remove the deleted item from the state
      setAllActivities(allActivities.filter(activity =>
        !(activity.id === item.id && activity.originalType === item.originalType)
      ));

      // Update pagination total count
      setPagination(prev => {
        const newTotal = Math.max(0, prev.total - 1);
        return {
          ...prev,
          total: newTotal,
          totalPages: Math.ceil(newTotal / prev.pageSize)
        };
      });

      // Refresh the activities list to ensure consistency
      fetchAllActivities(pagination.page);

      // Notify parent component that an activity has changed
      if (onActivityChange) onActivityChange();
    } catch (error) {
      console.error(`Failed to delete ${item.originalType}:`, error);
      alert(`Failed to delete ${item.originalType}. Please try again.`);
    }
  };

  const handleItemSaved = (savedItem, itemType) => {
    // Refresh the entire list to ensure consistency
    fetchAllActivities(pagination.page);

    // Notify parent component that an activity has changed
    if (onActivityChange) onActivityChange();
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      fetchAllActivities(newPage);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow h-full overflow-auto">
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Activity Timeline</h3>
          <div className="flex items-center gap-2">
            {/* Admin-only button to manage activity types */}
            {user && hasRole(user, ROLE_ADMIN) && (
              <button
                onClick={() => setIsActivityTypeManagerOpen(true)}
                className="px-3 py-1.5 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center gap-1"
              >
                <Settings size={14} /> Manage Types
              </button>
            )}
            <button
              onClick={handleAddActivity}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
            >
              Add Activity
            </button>
          </div>
        </div>

        {error && (
          <div className="p-3 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md mb-4">
            {error}
          </div>
        )}

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <SearchInput
            placeholder="Search activities, tasks, and notes..."
            onSearch={handleSearch}
            initialValue={filters.searchTerm}
          />

          <div className="flex items-center gap-2">
            <ActivityFilter onFilterChange={handleFilterChange} />

            <div className="text-sm font-medium">
              <span className="text-primary">{allActivities.length}</span> <span className="text-gray-600 dark:text-gray-300">items found</span>
              {filters.searchTerm && (
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                  {allActivities.length === 0 ? '(no matches)' : ''}
                </span>
              )}
            </div>
          </div>
        </div>

        {loading ? (
          <div className="py-10 text-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent">
              <span className="sr-only">Loading...</span>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <ActivityTimeline
              activities={allActivities}
              onEdit={handleEditItem}
              onDelete={handleDeleteItem}
            />

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex justify-center space-x-2 pt-4">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                  className="px-3 py-1 rounded-md border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 disabled:opacity-50"
                >
                  Previous
                </button>

                <span className="px-3 py-1 text-gray-700 dark:text-gray-300">
                  Page {pagination.page} of {pagination.totalPages}
                </span>

                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.totalPages}
                  className="px-3 py-1 rounded-md border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            )}
          </div>
        )}

        {/* Single form for adding/editing all types of activities */}
        <ActivityForm
          contactId={contactId}
          activity={currentActivity}
          isOpen={isActivityFormOpen}
          onClose={() => setIsActivityFormOpen(false)}
          onSuccess={(savedItem) => handleItemSaved(savedItem, savedItem.type || 'activity')}
        />

        {/* Activity Type Manager Dialog */}
        <Dialog.Root open={isActivityTypeManagerOpen} onOpenChange={setIsActivityTypeManagerOpen}>
          <Dialog.Portal>
            <Dialog.Overlay className="fixed inset-0 bg-black/50 z-[100]" />
            <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-4 w-full max-w-4xl z-[110] max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <Dialog.Title className="text-xl font-semibold text-gray-900 dark:text-white">
                  Manage Activity Types
                </Dialog.Title>
                <Dialog.Close className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400">
                  <X size={20} />
                </Dialog.Close>
              </div>

              <ActivityTypeManager />
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>
      </div>
    </div>
  );
}
