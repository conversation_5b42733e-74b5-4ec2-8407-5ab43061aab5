'use client';

import { useState, useEffect, useRef } from 'react';
import { draggable, dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import { setCustomNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview';
import { pointerOutsideOfPreview } from '@atlaskit/pragmatic-drag-and-drop/element/pointer-outside-of-preview';
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine';
import ReactDOM from 'react-dom';
import Switch from '@/components/ui/Switch';
import { Loader2 } from 'lucide-react';

// Define the state types for the sortable item
const ItemState = {
  IDLE: 'idle',
  DRAGGING: 'dragging',
  DRAG_OVER_TOP: 'drag-over-top',
  DRAG_OVER_BOTTOM: 'drag-over-bottom'
};

// Sortable item component for the column list
export default function PragmaticSortableItem({
  id,
  field,
  isChecked,
  onToggle,
  disabled,
  index,
  onReorder,
  isHiddenInDialog,
  isLoadingDialogPreferences
}) {
  const itemRef = useRef(null);
  const dragHandleRef = useRef(null);
  const [state, setState] = useState(ItemState.IDLE);
  const [previewContainer, setPreviewContainer] = useState(null);

  // Set up draggable
  useEffect(() => {
    if (!itemRef.current || !dragHandleRef.current || disabled) return;

    console.log(`Setting up draggable for item ${id} at index ${index}`);

    // Create the draggable
    const draggableCleanup = draggable({
      element: itemRef.current,
      dragHandle: dragHandleRef.current,
      getInitialData: () => ({ id, index, type: 'column-item' }),
      onGenerateDragPreview({ nativeSetDragImage }) {
        setCustomNativeDragPreview({
          nativeSetDragImage,
          getOffset: pointerOutsideOfPreview({
            x: 16,
            y: 8,
          }),
          render({ container }) {
            setPreviewContainer(container);
            return () => setState(ItemState.DRAGGING);
          },
        });
      },
      onDragStart: (e) => {
        console.log(`Started dragging item ${id}`);
        setState(ItemState.DRAGGING);

        // Set data for HTML5 drag and drop (for the empty drop target)
        if (e.nativeEvent && e.nativeEvent.dataTransfer) {
          e.nativeEvent.dataTransfer.setData('text/plain', JSON.stringify({ id, index }));
        }

        // Add a class to the body to indicate dragging is in progress
        document.body.classList.add('is-dragging-column');
      },
      onDrop: () => {
        console.log(`Dropped item ${id}`);
        setState(ItemState.IDLE);
        setPreviewContainer(null);
        // Remove the class from the body
        document.body.classList.remove('is-dragging-column');
      }
    });

    // Create the drop target
    const dropTargetCleanup = dropTargetForElements({
      element: itemRef.current,
      getIsSticky: () => true,
      canDrop: ({ source }) => {
        // Don't allow dropping on yourself
        if (source.element === itemRef.current) {
          return false;
        }
        // Only allow column items to be dropped
        return source.data?.type === 'column-item';
      },
      getData: () => ({ id, index, type: 'column-item' }),
      onDragEnter: ({ source, location }) => {
        if (source.element === itemRef.current) return;
        console.log(`Drag entered item ${id}`);

        // Get the mouse position from the location
        const mouseY = location.current.input.clientY;

        // Determine if we're over the top or bottom half
        const rect = itemRef.current.getBoundingClientRect();
        const position = mouseY < (rect.top + rect.height / 2) ? 'top' : 'bottom';

        setState(position === 'top' ? ItemState.DRAG_OVER_TOP : ItemState.DRAG_OVER_BOTTOM);
      },
      onDrag: ({ source, location }) => {
        if (source.element === itemRef.current) return;

        // Get the mouse position from the location
        const mouseY = location.current.input.clientY;

        // Determine if we're over the top or bottom half
        const rect = itemRef.current.getBoundingClientRect();
        const position = mouseY < (rect.top + rect.height / 2) ? 'top' : 'bottom';

        setState(position === 'top' ? ItemState.DRAG_OVER_TOP : ItemState.DRAG_OVER_BOTTOM);
      },
      onDragLeave: () => {
        console.log(`Drag left item ${id}`);
        setState(ItemState.IDLE);
      },
      onDrop: ({ source, location }) => {
        console.log(`Drop on item ${id}`);

        // Get the source data
        const sourceId = source.data?.id;
        const sourceIndex = source.data?.index;

        if (sourceId === undefined || sourceIndex === undefined) {
          console.log('Missing source data', source.data);
          setState(ItemState.IDLE);
          return;
        }

        // Get the mouse position from the location
        const mouseY = location.current.input.clientY;

        // Determine if we're over the top or bottom half
        const rect = itemRef.current.getBoundingClientRect();
        const position = mouseY < (rect.top + rect.height / 2) ? 'top' : 'bottom';

        console.log(`Dropping ${sourceId} (${sourceIndex}) onto ${id} (${index}) at ${position}`);

        // Call the parent's onReorder handler
        if (onReorder) {
          onReorder(sourceId, sourceIndex, id, index, position);
        }

        setState(ItemState.IDLE);
      }
    });

    // Combine the cleanups
    return () => {
      console.log(`Cleaning up draggable for item ${id}`);
      if (typeof draggableCleanup === 'function') {
        draggableCleanup();
      } else if (draggableCleanup && typeof draggableCleanup.cleanup === 'function') {
        draggableCleanup.cleanup();
      }

      if (typeof dropTargetCleanup === 'function') {
        dropTargetCleanup();
      } else if (dropTargetCleanup && typeof dropTargetCleanup.cleanup === 'function') {
        dropTargetCleanup.cleanup();
      }
    };
  }, [id, index, disabled, onReorder]);

  // Determine if we should show a drop indicator and where
  const showTopIndicator = state === ItemState.DRAG_OVER_TOP;
  const showBottomIndicator = state === ItemState.DRAG_OVER_BOTTOM;
  const isDragging = state === ItemState.DRAGGING;

  return (
    <div className="relative">
      <div
        ref={itemRef}
        data-id={id}
        data-index={index}
        data-field-id={id}
        className={`column-list-item ${isDragging ? 'column-list-item-dragging' : ''}`}
      >
        <div className="column-list-item-content">
          <div
            ref={dragHandleRef}
            className="drag-handle"
            title="Drag to reorder"
            aria-label={`Reorder ${field.label}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor" stroke="none">
              <circle cx="9" cy="5" r="1.5"></circle>
              <circle cx="9" cy="12" r="1.5"></circle>
              <circle cx="9" cy="19" r="1.5"></circle>
              <circle cx="15" cy="5" r="1.5"></circle>
              <circle cx="15" cy="12" r="1.5"></circle>
              <circle cx="15" cy="19" r="1.5"></circle>
            </svg>
          </div>

          <Switch
            id={`field-${id}`}
            checked={isChecked}
            onChange={() => onToggle(id)}
            disabled={disabled}
            size="sm"
          />
          <span className="ml-2 block text-sm text-gray-700 dark:text-gray-300 flex-1">
            {field.label}
            {field.isDefault && (
              <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">(Default)</span>
            )}
            {isLoadingDialogPreferences ? (
              <span className="ml-2 text-xs text-gray-500 dark:text-gray-400 inline-flex items-center">
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Loading...
              </span>
            ) : isHiddenInDialog ? (
              <span className="ml-2 text-xs text-amber-500 dark:text-amber-400 inline-flex items-center">
                (Hidden in Add/Edit)
              </span>
            ) : null}
          </span>
        </div>
      </div>

      {/* Drop indicators */}
      {showTopIndicator && (
        <div className="drop-indicator top" />
      )}
      {showBottomIndicator && (
        <div className="drop-indicator bottom" />
      )}

      {/* Drag preview */}
      {previewContainer && ReactDOM.createPortal(
        <div className="column-drag-preview">
          {field.label}
        </div>,
        previewContainer
      )}
    </div>
  );
}
