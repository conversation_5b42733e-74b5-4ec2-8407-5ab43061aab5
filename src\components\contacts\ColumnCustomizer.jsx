'use client';

import { useState, useEffect, useRef } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { CONTACT_FIELDS, DEFAULT_FIELDS } from '@/lib/constants/contactFields';
import PragmaticSortableItem from './PragmaticSortableItem';
import { fetchDialogFieldPreferences } from '@/lib/utils/dialogPreferences';
import { Loader2 } from 'lucide-react';
// Import custom styles
import './column-customizer.css';

// Initialize mouse position tracking
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  window.lastMouseX = 0;
  window.lastMouseY = 0;

  if (!window.mouseMoveListenerAdded) {
    window.mouseMoveListenerAdded = true;
    document.addEventListener('mousemove', (e) => {
      window.lastMouseX = e.clientX;
      window.lastMouseY = e.clientY;
    });
    console.log('Added mouse position tracking');
  }
}



export default function ColumnCustomizer({ selectedFields, onFieldsChange }) {
  const [isOpen, setIsOpen] = useState(false);
  const [localSelectedFields, setLocalSelectedFields] = useState(selectedFields);
  const [orderedFields, setOrderedFields] = useState([]);
  const [error, setError] = useState(''); // For displaying validation errors
  const [dialogVisibleFields, setDialogVisibleFields] = useState(null);
  const [isLoadingPreferences, setIsLoadingPreferences] = useState(false);
  const containerRef = useRef(null);

  // Dialog key for contact add/edit dialog
  const dialogKey = 'contact-add-edit';
  // Helper function to validate field IDs
  const validateFieldIds = (fieldIds) => {
    return fieldIds.filter(id => CONTACT_FIELDS.some(field => field.id === id));
  };

  // Handle item drop
  const handleItemDrop = (sourceId, sourceIndex, targetId, targetIndex, position) => {
    console.log(`Handling drop: ${sourceId} (${sourceIndex}) -> ${targetId} (${targetIndex}) at ${position}`);

    // Get the source item
    const sourceItem = orderedFields[sourceIndex];
    if (!sourceItem) {
      console.error('Source item not found');
      return;
    }

    // Create a new array without the source item
    const newItems = orderedFields.filter((_, index) => index !== sourceIndex);

    // Special case for dropping at the beginning of the list
    if (targetIndex === 0 && position === 'top') {
      // Insert at the beginning
      newItems.unshift(sourceItem);
      setOrderedFields(newItems);
      console.log(`Successfully moved item from index ${sourceIndex} to the beginning of the list`);
      return;
    }

    // Special case for dropping at the end of the list
    if (targetIndex === orderedFields.length - 1 && position === 'bottom') {
      // Insert at the end
      newItems.push(sourceItem);
      setOrderedFields(newItems);
      console.log(`Successfully moved item from index ${sourceIndex} to the end of the list`);
      return;
    }

    // Calculate the actual insert index
    let insertIndex = targetIndex;
    if (position === 'bottom') {
      insertIndex += 1;
    }

    // Adjust for the fact that we're removing the source item first
    if (sourceIndex < insertIndex) {
      insertIndex -= 1;
    }

    // Insert the item at the calculated position
    newItems.splice(insertIndex, 0, sourceItem);

    // Update the state with the new order
    setOrderedFields(newItems);

    console.log(`Successfully moved item from index ${sourceIndex} to index ${insertIndex}`);
  };

  // Fetch dialog field preferences when dialog opens
  useEffect(() => {
    if (isOpen) {
      fetchDialogPreferences();
    }
  }, [isOpen]);

  // Function to fetch dialog field preferences
  const fetchDialogPreferences = async () => {
    try {
      setIsLoadingPreferences(true);
      const visibleFields = await fetchDialogFieldPreferences(dialogKey);
      setDialogVisibleFields(visibleFields);
    } catch (error) {
      console.error('Failed to fetch dialog preferences:', error);
    } finally {
      setIsLoadingPreferences(false);
    }
  };

  // Initialize ordered fields when component mounts or selectedFields change
  useEffect(() => {
    // Validate selected fields
    const validSelectedFields = validateFieldIds(selectedFields);
    setLocalSelectedFields(validSelectedFields);

    // Get all field IDs from CONTACT_FIELDS
    const allFieldIds = CONTACT_FIELDS.map(field => field.id);

    // Create an ordered list of all fields, with selected fields first in their current order
    const selected = [...validSelectedFields];

    // Get unselected fields (all fields that are not in the selected fields)
    const unselected = allFieldIds.filter(id => !validSelectedFields.includes(id));

    // Set the ordered fields with selected fields first, then unselected fields
    setOrderedFields([...selected, ...unselected]);

    console.log('All fields count:', CONTACT_FIELDS.length);
    console.log('Selected fields count:', selected.length);
    console.log('Unselected fields count:', unselected.length);
    console.log('Total ordered fields count:', selected.length + unselected.length);

    // Debug: List all field IDs and labels
    console.log('All fields:', CONTACT_FIELDS.map(f => `${f.id}: ${f.label}`));
  }, [selectedFields]);



  // Set up container drag over handler for visual feedback
  useEffect(() => {
    if (!containerRef.current) return;

    const handleContainerDragOver = (e) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'move';
      containerRef.current.classList.add('bg-gray-50', 'dark:bg-gray-700/50');
    };

    const handleContainerDragLeave = () => {
      containerRef.current.classList.remove('bg-gray-50', 'dark:bg-gray-700/50');
    };

    const handleContainerDrop = (e) => {
      e.preventDefault();
      containerRef.current.classList.remove('bg-gray-50', 'dark:bg-gray-700/50');
    };

    // Add event listeners
    containerRef.current.addEventListener('dragover', handleContainerDragOver);
    containerRef.current.addEventListener('dragleave', handleContainerDragLeave);
    containerRef.current.addEventListener('drop', handleContainerDrop);

    // Clean up
    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener('dragover', handleContainerDragOver);
        containerRef.current.removeEventListener('dragleave', handleContainerDragLeave);
        containerRef.current.removeEventListener('drop', handleContainerDrop);
      }
    };
  }, []);

  const handleToggleField = (fieldId) => {
    setLocalSelectedFields(prev => {
      if (prev.includes(fieldId)) {
        // Don't allow removing all fields
        if (prev.length === 1) {
          return prev;
        }
        return prev.filter(id => id !== fieldId);
      } else {
        return [...prev, fieldId];
      }
    });
  };


  const handleSave = () => {
    // Save both the selection and the order
    const orderedSelection = orderedFields.filter(id => {
      // Make sure the ID is in localSelectedFields and is a valid field ID
      return localSelectedFields.includes(id) && CONTACT_FIELDS.some(field => field.id === id);
    });

    // Ensure we have at least one field
    if (orderedSelection.length === 0) {
      setError('No valid fields selected. Using defaults.');
      setTimeout(() => {
        onFieldsChange(DEFAULT_FIELDS);
        setIsOpen(false);
      }, 1500);
      return;
    } else {
      onFieldsChange(orderedSelection);
      setIsOpen(false);
    }
  };

  // Define essential fields that should always remain selected
  const ESSENTIAL_FIELDS = ['firstName', 'lastName', 'type'];

  // Reset to default fields
  const handleReset = () => {
    setLocalSelectedFields(DEFAULT_FIELDS);

    // Reset order to default as well
    const selected = [...DEFAULT_FIELDS];
    const unselected = CONTACT_FIELDS
      .filter(field => !DEFAULT_FIELDS.includes(field.id))
      .map(field => field.id);

    setOrderedFields([...selected, ...unselected]);
  };

  // Toggle all fields on or off
  const handleToggleAll = (selectAll) => {
    if (selectAll) {
      // Select all fields
      setLocalSelectedFields(CONTACT_FIELDS.map(field => field.id));
    } else {
      // Deselect all except essential fields
      setLocalSelectedFields(ESSENTIAL_FIELDS);
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>
      <Dialog.Trigger asChild>
        <button
          className="flex items-center space-x-1 px-3 py-2 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 min-w-[100px] justify-center"
          title="Customize table columns and their order"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          <span>Columns</span>
        </button>
      </Dialog.Trigger>

      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-hidden">
          <Dialog.Title className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Customize Table Columns
          </Dialog.Title>

          <div className="mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Select which columns you want to display in the contacts table.
              <span className="ml-2 text-xs text-gray-500">({orderedFields.length} fields available)</span>
            </p>
            <p className="text-xs text-amber-500 dark:text-amber-400 mt-2">
              Fields marked as "Hidden in Add/Edit" are not visible in the contact add/edit dialogs.
              Consider hiding these columns if you don't need to see them in the table.
            </p>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-200 rounded-md">
              {error}
            </div>
          )}

          <div className="mb-6">
            <div className="mb-2 flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Available Fields ({orderedFields.length})
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                Drag to reorder
              </span>
            </div>
            <div
              ref={containerRef}
              className="h-[350px] column-list-container p-2"
              style={{ overflowY: 'scroll' }}
            >
            <div>
              {/* Render all fields */}
              {orderedFields.map((fieldId, index) => {
                // Find the field definition
                const field = CONTACT_FIELDS.find(f => f.id === fieldId);
                if (!field) {
                  console.warn(`Field with id '${fieldId}' not found in CONTACT_FIELDS`);
                  return null;
                }

                // Check if this field is hidden in the add/edit dialog
                const isHiddenInDialog = dialogVisibleFields && !dialogVisibleFields.includes(fieldId);

                return (
                  <PragmaticSortableItem
                    key={fieldId}
                    id={fieldId}
                    field={field}
                    index={index}
                    isChecked={localSelectedFields.includes(fieldId)}
                    onToggle={handleToggleField}
                    disabled={localSelectedFields.length === 1 && localSelectedFields.includes(fieldId)}
                    onReorder={handleItemDrop}
                    isHiddenInDialog={isHiddenInDialog}
                    isLoadingDialogPreferences={isLoadingPreferences}
                  />
                );
              })}

              {/* Add spacer to ensure scrolling */}
              <div className="h-[50px]"></div>
            </div>
            </div>
          </div>

          <div className="flex justify-between">
            <div className="flex space-x-2">
              {/* Reset to Default button with tooltip */}
              <div className="relative inline-block group">
                <button
                  type="button"
                  onClick={handleReset}
                  className="px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 min-w-[90px]"
                  title="Reset to default columns"
                >
                  Reset Default
                </button>
                <div className="absolute bottom-full left-0 mb-1 w-40 text-xs bg-gray-800 text-white p-2 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
                  Reset to the default column selection
                </div>
              </div>

              {/* Toggle All button with tooltip */}
              <div className="relative inline-block group">
                <button
                  type="button"
                  onClick={() => handleToggleAll(localSelectedFields.length < CONTACT_FIELDS.length)}
                  className="px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 min-w-[90px]"
                  title={localSelectedFields.length < CONTACT_FIELDS.length ? 'Select all fields' : 'Select only essential fields'}
                >
                  {localSelectedFields.length < CONTACT_FIELDS.length ? 'Select All' : 'Essential Only'}
                </button>
                <div className="absolute bottom-full left-0 mb-1 w-48 text-xs bg-gray-800 text-white p-2 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
                  {localSelectedFields.length < CONTACT_FIELDS.length
                    ? 'Select all available fields'
                    : 'Select only essential fields (First Name, Last Name, Type)'}
                </div>
              </div>
            </div>

            <div className="flex space-x-2">
              {/* Cancel button with tooltip */}
              <div className="relative inline-block group">
                <Dialog.Close asChild>
                  <button
                    type="button"
                    className="px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 min-w-[60px]"
                    title="Cancel changes"
                  >
                    Cancel
                  </button>
                </Dialog.Close>
                <div className="absolute bottom-full right-0 mb-1 w-32 text-xs bg-gray-800 text-white p-2 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
                  Cancel without saving changes
                </div>
              </div>

              {/* Save button with tooltip */}
              <div className="relative inline-block group">
                <button
                  type="button"
                  onClick={handleSave}
                  className="px-2 py-1 text-xs bg-primary text-white rounded-md hover:bg-primary-hover min-w-[60px]"
                  title="Save changes"
                >
                  Save
                </button>
                <div className="absolute bottom-full right-0 mb-1 w-32 text-xs bg-gray-800 text-white p-2 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
                  Save column selection
                </div>
              </div>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
