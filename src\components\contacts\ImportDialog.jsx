'use client';

import { useState, useRef } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import <PERSON> from 'papaparse';

export default function ImportDialog({ onImportComplete, trigger }) {
  const [open, setOpen] = useState(false);
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState([]);
  const [importing, setImporting] = useState(false);
  const [error, setError] = useState('');
  const [mappings, setMappings] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    company: '',
    type: ''
  });
  const fileInputRef = useRef(null);

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    setFile(selectedFile);
    setError('');

    if (selectedFile) {
      Papa.parse(selectedFile, {
        header: true,
        preview: 3, // Parse only first 3 rows for preview
        complete: (results) => {
          if (results.data && results.data.length > 0) {
            setPreview(results.data);
            
            // Try to auto-map columns based on header names
            const headers = results.meta.fields || [];
            const newMappings = { ...mappings };
            
            headers.forEach(header => {
              const lowerHeader = header.toLowerCase();
              if (lowerHeader.includes('first') && lowerHeader.includes('name')) {
                newMappings.firstName = header;
              } else if (lowerHeader.includes('last') && lowerHeader.includes('name')) {
                newMappings.lastName = header;
              } else if (lowerHeader.includes('email')) {
                newMappings.email = header;
              } else if (lowerHeader.includes('phone')) {
                newMappings.phone = header;
              } else if (lowerHeader.includes('company')) {
                newMappings.company = header;
              } else if (lowerHeader.includes('type')) {
                newMappings.type = header;
              }
            });
            
            setMappings(newMappings);
          }
        },
        error: (error) => {
          setError(`Error parsing CSV: ${error.message}`);
        }
      });
    }
  };

  const handleImport = async () => {
    if (!file) {
      setError('Please select a file to import');
      return;
    }

    // Check if at least first name or last name is mapped
    if (!mappings.firstName && !mappings.lastName) {
      setError('You must map at least first name or last name');
      return;
    }

    setImporting(true);
    setError('');

    try {
      // Parse the entire file
      Papa.parse(file, {
        header: true,
        complete: async (results) => {
          if (results.data && results.data.length > 0) {
            // Map the data according to the mappings
            const mappedData = results.data.map(row => {
              const contact = {};
              Object.entries(mappings).forEach(([field, header]) => {
                if (header && row[header] !== undefined) {
                  contact[field] = row[header];
                }
              });
              return contact;
            }).filter(contact => 
              // Filter out empty contacts or those without at least a name
              (contact.firstName && contact.firstName.trim()) || 
              (contact.lastName && contact.lastName.trim())
            );

            if (mappedData.length === 0) {
              setError('No valid contacts found in the file');
              setImporting(false);
              return;
            }

            // Send the data to the server
            const response = await fetch('/api/contacts/import', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ contacts: mappedData }),
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.error || 'Failed to import contacts');
            }

            const result = await response.json();
            
            // Close the dialog and notify parent
            setOpen(false);
            onImportComplete(result);
            
            // Reset the form
            setFile(null);
            setPreview([]);
            if (fileInputRef.current) {
              fileInputRef.current.value = '';
            }
          }
        },
        error: (error) => {
          throw new Error(`Error parsing CSV: ${error.message}`);
        }
      });
    } catch (error) {
      setError(error.message);
    } finally {
      setImporting(false);
    }
  };

  const handleMappingChange = (field, value) => {
    setMappings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog.Root open={open} onOpenChange={setOpen}>
      <Dialog.Trigger asChild>
        {trigger}
      </Dialog.Trigger>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-[100]" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl z-[110] max-h-[90vh] overflow-y-auto">
          <Dialog.Title className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Import Contacts from CSV</Dialog.Title>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Select CSV File
              </label>
              <input
                type="file"
                accept=".csv"
                onChange={handleFileChange}
                ref={fileInputRef}
                className="block w-full text-sm text-gray-500 dark:text-gray-400
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-medium
                  file:bg-primary file:text-white
                  hover:file:bg-primary-hover"
              />
            </div>

            {error && (
              <div className="p-3 bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 rounded-md">
                {error}
              </div>
            )}

            {preview.length > 0 && (
              <div>
                <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Preview</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        {Object.keys(preview[0]).map((header, index) => (
                          <th key={index} className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {preview.map((row, rowIndex) => (
                        <tr key={rowIndex}>
                          {Object.values(row).map((cell, cellIndex) => (
                            <td key={cellIndex} className="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {cell}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {preview.length > 0 && (
              <div>
                <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Map Fields</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                  Match the CSV columns to contact fields. At least first name or last name is required.
                </p>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      First Name
                    </label>
                    <select
                      value={mappings.firstName}
                      onChange={(e) => handleMappingChange('firstName', e.target.value)}
                      className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">-- Select Column --</option>
                      {Object.keys(preview[0]).map((header, index) => (
                        <option key={index} value={header}>{header}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Last Name
                    </label>
                    <select
                      value={mappings.lastName}
                      onChange={(e) => handleMappingChange('lastName', e.target.value)}
                      className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">-- Select Column --</option>
                      {Object.keys(preview[0]).map((header, index) => (
                        <option key={index} value={header}>{header}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Email
                    </label>
                    <select
                      value={mappings.email}
                      onChange={(e) => handleMappingChange('email', e.target.value)}
                      className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">-- Select Column --</option>
                      {Object.keys(preview[0]).map((header, index) => (
                        <option key={index} value={header}>{header}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Phone
                    </label>
                    <select
                      value={mappings.phone}
                      onChange={(e) => handleMappingChange('phone', e.target.value)}
                      className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">-- Select Column --</option>
                      {Object.keys(preview[0]).map((header, index) => (
                        <option key={index} value={header}>{header}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Company
                    </label>
                    <select
                      value={mappings.company}
                      onChange={(e) => handleMappingChange('company', e.target.value)}
                      className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">-- Select Column --</option>
                      {Object.keys(preview[0]).map((header, index) => (
                        <option key={index} value={header}>{header}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Type
                    </label>
                    <select
                      value={mappings.type}
                      onChange={(e) => handleMappingChange('type', e.target.value)}
                      className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">-- Select Column --</option>
                      {Object.keys(preview[0]).map((header, index) => (
                        <option key={index} value={header}>{header}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3 mt-6">
              <Dialog.Close asChild>
                <button 
                  type="button" 
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-lg"
                >
                  Cancel
                </button>
              </Dialog.Close>
              <button
                type="button"
                onClick={handleImport}
                disabled={importing || !file}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {importing ? 'Importing...' : 'Import Contacts'}
              </button>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
