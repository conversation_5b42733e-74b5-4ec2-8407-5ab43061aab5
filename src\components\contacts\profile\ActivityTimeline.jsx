'use client';

import React, { useState, useEffect } from 'react';
import * as LucideIcons from 'lucide-react';
// Keep the import for backward compatibility
import { getActivityTypeById, getActivityTypeColor, getActivityIconBgColor } from '@/lib/constants/activityTypes';

// Helper function to format dates consistently with timezone handling
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    timeZone: 'UTC' // Ensure the date is interpreted as UTC
  });
};

// Helper function to format date and time
const formatDateTime = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString(undefined, {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'UTC' // Ensure the date is interpreted as UTC
  });
};

export default function ActivityTimeline({ activities, onEdit, onDelete }) {
  const [expandedActivity, setExpandedActivity] = useState(null);
  const [activityTypes, setActivityTypes] = useState([]);

  // Fetch activity types from API
  useEffect(() => {
    const fetchActivityTypes = async () => {
      try {
        console.log('ActivityTimeline: Fetching activity types from /api/activity-types');
        const response = await fetch('/api/activity-types');
        console.log('ActivityTimeline: API response status:', response.status);
        if (response.ok) {
          const types = await response.json();
          console.log('ActivityTimeline: Fetched activity types from API:', types);
          console.log('ActivityTimeline: System types:', types.filter(t => t.isSystem));
          console.log('ActivityTimeline: Non-system types:', types.filter(t => !t.isSystem));
          setActivityTypes(types);
        } else {
          // Fallback to hardcoded types - include all types from seed data
          setActivityTypes([
            {
              id: 'email',
              name: 'Email',
              icon: 'Mail',
              color: '#3b82f6',
              description: 'Email communication with contact',
              isSystem: true,
              isActive: true
            },
            {
              id: 'call',
              name: 'Call',
              icon: 'Phone',
              color: '#10b981',
              description: 'Phone call with contact',
              isSystem: true,
              isActive: true
            },
            {
              id: 'meeting',
              name: 'Meeting',
              icon: 'Users',
              color: '#6366f1',
              description: 'In-person or virtual meeting',
              isSystem: true,
              isActive: true
            },
            {
              id: 'note',
              name: 'Note',
              icon: 'FileText',
              color: '#f59e0b',
              description: 'General note or comment',
              isSystem: true,
              isActive: true
            },
            {
              id: 'follow-up',
              name: 'Follow-up',
              icon: 'Redo',
              color: '#ef4444',
              description: 'Follow-up activity',
              isSystem: false,
              isActive: true
            },
            {
              id: 'demo',
              name: 'Demo',
              icon: 'Presentation',
              color: '#ec4899',
              description: 'Product or service demonstration',
              isSystem: false,
              isActive: true
            },
            {
              id: 'proposal',
              name: 'Proposal',
              icon: 'FileCheck',
              color: '#f97316',
              description: 'Proposal or quote sent to contact',
              isSystem: false,
              isActive: true
            }
          ]);
        }
      } catch (error) {
        console.error('Error fetching activity types:', error);
        // Fallback to hardcoded types - include all types from seed data
        setActivityTypes([
          {
            id: 'email',
            name: 'Email',
            icon: 'Mail',
            color: '#3b82f6',
            description: 'Email communication with contact',
            isSystem: true,
            isActive: true
          },
          {
            id: 'call',
            name: 'Call',
            icon: 'Phone',
            color: '#10b981',
            description: 'Phone call with contact',
            isSystem: true,
            isActive: true
          },
          {
            id: 'meeting',
            name: 'Meeting',
            icon: 'Users',
            color: '#6366f1',
            description: 'In-person or virtual meeting',
            isSystem: true,
            isActive: true
          },
          {
            id: 'note',
            name: 'Note',
            icon: 'FileText',
            color: '#f59e0b',
            description: 'General note or comment',
            isSystem: true,
            isActive: true
          },
          {
            id: 'follow-up',
            name: 'Follow-up',
            icon: 'Redo',
            color: '#ef4444',
            description: 'Follow-up activity',
            isSystem: false,
            isActive: true
          },
          {
            id: 'demo',
            name: 'Demo',
            icon: 'Presentation',
            color: '#ec4899',
            description: 'Product or service demonstration',
            isSystem: false,
            isActive: true
          },
          {
            id: 'proposal',
            name: 'Proposal',
            icon: 'FileCheck',
            color: '#f97316',
            description: 'Proposal or quote sent to contact',
            isSystem: false,
            isActive: true
          }
        ]);
      }
    };

    fetchActivityTypes();
  }, []);

  // Get activity type information based on the item type
  const getItemTypeInfo = (item) => {
    // For regular activities, try to find the type in our fetched types
    if (item.originalType === 'activity') {
      console.log(`ActivityTimeline: Looking for activity type "${item.type}" for activity:`, item);

      // First try exact ID match
      let foundType = activityTypes.find(t => t.id === item.type);

      // If not found by ID, try matching by name (case-insensitive)
      if (!foundType) {
        foundType = activityTypes.find(t => t.name.toLowerCase() === item.type?.toLowerCase());
      }

      if (foundType) {
        console.log(`ActivityTimeline: Found admin type for "${item.type}":`, foundType);
        return {
          id: foundType.id,
          label: foundType.name,
          icon: foundType.icon,
          color: foundType.color,
          description: foundType.description
        };
      }

      // If not found in admin types, return a default with admin-style structure
      console.warn(`ActivityTimeline: Activity type "${item.type}" not found in admin activity types.`);
      console.warn('Available admin types:', activityTypes.map(t => ({ id: t.id, name: t.name, isSystem: t.isSystem })));
      return {
        id: item.type || 'unknown',
        label: item.type ? item.type.charAt(0).toUpperCase() + item.type.slice(1) : 'Unknown',
        icon: 'Activity', // Default Lucide icon
        color: '#6b7280', // gray
        description: 'Activity type not configured in admin settings'
      };
    }

    // For tasks
    if (item.originalType === 'task') {
      return {
        id: 'task',
        label: item.completed ? 'Completed Task' : 'Task',
        icon: 'CheckSquare',
        color: '#8b5cf6', // purple
        description: 'Task or to-do item'
      };
    }

    // For notes
    if (item.originalType === 'note') {
      return {
        id: 'note',
        label: 'Note',
        icon: 'FileText',
        color: '#f59e0b', // amber
        description: 'General note or comment'
      };
    }

    // Default fallback
    return {
      id: 'unknown',
      label: 'Unknown',
      icon: 'Activity',
      color: '#6b7280',
      description: 'Unknown activity type'
    };
  };

  // Get color for the activity type badge
  const getItemTypeColor = (item) => {
    if (item.originalType === 'task') {
      return item.completed
        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
        : 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
    }

    if (item.originalType === 'note') {
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
    }

    // For regular activities, try to find the type in our fetched types
    if (item.originalType === 'activity') {
      // First try exact ID match
      let foundType = activityTypes.find(t => t.id === item.type);

      // If not found by ID, try matching by name (case-insensitive)
      if (!foundType) {
        foundType = activityTypes.find(t => t.name.toLowerCase() === item.type?.toLowerCase());
      }

      if (foundType && foundType.color) {
        // Return null to indicate we should use inline styles
        return null;
      }
    }

    // Default gray color for unknown types
    return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  };

  // Get inline style for activity type badge
  const getItemTypeStyle = (item) => {
    if (item.originalType === 'activity') {
      // First try exact ID match
      let foundType = activityTypes.find(t => t.id === item.type);

      // If not found by ID, try matching by name (case-insensitive)
      if (!foundType) {
        foundType = activityTypes.find(t => t.name.toLowerCase() === item.type?.toLowerCase());
      }

      if (foundType && foundType.color) {
        // Convert hex to RGB for transparency
        const hex = foundType.color.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        return {
          backgroundColor: `rgba(${r}, ${g}, ${b}, 0.1)`,
          color: foundType.color,
          borderColor: `rgba(${r}, ${g}, ${b}, 0.2)`
        };
      }
    }
    return {};
  };

  // Get background color for the icon
  const getItemIconBgColor = (item) => {
    if (item.originalType === 'task') {
      return item.completed
        ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400'
        : 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400';
    }

    if (item.originalType === 'note') {
      return 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400';
    }

    // For regular activities, try to find the type in our fetched types
    if (item.originalType === 'activity') {
      // First try exact ID match
      let foundType = activityTypes.find(t => t.id === item.type);

      // If not found by ID, try matching by name (case-insensitive)
      if (!foundType) {
        foundType = activityTypes.find(t => t.name.toLowerCase() === item.type?.toLowerCase());
      }

      if (foundType && foundType.color) {
        // Return null to indicate we should use inline styles
        return null;
      }
    }

    // Default gray color for unknown types
    return 'bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400';
  };

  // Get inline style for activity type icon
  const getItemIconStyle = (item) => {
    if (item.originalType === 'activity') {
      // First try exact ID match
      let foundType = activityTypes.find(t => t.id === item.type);

      // If not found by ID, try matching by name (case-insensitive)
      if (!foundType) {
        foundType = activityTypes.find(t => t.name.toLowerCase() === item.type?.toLowerCase());
      }

      if (foundType && foundType.color) {
        // Convert hex to RGB for transparency
        const hex = foundType.color.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        return {
          backgroundColor: `rgba(${r}, ${g}, ${b}, 0.1)`,
          color: foundType.color
        };
      }
    }
    return {};
  };

  const toggleExpand = (activityId) => {
    if (expandedActivity === activityId) {
      setExpandedActivity(null);
    } else {
      setExpandedActivity(activityId);
    }
  };

  if (!activities || activities.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        No activities found. Add your first activity to get started.
      </div>
    );
  }

  return (
    <div className="flow-root">
      <ul className="-mb-4">
        {activities.map((activity, index) => {
          const itemType = getItemTypeInfo(activity);
          const isExpanded = expandedActivity === activity.id;

          // Get title for display
          const title = activity.title || (activity.originalType === 'task' ? activity.title : '');

          return (
            <li key={`${activity.originalType}-${activity.id}`}>
              <div className="relative pb-4 pt-1">
                {index < activities.length - 1 && (
                  <span className="absolute top-12 left-5 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700" />
                )}
                <div className="relative flex items-start space-x-3 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm mb-2">
                  <div className="relative">
                    <div
                      className={`h-10 w-10 rounded-full ring-4 ring-white dark:ring-gray-800 flex items-center justify-center ${getItemIconBgColor(activity) || ''}`}
                      style={getItemIconStyle(activity)}
                    >
                      {typeof itemType.icon === 'string' && LucideIcons[itemType.icon] ? (
                        React.createElement(LucideIcons[itemType.icon], { size: 20 })
                      ) : (
                        <span className="text-lg">{itemType.icon}</span>
                      )}
                    </div>
                  </div>
                  <div className="min-w-0 flex-1 py-1">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getItemTypeColor(activity) || ''}`}
                          style={getItemTypeStyle(activity)}
                        >
                          {itemType.label}
                        </span>
                        <span className="ml-2">{formatDate(activity.date)}</span>
                      </div>
                      <div className="flex flex-wrap gap-2 ml-auto">
                        <button
                          onClick={() => onEdit(activity)}
                          className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 p-1"
                          title={`Edit ${activity.originalType}`}
                          aria-label={`Edit ${activity.originalType}`}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => onDelete(activity)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-1"
                          title={`Delete ${activity.originalType}`}
                          aria-label={`Delete ${activity.originalType}`}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Show title if available (for tasks and notes) */}
                    {title && (
                      <div className="text-base font-medium text-gray-900 dark:text-white mb-2">
                        {title}
                      </div>
                    )}

                    {/* Show description/content */}
                    <div className={`mt-2 text-sm text-gray-700 dark:text-gray-300`}>
                      {isExpanded
                        ? activity.description
                        : activity.description && activity.description.length > 150
                          ? `${activity.description.substring(0, 150)}...`
                          : activity.description
                      }
                    </div>

                    {/* Show task status if applicable */}
                    {activity.originalType === 'task' && (
                      <div className="mt-2 text-sm">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                          activity.completed
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                        }`}>
                          {activity.completed ? 'Completed' : 'Due'}: {formatDate(activity.completed ? activity.completedAt : activity.date)}
                        </span>
                        {activity.priority && (
                          <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                            activity.priority === 'high'
                              ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                              : activity.priority === 'medium'
                                ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
                                : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                          }`}>
                            {activity.priority.charAt(0).toUpperCase() + activity.priority.slice(1)} Priority
                          </span>
                        )}
                      </div>
                    )}

                    {/* Expand/collapse button for long descriptions */}
                    {activity.description && activity.description.length > 150 && (
                      <button
                        onClick={() => toggleExpand(activity.id)}
                        className="mt-2 px-2 py-1 text-sm text-primary hover:text-primary-hover border border-primary/20 hover:border-primary/40 rounded-md transition-colors inline-flex items-center"
                      >
                        {isExpanded ? (
                          <>
                            <span className="mr-1">▲</span> Show less
                          </>
                        ) : (
                          <>
                            <span className="mr-1">▼</span> Read more
                          </>
                        )}
                      </button>
                    )}

                    <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
                      Added {formatDateTime(activity.createdAt)}
                    </div>
                  </div>
                </div>
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
}
