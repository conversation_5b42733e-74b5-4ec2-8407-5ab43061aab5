import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

// GET /api/checklist-items/[id] - Get a specific checklist item
export async function GET(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const itemId = params.id;
    
    const item = await prisma.checklistItem.findUnique({
      where: { id: itemId }
    });
    
    if (!item) {
      return NextResponse.json(
        { error: 'Checklist item not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(item);
  } catch (error) {
    console.error('Failed to fetch checklist item:', error);
    return NextResponse.json(
      { error: 'Failed to fetch checklist item: ' + error.message },
      { status: 500 }
    );
  }
}

// PATCH /api/checklist-items/[id] - Update a checklist item
export async function PATCH(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const itemId = params.id;
    
    const body = await request.json();
    
    // Get the current item to check if completion status is changing
    const currentItem = await prisma.checklistItem.findUnique({
      where: { id: itemId },
      select: { completed: true, checklistId: true }
    });
    
    if (!currentItem) {
      return NextResponse.json(
        { error: 'Checklist item not found' },
        { status: 404 }
      );
    }
    
    // Determine if we need to update completedAt
    const completedAt = body.completed !== undefined
      ? (body.completed ? new Date() : null)
      : undefined;
    
    // Update the checklist item
    const updatedItem = await prisma.checklistItem.update({
      where: { id: itemId },
      data: {
        ...(body.text !== undefined && { text: body.text }),
        ...(body.description !== undefined && { description: body.description }),
        ...(body.completed !== undefined && { completed: body.completed }),
        ...(completedAt !== undefined && { completedAt }),
        ...(body.order !== undefined && { order: body.order }),
        updatedAt: new Date()
      }
    });
    
    // If completion status changed, check if all items are completed
    if (body.completed !== undefined && body.completed !== currentItem.completed) {
      const allItems = await prisma.checklistItem.findMany({
        where: { checklistId: currentItem.checklistId }
      });
      
      const allCompleted = allItems.every(item => item.completed);
      
      // Update the checklist status based on item completion
      await prisma.checklist.update({
        where: { id: currentItem.checklistId },
        data: { 
          status: allCompleted ? 'completed' : 'in_progress',
          updatedAt: new Date()
        }
      });
    }
    
    return NextResponse.json(updatedItem);
  } catch (error) {
    console.error('Failed to update checklist item:', error);
    return NextResponse.json(
      { error: 'Failed to update checklist item: ' + error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/checklist-items/[id] - Delete a checklist item
export async function DELETE(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const itemId = params.id;
    
    // Get the checklist ID before deleting the item
    const item = await prisma.checklistItem.findUnique({
      where: { id: itemId },
      select: { checklistId: true }
    });
    
    if (!item) {
      return NextResponse.json(
        { error: 'Checklist item not found' },
        { status: 404 }
      );
    }
    
    // Delete the checklist item
    await prisma.checklistItem.delete({
      where: { id: itemId }
    });
    
    // Check if there are any items left in the checklist
    const remainingItems = await prisma.checklistItem.findMany({
      where: { checklistId: item.checklistId }
    });
    
    // If there are remaining items, check if they're all completed
    if (remainingItems.length > 0) {
      const allCompleted = remainingItems.every(item => item.completed);
      
      // Update the checklist status based on item completion
      await prisma.checklist.update({
        where: { id: item.checklistId },
        data: { 
          status: allCompleted ? 'completed' : 'in_progress',
          updatedAt: new Date()
        }
      });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete checklist item:', error);
    return NextResponse.json(
      { error: 'Failed to delete checklist item: ' + error.message },
      { status: 500 }
    );
  }
}
