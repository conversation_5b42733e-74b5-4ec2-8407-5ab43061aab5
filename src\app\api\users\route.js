import { getSignedInUser } from "@/lib/auth";

export async function GET(request) {
  const { user } = await getSignedInUser(request);
  const { searchParams } = new URL(request.url);

  const scope = searchParams.get("scope");

  if (scope === "admin") {
    // return all users
    const users = await prisma.user.findMany();
    console.log(5);
    console.log(5);
    console.log(5);
    console.log(5);
    console.log(5);
    console.log(5);
    console.log(5);

    console.log("the users: ", users);

    return Response.json(users);
  } else {
    return Response.json([]);
  }

  console.log(1);
  console.log(1);
  console.log(1);
  console.log(1);
  console.log(1);
  console.log(1);
  console.log(1);
  console.log(1);
  console.log(1);

  console.log("THE USER: ", user);
}
