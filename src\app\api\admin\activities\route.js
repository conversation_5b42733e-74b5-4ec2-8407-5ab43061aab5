import { NextResponse } from 'next/server';
import { getSignedInUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma/client';

// GET /api/admin/activities - Get all activities for admin management
export async function GET(request) {
  try {
    // Check authentication and authorization
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '50');
    const search = searchParams.get('search') || '';
    const activityType = searchParams.get('activityType') || '';
    const contactId = searchParams.get('contactId') || '';
    const startDate = searchParams.get('startDate') || '';
    const endDate = searchParams.get('endDate') || '';
    const sortBy = searchParams.get('sortBy') || 'date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * pageSize;

    // Build where clause
    const where = {};

    // Search in description
    if (search) {
      where.description = {
        contains: search,
        mode: 'insensitive'
      };
    }

    // Filter by activity type
    if (activityType) {
      where.type = activityType;
    }

    // Filter by contact
    if (contactId) {
      where.contactId = contactId;
    }

    // Filter by date range
    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = new Date(startDate);
      }
      if (endDate) {
        where.date.lte = new Date(endDate);
      }
    }

    // Build orderBy clause
    const orderBy = {};
    orderBy[sortBy] = sortOrder;

    // Get total count for pagination
    const total = await prisma.activity.count({ where });

    // Get activities with contact information
    const activities = await prisma.activity.findMany({
      where,
      orderBy,
      skip,
      take: pageSize,
      include: {
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        activityType: {
          select: {
            id: true,
            name: true,
            icon: true,
            color: true
          }
        }
      }
    });

    return NextResponse.json({
      activities,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('Error fetching admin activities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activities' },
      { status: 500 }
    );
  }
}

// POST /api/admin/activities - Create a new activity for any contact (admin only)
export async function POST(request) {
  try {
    // Check authentication and authorization
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.contactId || !body.type || !body.description) {
      return NextResponse.json(
        { error: 'Contact ID, type, and description are required' },
        { status: 400 }
      );
    }

    // Verify the contact exists
    const contact = await prisma.contact.findUnique({
      where: { id: body.contactId }
    });

    if (!contact) {
      return NextResponse.json(
        { error: 'Contact not found' },
        { status: 404 }
      );
    }

    // Create the activity
    const activity = await prisma.activity.create({
      data: {
        type: body.type,
        description: body.description,
        contactId: body.contactId,
        date: body.date ? new Date(body.date) : new Date(),
        createdAt: new Date()
      },
      include: {
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        activityType: {
          select: {
            id: true,
            name: true,
            icon: true,
            color: true
          }
        }
      }
    });

    // Update the contact's lastContactDate
    await prisma.contact.update({
      where: { id: body.contactId },
      data: { lastContactDate: new Date() }
    });

    return NextResponse.json(activity, { status: 201 });

  } catch (error) {
    console.error('Error creating admin activity:', error);
    return NextResponse.json(
      { error: 'Failed to create activity' },
      { status: 500 }
    );
  }
}
