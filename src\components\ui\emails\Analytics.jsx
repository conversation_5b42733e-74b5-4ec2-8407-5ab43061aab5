"use client";
import { useState, useEffect } from "react";
import { Line, Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
} from "chart.js";
import { Info, Send, Mail, MousePointer, AlertCircle } from "lucide-react";
import Tooltip from "@/components/ui/Tooltip";
import MetricCard from "./MetricCard";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend
);

const formatPercentage = (value) => {
  if (!value || isNaN(value)) return "0%";
  return `${(value * 100).toFixed(1)}%`;
};

export default function Analytics() {
  const [metrics, setMetrics] = useState({
    emailsSent: 0,
    openRate: 0,
    clickRate: 0,
    bounceRate: 0,
    weeklyTrends: {
      emailsSent: 0,
      openRate: 0,
      clickRate: 0,
      bounceRate: 0,
    },
    monthlyTrends: {
      emailsSent: 0,
      openRate: 0,
      clickRate: 0,
      bounceRate: 0,
    },
    monthlyStats: [],
    templatePerformance: [],
    topEngagingTemplates: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await fetch("/api/email-metrics");
        if (!response.ok) throw new Error("Failed to fetch metrics");
        const data = await response.json();
        setMetrics(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchMetrics();
  }, []);

  const monthlyChartData = {
    labels: metrics.monthlyStats.map((stat) => stat.month),
    datasets: [
      {
        label: "Sent",
        data: metrics.monthlyStats.map((stat) => stat.sent),
        borderColor: "#4F46E5",
        backgroundColor: "#4F46E5",
        tension: 0.4,
      },
      {
        label: "Opened",
        data: metrics.monthlyStats.map((stat) => stat.opened),
        borderColor: "#10B981",
        backgroundColor: "#10B981",
        tension: 0.4,
      },
      {
        label: "Clicked",
        data: metrics.monthlyStats.map((stat) => stat.clicked),
        borderColor: "#F59E0B",
        backgroundColor: "#F59E0B",
        tension: 0.4,
      },
    ],
  };

  const monthlyChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: true,
        },
        ticks: {
          stepSize: 50,
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
    plugins: {
      legend: {
        position: "top",
      },
      tooltip: {
        backgroundColor: "#1F2937",
        titleColor: "#F3F4F6",
        bodyColor: "#F3F4F6",
        padding: 12,
        displayColors: true,
      },
    },
  };

  const templateChartData = {
    labels: metrics.templatePerformance
      .sort((a, b) => b.openRate + b.clickRate - (a.openRate + a.clickRate))
      .map((temp) => temp.name),
    datasets: [
      {
        label: "Open Rate",
        data: metrics.templatePerformance.map((temp) => temp.openRate * 100),
        backgroundColor: "#4F46E5",
        borderRadius: 4,
      },
      {
        label: "Click Rate",
        data: metrics.templatePerformance.map((temp) => temp.clickRate * 100),
        backgroundColor: "#10B981",
        borderRadius: 4,
      },
    ],
  };

  const templateChartOptions = {
    indexAxis: "y",
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        beginAtZero: true,
        grid: {
          display: true,
        },
        ticks: {
          callback: (value) => `${value}%`,
        },
      },
      y: {
        grid: {
          display: false,
        },
      },
    },
    plugins: {
      legend: {
        position: "top",
      },
      tooltip: {
        backgroundColor: "#1F2937",
        titleColor: "#F3F4F6",
        bodyColor: "#F3F4F6",
        padding: 12,
        displayColors: true,
        callbacks: {
          label: (context) =>
            `${context.dataset.label}: ${context.parsed.x.toFixed(1)}%`,
        },
      },
    },
    barThickness: 20,
    maxBarThickness: 30,
  };

  const engagementChartData = {
    labels: metrics.monthlyStats.map((stat) => stat.month),
    datasets: [
      {
        label: "Active Contacts",
        data: metrics.monthlyStats.map((stat) =>
          ((stat.opened / stat.sent) * 100).toFixed(1)
        ),
        borderColor: "#8B5CF6",
        backgroundColor: "#8B5CF6",
        tension: 0.4,
      },
      {
        label: "Highly Engaged",
        data: metrics.monthlyStats.map((stat) =>
          ((stat.clicked / stat.opened) * 100).toFixed(1)
        ),
        borderColor: "#10B981",
        backgroundColor: "#10B981",
        tension: 0.4,
      },
    ],
  };

  const engagementChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: true,
        },
        ticks: {
          callback: (value) => `${value}%`,
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
    plugins: {
      legend: {
        position: "top",
      },
      tooltip: {
        backgroundColor: "#1F2937",
        titleColor: "#F3F4F6",
        bodyColor: "#F3F4F6",
        padding: 12,
        displayColors: true,
        callbacks: {
          label: (context) => `${context.dataset.label}: ${context.parsed.y}%`,
        },
      },
    },
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 dark:text-red-400 p-4">
        {error}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card-light dark:bg-gray-800 p-6 rounded-lg">
          <div className="bg-[#4F46E5]/10 dark:bg-[#4F46E5]/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Emails Sent</h3>
              <Tooltip content="Total number of emails sent this week, with week-over-week trend">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Emails Sent"
              value={metrics.emailsSent}
              trend={metrics.weeklyTrends.emailsSent}
              icon={Send}
            />
          </div>
        </div>
        <div className="card-light dark:bg-gray-800 p-6 rounded-lg">
          <div className="bg-[#10B981]/10 dark:bg-[#10B981]/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Open Rate</h3>
              <Tooltip content="Percentage of sent emails that were opened this week, with week-over-week trend">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Open Rate"
              value={formatPercentage(metrics.openRate)}
              trend={metrics.weeklyTrends.openRate}
              icon={Mail}
            />
          </div>
        </div>
        <div className="card-light dark:bg-gray-800 p-6 rounded-lg">
          <div className="bg-[#F59E0B]/10 dark:bg-[#F59E0B]/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Click Rate</h3>
              <Tooltip content="Percentage of opened emails clicked this week, with week-over-week trend">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Click Rate"
              value={formatPercentage(metrics.clickRate)}
              trend={metrics.weeklyTrends.clickRate}
              icon={MousePointer}
            />
          </div>
        </div>
        <div className="card-light dark:bg-gray-800 p-6 rounded-lg">
          <div className="bg-red-500/10 dark:bg-red-500/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Bounce Rate</h3>
              <Tooltip content="Percentage of emails that bounced this week, with week-over-week trend">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Bounce Rate"
              value={formatPercentage(metrics.bounceRate)}
              trend={metrics.weeklyTrends.bounceRate}
              icon={AlertCircle}
              trendReversed
            />
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card-light dark:bg-gray-800 p-6 rounded-lg">
          <div className="flex items-center gap-2 mb-6">
            <h3 className="text-lg font-medium">Monthly Email Statistics</h3>
            <Tooltip content="Number of emails sent, opened, and clicked per month">
              <Info className="h-4 w-4 text-gray-400" />
            </Tooltip>
          </div>
          <div className="h-[400px]">
            <Bar data={monthlyChartData} options={monthlyChartOptions} />
          </div>
        </div>

        <div className="card-light dark:bg-gray-800 p-6 rounded-lg">
          <div className="flex items-center gap-2 mb-6">
            <h3 className="text-lg font-medium">Template Performance</h3>
            <Tooltip content="Open and click rates for each email template">
              <Info className="h-4 w-4 text-gray-400" />
            </Tooltip>
          </div>
          <div className="h-[400px]">
            <Bar data={templateChartData} options={templateChartOptions} />
          </div>
        </div>
      </div>

      <div className="card-light dark:bg-gray-800 p-6 rounded-lg">
        <div className="flex items-center gap-2 mb-6">
          <h3 className="text-lg font-medium">Contact Engagement Trends</h3>
          <Tooltip content="Monthly trends in contact engagement levels">
            <Info className="h-4 w-4 text-gray-400" />
          </Tooltip>
        </div>
        <div className="h-[400px]">
          <Line data={engagementChartData} options={engagementChartOptions} />
        </div>
      </div>
    </div>
  );
}
