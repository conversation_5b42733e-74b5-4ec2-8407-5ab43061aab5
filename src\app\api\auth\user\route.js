import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";

export async function POST(request) {
  try {
    const { email, name, azureId } = await request.json();

    if (!email || !azureId) {
      return NextResponse.json(
        { error: "Email and Azure ID are required" },
        { status: 400 }
      );
    }

    // Try to find existing user
    let user = await prisma.user.findUnique({
      where: { azureId },
    });

    if (!user) {
      // Create new user if they don't exist
      user = await prisma.user.create({
        data: {
          email,
          name,
          azureId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      console.log("Created new user:", user);
    } else {
      console.log("Found Existing User:", user);
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Error in user creation/update:", error);
    return NextResponse.json(
      { error: "Failed to process user: " + error.message },
      { status: 500 }
    );
  }
}

// Optionally add a GET endpoint to fetch user info
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const azureId = searchParams.get("azureId");

    if (!azureId) {
      return NextResponse.json(
        { error: "Azure ID is required" },
        { status: 400 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { azureId },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { error: "Failed to fetch user: " + error.message },
      { status: 500 }
    );
  }
}
