"use client";

import { useState, useEffect } from "react";
import { usePermissions } from "@/hooks/usePermissions";
import {
  Info,
  Upload,
  Download,
  Users,
  Tag,
  Trash2,
  Grid,
  List,
  Search,
  ExternalLink,
  File as FileIcon,
  FileText,
  Image,
  SortAsc,
  SortDesc,
} from "lucide-react";
import Tooltip from "@/components/ui/Tooltip";
import DocumentTagSelector from "@/components/documents/DocumentTagSelector";
import UploadDocumentModal from "@/components/documents/UploadDocumentModal";
import DeleteDocumentModal from "@/components/documents/DeleteDocumentModal";
import ManageContactsModal from "@/components/documents/ManageContactsModal";
import EditDocumentTagsModal from "@/components/documents/EditDocumentTagsModal";

export default function DocumentsTab({ contactId }) {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState("grid"); // 'grid' or 'list'
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSource, setSelectedSource] = useState("all");
  const [selectedTags, setSelectedTags] = useState([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);
  const [manageContactsModalOpen, setManageContactsModalOpen] = useState(false);
  const [documentToManage, setDocumentToManage] = useState(null);
  const [editTagsModalOpen, setEditTagsModalOpen] = useState(false);
  const [documentToEditTags, setDocumentToEditTags] = useState(null);
  const [contacts, setContacts] = useState([]);
  const [contactsError, setContactsError] = useState(null);

  // Permission check for contacts
  const { hasPermission: canViewContacts, loading: permissionsLoading } =
    usePermissions("contacts:read");

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/contacts/${contactId}/documents`);
        if (response.ok) {
          const data = await response.json();
          setDocuments(data);
        }
      } catch (error) {
        console.error("Failed to fetch documents:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchDocuments();
  }, [contactId]);

  // Fetch contacts if permitted
  // Map contactsData for each document after both documents and contacts are loaded
  useEffect(() => {
    if (
      !Array.isArray(documents) ||
      !Array.isArray(contacts) ||
      contacts.length === 0
    )
      return;
    // Only update if at least one document is missing contactsData
    const needsMapping = documents.some(
      (doc) => !Array.isArray(doc.contactsData) || doc.contactsData.length === 0
    );
    if (!needsMapping) return;
    setDocuments((prevDocs) =>
      prevDocs.map((doc) => {
        if (Array.isArray(doc.contactsData) && doc.contactsData.length > 0)
          return doc;
        if (Array.isArray(doc.contactId)) {
          return {
            ...doc,
            contactsData: doc.contactId
              .map((cid) => contacts.find((c) => c.id === cid))
              .filter(Boolean),
          };
        }
        if (doc.contactId) {
          const found = contacts.find((c) => c.id === doc.contactId);
          if (found) {
            return {
              ...doc,
              contactsData: [found],
            };
          }
        }
        if (doc.contactName) {
          const found = contacts.find((c) => c.name === doc.contactName);
          if (found) {
            return {
              ...doc,
              contactsData: [found],
            };
          }
        }
        return doc;
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contacts]);
  useEffect(() => {
    if (!canViewContacts || permissionsLoading) return;
    const fetchContacts = async () => {
      try {
        setContactsError(null);
        const response = await fetch("/api/contacts");
        if (response.ok) {
          const data = await response.json();
          // Support both {id, name} and {id, firstName, lastName}
          setContacts(
            data.map((c) => ({
              id: c.id,
              name: c.name || `${c.firstName || ""} ${c.lastName || ""}`.trim(),
            }))
          );
        } else if (response.status === 403 || response.status === 401) {
          setContactsError("You do not have permission to view contacts.");
        } else {
          setContactsError("Failed to load contacts.");
        }
      } catch (error) {
        setContactsError("Failed to load contacts.");
      }
    };
    fetchContacts();
  }, [canViewContacts, permissionsLoading]);

  // Filter documents only
  const [sortConfig, setSortConfig] = useState({
    key: "uploadedAt",
    direction: "desc",
  });
  const filteredDocuments = (documents || []).filter((doc) => {
    const matchesSearch =
      searchTerm === "" ||
      (doc.name && doc.name.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesSource =
      selectedSource === "all" || doc.source === selectedSource;
    // Tag filtering - document must have all selected tags
    const safeSelectedTags = Array.isArray(selectedTags) ? selectedTags : [];
    const matchesTags =
      safeSelectedTags.length === 0 ||
      (doc.tags &&
        Array.isArray(doc.tags) &&
        safeSelectedTags.every((tag) => doc.tags.includes(tag)));
    return matchesSearch && matchesSource && matchesTags;
  });

  // Sort documents (separate from filter)
  const sortedDocuments = [...filteredDocuments].sort((a, b) => {
    const { key, direction } = sortConfig;
    let aValue = a[key];
    let bValue = b[key];
    if (key === "name") {
      aValue = (aValue || "").toLowerCase();
      bValue = (bValue || "").toLowerCase();
    } else if (key === "uploadedAt") {
      aValue = aValue ? new Date(aValue) : new Date(0);
      bValue = bValue ? new Date(bValue) : new Date(0);
    }
    if (aValue < bValue) return direction === "asc" ? -1 : 1;
    if (aValue > bValue) return direction === "asc" ? 1 : -1;
    return 0;
  });

  // Toggle sort key/direction
  const toggleSort = (key) => {
    setSortConfig((prev) => {
      if (prev.key === key) {
        return { key, direction: prev.direction === "asc" ? "desc" : "asc" };
      }
      return { key, direction: "asc" };
    });
  };

  // Get file icon based on file type (match main documents page)
  const getFileIcon = (fileType) => {
    switch (fileType) {
      case "pdf":
        return <FileText className="h-6 w-6 text-red-500" />;
      case "docx":
        return <FileText className="h-6 w-6 text-blue-500" />;
      case "xlsx":
        return <FileText className="h-6 w-6 text-green-500" />;
      case "pptx":
        return <FileText className="h-6 w-6 text-orange-500" />;
      case "jpg":
      case "png":
      case "gif":
        return <Image className="h-6 w-6 text-purple-500" />;
      default:
        return <FileIcon className="h-6 w-6 text-gray-400" />;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };



  return (
    <div className="flex flex-col h-full gap-4">
      {/* Header Section */}
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Documents
          </h3>
          <Tooltip
            content={
              <div className="w-64">
                <p className="font-medium mb-1">Microsoft Integration</p>
                <p className="text-xs mb-1">
                  This tab integrates with Microsoft OneDrive and SharePoint to
                  show documents associated with this contact.
                </p>
              </div>
            }
            position="bottom"
          >
            <div className="ml-2 text-gray-500 dark:text-gray-400 cursor-help">
              <Info className="h-4 w-4" />
            </div>
          </Tooltip>
        </div>
        <button
          className="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
          onClick={() => setShowUploadModal(true)}
        >
          <Upload className="h-4 w-4 mr-2" />
          Upload Document
        </button>
      </div>

      {/* Improved Toolbar with compact, clear layout */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-4">
        <div className="flex flex-wrap gap-2 items-center justify-between">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 w-full">
            <div className="flex flex-1 gap-2 items-center min-w-0">
              <div className="relative w-full max-w-xs">
                <input
                  type="text"
                  placeholder="Search documents..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <Search className="absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>
              <Tooltip content="Filter by source">
                <select
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white min-w-[120px]"
                  value={selectedSource}
                  onChange={(e) => setSelectedSource(e.target.value)}
                >
                  <option value="all">All Sources</option>
                  <option value="onedrive">OneDrive</option>
                  <option value="sharepoint">SharePoint</option>
                </select>
              </Tooltip>
              <Tooltip content="Filter by tags">
                <div className="min-w-32">
                  <DocumentTagSelector
                    value={selectedTags}
                    onChange={setSelectedTags}
                    className="text-sm"
                  />
                </div>
              </Tooltip>
            </div>
            <div className="flex gap-2 items-center flex-wrap">
              <Tooltip content="Sort by name">
                <button
                  className={`flex items-center px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    sortConfig.key === "name"
                      ? "font-semibold text-primary"
                      : ""
                  }`}
                  onClick={() => toggleSort("name")}
                >
                  Name
                  {sortConfig.key === "name" &&
                    (sortConfig.direction === "asc" ? (
                      <SortAsc className="h-3 w-3 ml-1 text-primary" />
                    ) : (
                      <SortDesc className="h-3 w-3 ml-1 text-primary" />
                    ))}
                </button>
              </Tooltip>
              <Tooltip content="Sort by date">
                <button
                  className={`flex items-center px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    sortConfig.key === "uploadedAt"
                      ? "font-semibold text-primary"
                      : ""
                  }`}
                  onClick={() => toggleSort("uploadedAt")}
                >
                  Date
                  {sortConfig.key === "uploadedAt" &&
                    (sortConfig.direction === "asc" ? (
                      <SortAsc className="h-3 w-3 ml-1 text-primary" />
                    ) : (
                      <SortDesc className="h-3 w-3 ml-1 text-primary" />
                    ))}
                </button>
              </Tooltip>
              <Tooltip content="Grid view">
                <button
                  className={`p-2 ${
                    viewMode === "grid"
                      ? "bg-gray-100 dark:bg-gray-700"
                      : "bg-white dark:bg-gray-800"
                  } border border-gray-300 dark:border-gray-600 rounded-md`}
                  onClick={() => setViewMode("grid")}
                  aria-label="Grid view"
                >
                  <Grid className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                </button>
              </Tooltip>
              <Tooltip content="List view">
                <button
                  className={`p-2 ${
                    viewMode === "list"
                      ? "bg-gray-100 dark:bg-gray-700"
                      : "bg-white dark:bg-gray-800"
                  } border border-gray-300 dark:border-gray-600 rounded-md`}
                  onClick={() => setViewMode("list")}
                  aria-label="List view"
                >
                  <List className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                </button>
              </Tooltip>
            </div>
          </div>
        </div>
      </div>

      {/* Document List/Grid Section */}
      <div className="flex-1 overflow-y-auto bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : sortedDocuments.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <div className="text-5xl mb-4">📄</div>
            <p className="text-lg font-medium">No documents found</p>
            <p className="text-sm mt-1">
              Upload a document or change your search criteria
            </p>
          </div>
        ) : viewMode === "grid" ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {sortedDocuments.map((doc) => (
              <div
                key={doc.id}
                className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200 flex flex-col h-full"
              >
                <div className="p-4 flex-1">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {getFileIcon(doc.fileType || doc.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <Tooltip content={doc.name}>
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {doc.name}
                        </p>
                      </Tooltip>
                      <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {doc.contactsData && doc.contactsData.length > 0 ? (
                          <Tooltip
                            content={
                              <div className="text-xs">
                                <div className="font-medium mb-1">
                                  Associated with:
                                </div>
                                <ul className="list-disc pl-4">
                                  {doc.contactsData.map((contact) => (
                                    <li key={contact.id}>
                                      {contact.name ||
                                        `Contact ID: ${contact.id}`}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            }
                          >
                            <span className="text-primary dark:text-primary-light">
                              {doc.contactsData
                                .map((c) => c.name)
                                .filter(Boolean)
                                .join(", ") || "No contact"}
                            </span>
                          </Tooltip>
                        ) : (
                          <span className="text-gray-400">No contact</span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        <span className="flex items-center gap-2">
                          <span>{formatDate(doc.uploadedAt)}</span>
                          {doc.size && (
                            <span className="text-gray-400">
                              • {doc.size}
                            </span>
                          )}
                        </span>
                      </div>
                      {/* Document Tags */}
                      {doc.tags &&
                      Array.isArray(doc.tags) &&
                      doc.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                            {doc.tags.slice(0, 2).map((tag, index) => (
                              <span
                                key={index}
                                className="inline-flex px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 rounded-full"
                              >
                                {tag}
                              </span>
                            ))}
                            {doc.tags.length > 2 && (
                              <Tooltip
                                content={
                                  <div className="text-xs">
                                    <div className="font-medium mb-1">
                                      All tags:
                                    </div>
                                    <div className="flex flex-wrap gap-1">
                                      {doc.tags.map((tag, index) => (
                                        <span
                                          key={index}
                                          className="inline-block px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs"
                                        >
                                          {tag}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                }
                              >
                                <span className="inline-flex px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-full cursor-help">
                                  +{doc.tags.length - 2}
                                </span>
                              </Tooltip>
                            )}
                          </div>
                        )}
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 grid grid-cols-4 gap-1 mt-auto">
                  <div className="flex justify-start">
                    <Tooltip content="Download this document">
                      <button
                        type="button"
                        className="text-xs text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white flex items-center"
                        onClick={() => {
                          try {
                            const a = document.createElement("a");
                            a.href = doc.downloadUrl;
                            a.style.display = "none";
                            // Only set download attribute for same-origin URLs
                            try {
                              const urlObj = new URL(
                                doc.downloadUrl,
                                window.location.origin
                              );
                              if (urlObj.origin === window.location.origin) {
                                a.setAttribute(
                                  "download",
                                  doc.name || "document"
                                );
                              }
                            } catch {}
                            document.body.appendChild(a);
                            a.click();
                            setTimeout(() => document.body.removeChild(a), 100);
                          } catch {
                            window.open(
                              doc.downloadUrl,
                              "_blank",
                              "noopener,noreferrer"
                            );
                          }
                        }}
                        aria-label={`Download ${doc.name}`}
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </button>
                    </Tooltip>
                  </div>
                  <div className="flex justify-center">
                    <Tooltip content="Manage contact associations">
                      <button
                        className="text-xs text-primary hover:text-primary-hover flex items-center"
                        onClick={() => {
                          setManageContactsModalOpen(true);
                          setDocumentToManage(doc);
                        }}
                      >
                        <Users className="h-3 w-3 mr-1" />
                        Contacts
                      </button>
                    </Tooltip>
                  </div>
                  <div className="flex justify-center">
                    <Tooltip content="Edit document tags">
                      <button
                        className="text-xs text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 flex items-center"
                        onClick={() => {
                          setEditTagsModalOpen(true);
                          setDocumentToEditTags(doc);
                        }}
                      >
                        <Tag className="h-3 w-3 mr-1" />
                        Tags
                      </button>
                    </Tooltip>
                  </div>
                  <div className="flex justify-end">
                    <Tooltip content="Delete this document">
                      <button
                        className="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 flex items-center"
                        onClick={() => {
                          setDeleteModalOpen(true);
                          setDocumentToDelete(doc);
                        }}
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        Delete
                      </button>
                    </Tooltip>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => toggleSort("name")}
                    >
                      Name
                      {sortConfig.key === "name" &&
                        (sortConfig.direction === "asc" ? (
                          <SortAsc className="h-3 w-3 ml-1 text-primary" />
                        ) : (
                          <SortDesc className="h-3 w-3 ml-1 text-primary" />
                        ))}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Contact
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    <button
                      className="flex items-center"
                      onClick={() => toggleSort("uploadedAt")}
                    >
                      Date
                      {sortConfig.key === "uploadedAt" &&
                        (sortConfig.direction === "asc" ? (
                          <SortAsc className="h-3 w-3 ml-1 text-primary" />
                        ) : (
                          <SortDesc className="h-3 w-3 ml-1 text-primary" />
                        ))}
                    </button>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Size
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Source
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Tags
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {sortedDocuments.map((doc) => (
                  <tr
                    key={doc.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 mr-3">
                          {getFileIcon(doc.fileType || doc.type)}
                        </div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {doc.name}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {doc.contactsData && doc.contactsData.length > 0 ? (
                        <Tooltip
                          content={
                            <div className="text-xs">
                              <div className="font-medium mb-1">
                                Associated with:
                              </div>
                              <ul className="list-disc pl-4">
                                {doc.contactsData.map((contact) => (
                                  <li key={contact.id}>
                                    {contact.name ||
                                      `Contact ID: ${contact.id}`}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          }
                        >
                          <div className="text-sm text-primary dark:text-primary-light">
                            {doc.contactsData
                              .map((c) => c.name)
                              .filter(Boolean)
                              .join(", ") || "No contact"}
                          </div>
                        </Tooltip>
                      ) : (
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          No contact
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(doc.uploadedAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {doc.size || "-"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          doc.source === "onedrive"
                            ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                            : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                        }`}
                      >
                        {doc.source === "onedrive" ? "OneDrive" : "SharePoint"}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      {doc.tags &&
                      Array.isArray(doc.tags) &&
                      doc.tags.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {doc.tags.slice(0, 3).map((tag, index) => (
                            <span
                              key={index}
                              className="inline-flex px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                          {doc.tags.length > 3 && (
                            <Tooltip
                              content={
                                <div className="text-xs">
                                  <div className="font-medium mb-1">
                                    All tags:
                                  </div>
                                  <div className="flex flex-wrap gap-1">
                                    {doc.tags.map((tag, index) => (
                                      <span
                                        key={index}
                                        className="inline-block px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs"
                                      >
                                        {tag}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              }
                            >
                              <span className="inline-flex px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-full cursor-help">
                                +{doc.tags.length - 3}
                              </span>
                            </Tooltip>
                          )}
                        </div>
                      ) : (
                        <span className="text-xs text-gray-400 dark:text-gray-500">
                          No tags
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Tooltip content="Download this document">
                        <button
                          type="button"
                          className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white mr-3 flex items-center"
                          onClick={() => {
                            try {
                              const a = document.createElement("a");
                              a.href = doc.downloadUrl;
                              a.style.display = "none";
                              try {
                                const urlObj = new URL(
                                  doc.downloadUrl,
                                  window.location.origin
                                );
                                if (urlObj.origin === window.location.origin) {
                                  a.setAttribute(
                                    "download",
                                    doc.name || "document"
                                  );
                                }
                              } catch {}
                              document.body.appendChild(a);
                              a.click();
                              setTimeout(
                                () => document.body.removeChild(a),
                                100
                              );
                            } catch {
                              window.open(
                                doc.downloadUrl,
                                "_blank",
                                "noopener,noreferrer"
                              );
                            }
                          }}
                          aria-label={`Download ${doc.name}`}
                        >
                          <Download className="h-3.5 w-3.5 mr-1" />
                          Download
                        </button>
                      </Tooltip>
                      <Tooltip content="Manage contact associations">
                        <button
                          className="text-primary hover:text-primary-hover mr-3 flex items-center"
                          onClick={() => {
                            setManageContactsModalOpen(true);
                            setDocumentToManage(doc);
                          }}
                        >
                          <Users className="h-3.5 w-3.5 mr-1" />
                          Contacts
                        </button>
                      </Tooltip>
                      <Tooltip content="Edit document tags">
                        <button
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3 flex items-center"
                          onClick={() => {
                            setEditTagsModalOpen(true);
                            setDocumentToEditTags(doc);
                          }}
                        >
                          <Tag className="h-3.5 w-3.5 mr-1" />
                          Tags
                        </button>
                      </Tooltip>
                      <Tooltip content="Delete this document">
                        <button
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 flex items-center"
                          onClick={() => {
                            setDeleteModalOpen(true);
                            setDocumentToDelete(doc);
                          }}
                        >
                          <Trash2 className="h-3.5 w-3.5 mr-1" />
                          Delete
                        </button>
                      </Tooltip>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Upload Modal */}
      <UploadDocumentModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUploadComplete={() => window.location.reload()}
        contacts={contacts}
      />

      {/* Delete Modal */}
      <DeleteDocumentModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        documentName={documentToDelete?.name}
        onConfirm={() => {
          setDocuments((prevDocs) =>
            prevDocs.filter((doc) => doc.id !== documentToDelete?.id)
          );
        }}
      />

      {/* Manage Contacts Modal */}
      <ManageContactsModal
        isOpen={manageContactsModalOpen}
        onClose={() => setManageContactsModalOpen(false)}
        document={documentToManage}
        allContacts={contacts}
        onSave={(updatedDoc) => {
          setDocuments((prevDocs) =>
            prevDocs.map((doc) => (doc.id === updatedDoc.id ? updatedDoc : doc))
          );
        }}
      />

      {/* Edit Document Tags Modal */}
      <EditDocumentTagsModal
        isOpen={editTagsModalOpen}
        onClose={() => setEditTagsModalOpen(false)}
        document={documentToEditTags}
        onSave={(updatedDoc) => {
          setDocuments((prevDocs) =>
            prevDocs.map((doc) => (doc.id === updatedDoc.id ? updatedDoc : doc))
          );
        }}
      />
      {/* Contacts fetch error display */}
      {contactsError && (
        <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded text-xs">
          {contactsError}
        </div>
      )}
    </div>
  );
}
