import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

// GET /api/activity-types/:id - Get a specific activity type
export async function GET(request, { params }) {
  try {
    // Check authentication
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;
    
    // Fetch the activity type
    const activityType = await prisma.activityType.findUnique({
      where: { id }
    });

    if (!activityType) {
      return NextResponse.json(
        { error: 'Activity type not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(activityType);
  } catch (error) {
    console.error('Failed to fetch activity type:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activity type' },
      { status: 500 }
    );
  }
}

// PATCH /api/activity-types/:id - Update an activity type
export async function PATCH(request, { params }) {
  try {
    // Check authentication and authorization
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Only admins can update activity types' },
        { status: 403 }
      );
    }

    const { id } = params;
    const body = await request.json();
    
    // Check if the activity type exists
    const existingType = await prisma.activityType.findUnique({
      where: { id }
    });

    if (!existingType) {
      return NextResponse.json(
        { error: 'Activity type not found' },
        { status: 404 }
      );
    }

    // Prevent modification of isSystem flag
    if (body.isSystem !== undefined) {
      delete body.isSystem;
    }

    // Update the activity type
    const updatedType = await prisma.activityType.update({
      where: { id },
      data: body
    });

    // Log the activity type update
    await auditLogger.logActivityTypeUpdate({
      userId: user.id,
      activityTypeId: id,
      oldValues: existingType,
      newValues: updatedType,
      request
    });

    return NextResponse.json(updatedType);
  } catch (error) {
    console.error('Failed to update activity type:', error);
    return NextResponse.json(
      { error: 'Failed to update activity type' },
      { status: 500 }
    );
  }
}

// DELETE /api/activity-types/:id - Delete an activity type
export async function DELETE(request, { params }) {
  try {
    // Check authentication and authorization
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Only admins can delete activity types' },
        { status: 403 }
      );
    }

    const { id } = params;
    
    // Check if the activity type exists
    const existingType = await prisma.activityType.findUnique({
      where: { id }
    });

    if (!existingType) {
      return NextResponse.json(
        { error: 'Activity type not found' },
        { status: 404 }
      );
    }

    // Prevent deletion of system types
    if (existingType.isSystem) {
      return NextResponse.json(
        { error: 'Cannot delete system activity types' },
        { status: 403 }
      );
    }

    // Check if the activity type is in use
    const activityCount = await prisma.activity.count({
      where: { type: id }
    });

    if (activityCount > 0) {
      // Instead of deleting, mark as inactive
      const deactivatedType = await prisma.activityType.update({
        where: { id },
        data: { isActive: false }
      });

      // Log the activity type deactivation
      await auditLogger.logActivityTypeUpdate({
        userId: user.id,
        activityTypeId: id,
        oldValues: existingType,
        newValues: deactivatedType,
        request
      });

      return NextResponse.json({
        ...deactivatedType,
        message: 'Activity type is in use and has been deactivated instead of deleted'
      });
    }

    // Log the activity type deletion
    await auditLogger.logActivityTypeDelete({
      userId: user.id,
      activityTypeId: id,
      oldValues: existingType,
      request
    });

    // Delete the activity type if not in use
    await prisma.activityType.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Activity type deleted successfully' });
  } catch (error) {
    console.error('Failed to delete activity type:', error);
    return NextResponse.json(
      { error: 'Failed to delete activity type' },
      { status: 500 }
    );
  }
}
