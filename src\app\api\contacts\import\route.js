import { NextResponse } from "next/server";
import prisma from "@/lib/prisma/client";

export async function POST(request) {
  try {
    const { contacts } = await request.json();

    if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
      return NextResponse.json(
        { error: "No valid contacts provided" },
        { status: 400 }
      );
    }

    // Process each contact
    const results = {
      total: contacts.length,
      imported: 0,
      failed: 0,
      errors: []
    };

    for (const contact of contacts) {
      try {
        // Validate required fields
        if (!contact.firstName && !contact.lastName) {
          results.failed++;
          results.errors.push({
            contact: `${contact.firstName || ''} ${contact.lastName || ''}`.trim(),
            error: "First name or last name is required"
          });
          continue;
        }

        // Create the contact
        await prisma.contact.create({
          data: {
            firstName: contact.firstName || '',
            lastName: contact.lastName || '',
            email: contact.email || '',
            phone: contact.phone || '',
            company: contact.company || '',
            type: contact.type || 'prospective',
            userId: "000000000000000000000000", // Valid 24-character MongoDB ObjectID //TODO: Replace with actual user ID from session
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });

        results.imported++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          contact: `${contact.firstName || ''} ${contact.lastName || ''}`.trim(),
          error: error.message
        });
      }
    }

    return NextResponse.json(results);
  } catch (error) {
    console.error("Failed to import contacts:", error);
    return NextResponse.json(
      { error: "Failed to import contacts" },
      { status: 500 }
    );
  }
}
