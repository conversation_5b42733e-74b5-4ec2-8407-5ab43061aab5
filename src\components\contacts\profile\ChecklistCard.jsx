'use client';

import { useState } from 'react';
import { CheckSquare, Square, ChevronDown, ChevronUp, Edit, Trash2 } from 'lucide-react';

export default function ChecklistCard({
  checklist,
  isExpanded,
  onToggleExpand,
  onEdit,
  onDelete,
  onItemToggle
}) {
  const [isHovered, setIsHovered] = useState(false);
  
  // Calculate completion percentage
  const totalItems = checklist.items.length;
  const completedItems = checklist.items.filter(item => item.completed).length;
  const completionPercentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
  
  // Format dates
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  // Get status badge color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'archived':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };
  
  return (
    <div 
      className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm overflow-hidden transition-all duration-200 ${isHovered ? 'shadow-md' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Checklist Header */}
      <div className="p-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 border-b border-gray-200 dark:border-gray-700">
        <div className="flex-1">
          <div className="flex items-center">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">{checklist.title}</h4>
            {checklist.category && (
              <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 rounded-full">
                {checklist.category}
              </span>
            )}
            <span className={`ml-2 px-2 py-0.5 text-xs font-medium rounded-full ${getStatusColor(checklist.status)}`}>
              {checklist.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </span>
          </div>
          
          {checklist.description && (
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              {checklist.description}
            </p>
          )}
          
          <div className="mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400">
            <span>Created: {formatDate(checklist.createdAt)}</span>
            {checklist.updatedAt && checklist.updatedAt !== checklist.createdAt && (
              <span className="ml-2">· Updated: {formatDate(checklist.updatedAt)}</span>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={onEdit}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            title="Edit checklist"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button
            onClick={onDelete}
            className="p-1 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
            title="Delete checklist"
          >
            <Trash2 className="h-4 w-4" />
          </button>
          <button
            onClick={onToggleExpand}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            title={isExpanded ? "Collapse" : "Expand"}
          >
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </button>
        </div>
      </div>
      
      {/* Progress Bar */}
      <div className="px-4 py-2 bg-gray-50 dark:bg-gray-700/50">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Progress</span>
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">{completionPercentage}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
          <div 
            className="bg-primary h-2.5 rounded-full transition-all duration-300" 
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 text-right">
          {completedItems} of {totalItems} items completed
        </div>
      </div>
      
      {/* Checklist Items */}
      {isExpanded && (
        <div className="p-4 space-y-2 bg-gray-50 dark:bg-gray-800">
          {checklist.items.length > 0 ? (
            checklist.items.map((item) => (
              <div 
                key={item.id} 
                className={`p-3 rounded-md border ${item.completed 
                  ? 'bg-green-50 dark:bg-green-900/10 border-green-200 dark:border-green-900/30' 
                  : 'bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600'}`}
              >
                <div className="flex items-start gap-2">
                  <button
                    onClick={() => onItemToggle(checklist.id, item.id, !item.completed)}
                    className={`mt-0.5 ${item.completed 
                      ? 'text-green-600 dark:text-green-500' 
                      : 'text-gray-400 dark:text-gray-500 hover:text-primary dark:hover:text-primary-light'}`}
                  >
                    {item.completed ? <CheckSquare className="h-5 w-5" /> : <Square className="h-5 w-5" />}
                  </button>
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${item.completed 
                      ? 'text-gray-600 dark:text-gray-400 line-through' 
                      : 'text-gray-900 dark:text-white'}`}
                    >
                      {item.text}
                    </p>
                    {item.description && (
                      <p className={`mt-1 text-xs ${item.completed 
                        ? 'text-gray-500 dark:text-gray-500 line-through' 
                        : 'text-gray-600 dark:text-gray-400'}`}
                      >
                        {item.description}
                      </p>
                    )}
                    {item.completed && item.completedAt && (
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                        Completed: {formatDate(item.completedAt)}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400">
              No items in this checklist
            </div>
          )}
        </div>
      )}
    </div>
  );
}
