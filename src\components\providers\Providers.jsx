"use client";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { ThemeProvider } from "@/components/providers/ThemeProvider";
import { AuthProvider } from "./AuthContext";
import { RefreshProvider } from "@/hooks/useStatsRefresh";
import { Toaster } from 'react-hot-toast';

export default function Providers({ children }) {
  const queryClient = new QueryClient();

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <ThemeProvider>
          <RefreshProvider>
            <Toaster position="top-center" toastOptions={{
              duration: 5000,
              style: {
                border: '1px solid #71717A'
              }
            }} />
            {children}
          </RefreshProvider>
        </ThemeProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}
