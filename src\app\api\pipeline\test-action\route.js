import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma/client';

export async function GET() {
  try {
    // Get the first pipeline stage
    const stage = await prisma.pipelineStage.findFirst();
    
    if (!stage) {
      return NextResponse.json(
        { error: 'No pipeline stages found' },
        { status: 404 }
      );
    }
    
    // Try to create a test action
    const action = await prisma.stageAction.create({
      data: {
        actionType: 'activity',
        actionDetails: {
          type: 'call',
          description: 'Test action',
          date: new Date().toISOString()
        },
        stageId: stage.id
      }
    });
    
    return NextResponse.json({
      success: true,
      action,
      stage
    });
  } catch (error) {
    console.error('Test action creation failed:', error);
    return NextResponse.json(
      { 
        error: 'Test action creation failed: ' + error.message,
        stack: error.stack
      },
      { status: 500 }
    );
  }
}
