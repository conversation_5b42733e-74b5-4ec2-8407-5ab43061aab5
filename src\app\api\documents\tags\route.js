import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';

export async function GET(request) {
  try {
    const { user } = await getSignedInUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Fetch Document tags from the Tag table
    const documentTags = await prisma.tag.findMany({
      where: {
        type: 'Document'
      },
      select: {
        name: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Extract just the tag names
    const tags = documentTags.map(tag => tag.name);

    return NextResponse.json({ tags });
  } catch (error) {
    console.error('Error fetching document tags:', error);
    return NextResponse.json(
      { error: 'Failed to fetch document tags' },
      { status: 500 }
    );
  }
}
