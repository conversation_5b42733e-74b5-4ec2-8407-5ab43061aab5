import { NextResponse } from 'next/server';
import { getSignedInUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma/client';

// GET /api/admin/overview/contacts - Get detailed contact lists for drill-down
export async function GET(request) {
  try {
    // Check authentication and authorization
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // dormant, high-risk, stuck, high-value
    const stageId = searchParams.get('stageId');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '20');
    const sortBy = searchParams.get('sortBy') || 'lastActivityDate';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    const skip = (page - 1) * pageSize;

    let whereClause = {};
    let include = {
      contact: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          company: true,
          pipelineStage: true,
          createdAt: true,
          lastContactDate: true
        }
      }
    };

    // Build where clause based on type
    switch (type) {
      case 'dormant':
        whereClause = { isDormant: true };
        break;
      case 'high-risk':
        whereClause = { riskLevel: 'high' };
        break;
      case 'stuck':
        whereClause = { 
          currentStageDays: { gt: 60 },
          contact: { pipelineStage: { not: null } }
        };
        break;
      case 'high-value':
        whereClause = { isHighValue: true };
        break;
      case 'stage':
        if (stageId) {
          whereClause = { contact: { pipelineStage: stageId } };
        }
        break;
      default:
        // Return all contacts with metrics
        whereClause = {};
    }

    // Get total count
    const total = await prisma.contactLifecycleMetrics.count({
      where: whereClause
    });

    // Get contacts with pagination
    const contacts = await prisma.contactLifecycleMetrics.findMany({
      where: whereClause,
      include,
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: pageSize
    });

    // Format response
    const formattedContacts = contacts.map(item => ({
      id: item.contact.id,
      name: `${item.contact.firstName} ${item.contact.lastName}`.trim(),
      email: item.contact.email,
      phone: item.contact.phone,
      company: item.contact.company,
      pipelineStage: item.contact.pipelineStage,
      createdAt: item.contact.createdAt,
      lastContactDate: item.contact.lastContactDate,
      
      // Metrics
      engagementScore: item.engagementScore,
      totalActivities: item.totalActivities,
      lastActivityDate: item.lastActivityDate,
      daysInPipeline: item.daysInPipeline,
      currentStageDays: item.currentStageDays,
      riskLevel: item.riskLevel,
      isDormant: item.isDormant,
      isHighValue: item.isHighValue,
      conversionProbability: item.conversionProbability,
      activityFrequency: item.activityFrequency
    }));

    return NextResponse.json({
      contacts: formattedContacts,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      },
      filters: {
        type,
        stageId,
        sortBy,
        sortOrder
      }
    });

  } catch (error) {
    console.error('Error fetching overview contacts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contacts' },
      { status: 500 }
    );
  }
}
