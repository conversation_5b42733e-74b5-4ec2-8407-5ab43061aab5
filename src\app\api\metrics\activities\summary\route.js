import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma/client';

export async function GET() {
  try {
    // Get the current date
    const today = new Date();
    
    // Calculate date ranges
    const startOfToday = new Date(today);
    startOfToday.setHours(0, 0, 0, 0);
    
    const startOfYesterday = new Date(today);
    startOfYesterday.setDate(today.getDate() - 1);
    startOfYesterday.setHours(0, 0, 0, 0);
    
    const endOfYesterday = new Date(startOfToday);
    endOfYesterday.setMilliseconds(-1);
    
    const startOfThisWeek = new Date(today);
    startOfThisWeek.setDate(today.getDate() - today.getDay()); // Start of week (Sunday)
    startOfThisWeek.setHours(0, 0, 0, 0);
    
    const startOfLastWeek = new Date(startOfThisWeek);
    startOfLastWeek.setDate(startOfLastWeek.getDate() - 7);
    
    const endOfLastWeek = new Date(startOfThisWeek);
    endOfLastWeek.setMilliseconds(-1);
    
    const startOfThisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    
    const endOfLastMonth = new Date(startOfThisMonth);
    endOfLastMonth.setMilliseconds(-1);
    
    // Get activity counts for different time periods
    const [
      todayCount,
      yesterdayCount,
      thisWeekCount,
      lastWeekCount,
      thisMonthCount,
      lastMonthCount,
      totalCount,
      activityTypes
    ] = await Promise.all([
      // Today's activities
      prisma.activity.count({
        where: {
          date: {
            gte: startOfToday
          }
        }
      }),
      
      // Yesterday's activities
      prisma.activity.count({
        where: {
          date: {
            gte: startOfYesterday,
            lt: startOfToday
          }
        }
      }),
      
      // This week's activities
      prisma.activity.count({
        where: {
          date: {
            gte: startOfThisWeek
          }
        }
      }),
      
      // Last week's activities
      prisma.activity.count({
        where: {
          date: {
            gte: startOfLastWeek,
            lt: startOfThisWeek
          }
        }
      }),
      
      // This month's activities
      prisma.activity.count({
        where: {
          date: {
            gte: startOfThisMonth
          }
        }
      }),
      
      // Last month's activities
      prisma.activity.count({
        where: {
          date: {
            gte: startOfLastMonth,
            lt: startOfThisMonth
          }
        }
      }),
      
      // Total activities
      prisma.activity.count(),
      
      // Activity types breakdown
      prisma.activity.groupBy({
        by: ['type'],
        _count: {
          id: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        }
      })
    ]);
    
    // Calculate trends
    const weeklyTrend = lastWeekCount > 0 
      ? Math.round(((thisWeekCount - lastWeekCount) / lastWeekCount) * 100) 
      : 0;
    
    const monthlyTrend = lastMonthCount > 0 
      ? Math.round(((thisMonthCount - lastMonthCount) / lastMonthCount) * 100) 
      : 0;
    
    // Format activity types
    const activityTypeBreakdown = activityTypes.map(item => ({
      type: item.type || 'other',
      count: item._count.id,
      percentage: totalCount > 0 ? Math.round((item._count.id / totalCount) * 100) : 0
    }));
    
    // Return the summary data
    return NextResponse.json({
      today: todayCount,
      yesterday: yesterdayCount,
      thisWeek: thisWeekCount,
      lastWeek: lastWeekCount,
      thisMonth: thisMonthCount,
      lastMonth: lastMonthCount,
      total: totalCount,
      weeklyTrend,
      monthlyTrend,
      activityTypes: activityTypeBreakdown
    });
  } catch (error) {
    console.error('Failed to fetch activity summary:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activity summary' },
      { status: 500 }
    );
  }
}
