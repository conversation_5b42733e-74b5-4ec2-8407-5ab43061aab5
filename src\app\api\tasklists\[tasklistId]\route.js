import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { NextResponse } from 'next/server';

// PATCH: Update a tasklist
export async function PATCH(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { tasklistId } = await params;
    const data = await request.json();
    // Only allow access to user's own tasklists unless admin
    const where = user.role === 'admin' ? { id: tasklistId } : { id: tasklistId, userId: user.id };
    const tasklist = await prisma.tasklist.findFirst({ where });
    if (!tasklist) {
      return NextResponse.json({ error: 'Tasklist not found' }, { status: 404 });
    }
    const updated = await prisma.tasklist.update({
      where: { id: tasklistId },
      data,
    });
    return NextResponse.json(updated);
  } catch (error) {
    console.error('Failed to update tasklist:', error);
    return NextResponse.json({ error: 'Failed to update tasklist' }, { status: 500 });
  }
}

// DELETE: Delete a tasklist
export async function DELETE(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { tasklistId } = await params;
    // Only allow access to user's own tasklists unless admin
    const where = user.role === 'admin' ? { id: tasklistId } : { id: tasklistId, userId: user.id };
    const tasklist = await prisma.tasklist.findFirst({ where });
    if (!tasklist) {
      return NextResponse.json({ error: 'Tasklist not found' }, { status: 404 });
    }
    await prisma.tasklist.delete({ where: { id: tasklistId } });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete tasklist:', error);
    return NextResponse.json({ error: 'Failed to delete tasklist' }, { status: 500 });
  }
}
