'use client';

import Link from 'next/link';
import { CheckSquare } from 'lucide-react';
import { useRef, useEffect, useState } from 'react';
import { draggable } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import { setCustomNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview';
import { pointerOutsideOfPreview } from '@atlaskit/pragmatic-drag-and-drop/element/pointer-outside-of-preview';
import ReactDOM from 'react-dom';

export default function PragmaticContactCard({
  contact,
  isMultiSelectMode = false,
  isSelected = false,
  onSelect
}) {
  const cardRef = useRef(null);
  const dragHandleRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [previewContainer, setPreviewContainer] = useState(null);

  // Set up draggable
  useEffect(() => {
    if (!cardRef.current || !dragHandleRef.current || isMultiSelectMode) return;

    console.log(`Setting up draggable for contact ${contact.id}`);
    
    const cleanup = draggable({
      element: cardRef.current,
      dragHandle: dragHandleRef.current,
      getInitialData: () => ({ 
        contactId: contact.id,
        type: 'contact-card',
        contact: {
          id: contact.id,
          firstName: contact.firstName,
          lastName: contact.lastName,
          email: contact.email,
          phone: contact.phone,
          company: contact.company,
          pipelineStage: contact.pipelineStage
        }
      }),
      onGenerateDragPreview({ nativeSetDragImage }) {
        setCustomNativeDragPreview({
          nativeSetDragImage,
          getOffset: pointerOutsideOfPreview({
            x: 16,
            y: 8,
          }),
          render({ container }) {
            setPreviewContainer(container);
            return () => setIsDragging(true);
          },
        });
      },
      onDragStart: () => {
        console.log(`Started dragging contact ${contact.id}`);
        setIsDragging(true);
        // Add a class to the body to indicate dragging is in progress
        document.body.classList.add('is-dragging-contact');
      },
      onDrop: () => {
        console.log(`Dropped contact ${contact.id}`);
        setIsDragging(false);
        setPreviewContainer(null);
        // Remove the class from the body
        document.body.classList.remove('is-dragging-contact');
      }
    });

    return () => {
      console.log(`Cleaning up draggable for contact ${contact.id}`);
      if (typeof cleanup === 'function') {
        cleanup();
      } else if (cleanup && typeof cleanup.cleanup === 'function') {
        cleanup.cleanup();
      }
    };
  }, [contact.id, isMultiSelectMode]);

  // Check if contact is valid
  if (!contact || !contact.id) {
    console.error('Invalid contact:', contact);
    return <div className="p-3 text-red-500">Invalid contact data</div>;
  }

  const handleSelectClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onSelect) {
      onSelect(contact.id);
    }
  };

  return (
    <div
      ref={cardRef}
      className={`pipeline-card card-light dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600
        ${isDragging ? 'pipeline-card-dragging ring-2 ring-primary shadow-md' : 'hover:shadow-md hover:border-primary/30 dark:hover:border-primary/30'}
        ${isSelected ? 'ring-2 ring-primary border-primary bg-primary/5 dark:bg-primary/10' : ''}
        ${isMultiSelectMode ? 'cursor-pointer' : ''}
        transition-all duration-200`}
      data-is-dragging={isDragging ? 'true' : 'false'}
      data-is-selected={isSelected ? 'true' : 'false'}
      data-contact-id={contact.id}
      onClick={isMultiSelectMode ? handleSelectClick : undefined}
    >
      {/* Drag Handle */}
      <div className={`flex items-center border-b border-gray-200 dark:border-gray-600 p-2 rounded-t-lg ${isSelected ? 'bg-primary/10 dark:bg-primary/20' : 'bg-gray-50 dark:bg-gray-800/80'}`}>
        {isMultiSelectMode ? (
          <button
            onClick={handleSelectClick}
            className={`p-1 mr-2 rounded-md ${
              isSelected
                ? 'bg-primary text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300'
            }`}
            title={isSelected ? "Deselect contact" : "Select contact"}
          >
            <CheckSquare className="h-4 w-4" />
          </button>
        ) : (
          <div
            ref={dragHandleRef}
            className="drag-handle p-1 mr-2 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md"
            title="Drag to move contact"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor" stroke="none">
              <circle cx="9" cy="5" r="1.5"></circle>
              <circle cx="9" cy="12" r="1.5"></circle>
              <circle cx="9" cy="19" r="1.5"></circle>
              <circle cx="15" cy="5" r="1.5"></circle>
              <circle cx="15" cy="12" r="1.5"></circle>
              <circle cx="15" cy="19" r="1.5"></circle>
            </svg>
          </div>
        )}
        <Link href={`/contacts/${contact.id}`} className="flex-1 font-medium text-gray-900 dark:text-white hover:text-primary dark:hover:text-primary-light truncate">
          {contact.firstName} {contact.lastName}
        </Link>
      </div>

      <div className="p-3 pt-2">
        {contact.company && (
          <div className="text-sm text-gray-600 dark:text-gray-300 mb-1 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            <span className="truncate">{contact.company}</span>
          </div>
        )}
        {contact.email && (
          <div className="text-sm text-primary dark:text-primary-light flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span className="truncate">{contact.email}</span>
          </div>
        )}
        {contact.phone && (
          <div className="text-sm text-gray-600 dark:text-gray-300 mt-1 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            <span className="truncate">{contact.phone}</span>
          </div>
        )}
      </div>
      
      {/* Drag preview */}
      {previewContainer && ReactDOM.createPortal(
        <div className="contact-drag-preview">
          {contact.firstName} {contact.lastName}
        </div>,
        previewContainer
      )}
    </div>
  );
}
