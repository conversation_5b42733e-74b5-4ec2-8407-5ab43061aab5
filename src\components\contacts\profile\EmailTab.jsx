"use client";

import MetricCard from "@/components/ui/emails/MetricCard";
import Tooltip from "@/components/ui/Tooltip";
import { useQuery } from "@tanstack/react-query";
import {
  Calendar,
  Clock,
  Users,
  ArrowLeft,
  Mail,
  MousePointer,
  AlertCircle,
  Info,
  Send,
  Filter,
  CheckCircle2,
  X,
  Search,
} from "lucide-react";
import { useParams } from "next/navigation";
import { useState, useEffect } from "react";
import { Bar, Line } from "react-chartjs-2";

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend
);

export default function EmailTab({ contactId }) {
  const [loading, setLoading] = useState(true);
  const [range, setRange] = useState("30d");
  const [emails, setEmails] = useState([]);

  // Date range options
  const dateRanges = [
    { value: "7d", label: "Last 7 days" },
    { value: "30d", label: "Last 30 days" },
    { value: "90d", label: "Last 3 months" },
    { value: "180d", label: "Last 6 months" },
    { value: "1y", label: "Last year" },
    { value: "all", label: "All time" },
  ];

  const { data: analytics, refetch: refetchAnalytics } = useQuery({
    queryKey: ["contact-analytics", contactId, range],
    queryFn: async () => {
      const response = await fetch(
        `/api/contacts/${contactId}/analytics?range=${range}`
      );
      if (!response.ok) throw new Error("Failed to fetch analytics");
      return response.json();
    },
    enabled: !!contactId,
  });

  const engagementData = {
    labels: analytics?.dailyStats?.map((stat) =>
      new Date(stat.date).toLocaleDateString()
    ) || ["Day 1", "Day 2", "Day 3", "Day 4", "Day 5", "Day 6"],
    datasets: [
      {
        label: "Opens",
        data: analytics?.dailyStats?.map((stat) => stat.opens) || [
          65.2, 70.8, 68.5, 75.3, 80.1, 82.7,
        ],
        borderColor: "#10B981",
        backgroundColor: "#10B981",
        tension: 0.4,
      },
      {
        label: "Clicks",
        data: analytics?.dailyStats?.map((stat) => stat.clicks) || [
          35.4, 42.1, 38.9, 45.6, 50.2, 48.8,
        ],
        borderColor: "#F59E0B",
        backgroundColor: "#F59E0B",
        tension: 0.4,
      },
    ],
  };

  const engagementOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        grid: { color: "rgba(156, 163, 175, 0.1)" },
      },
      x: {
        grid: { display: false },
      },
    },
    plugins: {
      legend: {
        position: "top",
      },
    },
  };

  const deliveryData = {
    labels: ["Delivered", "Opened", "Clicked", "Bounced"],
    datasets: [
      {
        label: "Email Statistics",
        data: [
          analytics?.delivered || 15,
          analytics?.opened || 13,
          analytics?.clicked || 5,
          analytics?.bounced || 1,
        ],
        backgroundColor: ["#4F46E5", "#10B981", "#F59E0B", "#EF4444"],
        borderRadius: 4,
      },
    ],
  };

  const deliveryOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        grid: { color: "rgba(156, 163, 175, 0.1)" },
      },
    },
    plugins: {
      legend: {
        display: false,
      },
    },
  };

  // Additional chart configurations
  const deviceBreakdownData = {
    labels: ["Mobile", "Desktop", "Tablet"],
    datasets: [
      {
        label: "Device Usage",
        data: [
          analytics?.deviceBreakdown?.mobile || 21,
          analytics?.deviceBreakdown?.desktop || 50,
          analytics?.deviceBreakdown?.tablet || 10,
        ],
        backgroundColor: ["#4F46E5", "#10B981", "#F59E0B"],
        borderRadius: 4,
      },
    ],
  };

  const locationData = {
    labels: analytics?.topLocations?.map((loc) => loc.location) || ["U.S."],
    datasets: [
      {
        label: "Location Distribution",
        data: analytics?.topLocations?.map((loc) => loc.percentage) || [1000],
        backgroundColor: [
          "#4F46E5",
          "#10B981",
          "#F59E0B",
          "#8B5CF6",
          "#EC4899",
        ],
        borderRadius: 4,
      },
    ],
  };

  useEffect(() => {
    const fetchEmails = async () => {
      try {
        const response = await fetch(
          `/api/contacts/${contactId}/emails?range=${range}`
        );
        if (response.ok) {
          const data = await response.json();
          setEmails(data);
        }
      } catch (error) {
        console.error("Failed to fetch emails:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEmails();
  }, [contactId, range]);

  const formatPercentage = (value) => {
    if (!value || isNaN(value)) return "0%";
    return `${(value * 100).toFixed(1)}%`;
  };

  const handleRangeChange = (newRange) => {
    setRange(newRange);
    setLoading(true);
  };

  const getCurrentRangeLabel = () => {
    return dateRanges.find((r) => r.value === range)?.label || "All time";
  };

  if (loading) return <div>Loading emails...</div>;

  return (
    <div className="space-y-4">
      {/* Header with Date Range Selector */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Email Analytics
          </h3>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <select
              value={range}
              onChange={(e) => handleRangeChange(e.target.value)}
              className="px-3 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {dateRanges.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
        <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover">
          Compose Email
        </button>
      </div>

      {/* Analytics Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
        {/* Current Range Indicator */}
        <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
          Showing data for:{" "}
          <span className="font-medium">{getCurrentRangeLabel()}</span>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-[#4F46E5]/10 dark:bg-[#4F46E5]/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Emails Sent</h3>
              <Tooltip content="Total number of emails delivered to this contact">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Emails Sent"
              value={analytics?.delivered || 0}
              trend={0}
              icon={Send}
            />
          </div>

          <div className="bg-[#10B981]/10 dark:bg-[#10B981]/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Open Rate</h3>
              <Tooltip content="Percentage of emails opened by this contact">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Open Rate"
              value={formatPercentage(analytics?.openRate)}
              trend={0}
              icon={Mail}
              subtitle={`${analytics?.uniqueOpens || 0} unique opens`}
            />
          </div>

          <div className="bg-[#F59E0B]/10 dark:bg-[#F59E0B]/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Click Rate</h3>
              <Tooltip content="Percentage of emails clicked by this contact">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Click Rate"
              value={formatPercentage(analytics?.clickRate)}
              trend={0}
              icon={MousePointer}
              subtitle={`${analytics?.uniqueClicks || 0} unique clicks`}
            />
          </div>

          <div className="bg-red-500/10 dark:bg-red-500/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Bounce Rate</h3>
              <Tooltip content="Percentage of emails that bounced">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Bounce Rate"
              value={formatPercentage(analytics?.bounceRate)}
              trend={0}
              icon={AlertCircle}
              trendReversed
            />
          </div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium mb-4">Engagement Over Time</h3>
            <div className="h-[300px]">
              <Line data={engagementData} options={engagementOptions} />
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium mb-4">Delivery Statistics</h3>
            <div className="h-[300px]">
              <Bar data={deliveryData} options={deliveryOptions} />
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium mb-4">Device Breakdown</h3>
            <div className="h-[300px]">
              <Bar data={deviceBreakdownData} options={deliveryOptions} />
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium mb-4">Top Locations</h3>
            <div className="h-[300px]">
              <Bar data={locationData} options={deliveryOptions} />
            </div>
          </div>
        </div>
      </div>

      {/* Email History Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Email History ({getCurrentRangeLabel()})
        </h3>

        <div className="space-y-4">
          {emails.map((email) => (
            <div
              key={email.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"
            >
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {email.subject}
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {new Date(email.sentAt).toLocaleString()}
                    </p>
                  </div>
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      email.status === "sent"
                        ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                        : email.status === "delivered"
                        ? "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100"
                        : email.status === "opened"
                        ? "bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100"
                        : email.status === "clicked"
                        ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                        : email.status === "bounced"
                        ? "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                        : "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
                    }`}
                  >
                    {email.status}
                  </span>
                </div>
                <div className="mt-2">
                  <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                    {email.preview}
                  </p>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 flex justify-end space-x-3">
                <button className="text-sm text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                  View Full Email
                </button>
                <button className="text-sm text-primary hover:text-primary-hover">
                  Reply
                </button>
              </div>
            </div>
          ))}
          {emails.length === 0 && (
            <div className="text-center text-gray-500 dark:text-gray-400 py-8">
              No email history for {getCurrentRangeLabel().toLowerCase()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
