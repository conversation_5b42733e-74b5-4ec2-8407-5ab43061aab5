import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser } from "@/lib/auth";
import { auditLogger } from "@/lib/services/auditLogger";

export async function GET(request, { params }) {
  try {
    let { id: contactID } = await params;
    console.log("Activities API called with params:", contactID);
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get("type");
    const sortBy = searchParams.get("sortBy") || "date";
    const sortOrder = searchParams.get("sortOrder") || "desc";
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const search = searchParams.get("search");
    const skip = (page - 1) * pageSize;

    console.log("Search params:", {
      type,
      sortBy,
      sortOrder,
      page,
      pageSize,
      search,
    });

    // Build the where clause
    const where = { contactId: contactID };
    if (type) where.type = type;

    // Add search functionality
    if (search && search.trim() !== "") {
      try {
        const searchTerm = search.trim();
        console.log("Processing search term:", searchTerm);

        // Use Prisma's search capabilities
        where.OR = [
          { title: { contains: searchTerm } },
          { description: { contains: searchTerm } },
        ];

        console.log("Added search criteria to query:", where);
      } catch (searchError) {
        console.error("Error adding search criteria:", searchError);
        // If search fails, continue without search
      }
    }

    // Build the orderBy clause
    const orderBy = {};
    orderBy[sortBy] = sortOrder;

    // Log the query parameters
    console.log("Prisma query:", { where, orderBy, skip, take: pageSize });

    // Get total count for pagination
    const totalCount = await prisma.activity.count({ where });

    // Get activities with pagination
    const activities = await prisma.activity.findMany({
      where,
      orderBy,
      skip,
      take: pageSize,
    });

    console.log(
      `Found ${activities.length} activities out of ${totalCount} total`
    );

    return NextResponse.json({
      activities,
      pagination: {
        total: totalCount,
        page,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize),
      },
    });
  } catch (error) {
    console.error("Failed to fetch activities:", error);
    return NextResponse.json(
      { error: "Failed to fetch activities" },
      { status: 500 }
    );
  }
}

export async function POST(request, { params }) {
  try {
    let { id: contactID } = await params;
    const body = await request.json();

    // Before creating, verify the contact exists and is accessible
    let whereClause = { id: String(contactID) };
    // If not admin, restrict to user's own contacts
    let user;
    try {
      ({ user } = await getSignedInUser(request));
    } catch {}
    if (user && user.role !== "admin") {
      whereClause.userId = String(user.id);
    }
    const contact = await prisma.contact.findFirst({ where: whereClause });
    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    // Handle different activity types
    if (body.type === 'task') {
      // For tasks, we need to create a TasklistItem and an Activity record
      // First, find or create a default tasklist for this contact
      let tasklist = await prisma.tasklist.findFirst({
        where: {
          contactId: contactID,
          title: 'General Tasks'
        }
      });

      if (!tasklist) {
        tasklist = await prisma.tasklist.create({
          data: {
            title: 'General Tasks',
            description: 'Default tasklist for contact activities',
            contactId: contactID,
            status: 'in_progress'
          }
        });
      }

      // Create the task item
      const taskItem = await prisma.tasklistItem.create({
        data: {
          title: body.title || 'Untitled Task',
          description: body.description || '',
          dueDate: body.date ? new Date(`${body.date}T12:00:00Z`) : new Date(),
          priority: body.priority || 'medium',
          tasklistId: tasklist.id,
          completed: false
        }
      });

      // Create the activity record
      const activity = await prisma.activity.create({
        data: {
          type: body.type,
          description: body.description || `Task: ${body.title}`,
          contactId: contactID,
          date: body.date ? new Date(`${body.date}T12:00:00Z`) : new Date(),
          createdAt: new Date(),
        },
      });

      // Update the contact's lastContactDate
      await prisma.contact.update({
        where: { id: contactID },
        data: { lastContactDate: new Date() },
      });

      // Log the activity creation
      await auditLogger.logActivityCreate({
        userId: user?.id,
        activity,
        request
      });

      return NextResponse.json({ activity, taskItem });

    } else if (body.type === 'note') {
      // For notes, create a Note record and an Activity record
      const note = await prisma.note.create({
        data: {
          title: body.title || 'Untitled Note',
          content: body.content || body.description || '',
          contactId: contactID
        }
      });

      // Create the activity record
      const activity = await prisma.activity.create({
        data: {
          type: body.type,
          description: body.description || body.content || body.title,
          contactId: contactID,
          date: body.date ? new Date(`${body.date}T12:00:00Z`) : new Date(),
          createdAt: new Date(),
        },
      });

      // Update the contact's lastContactDate
      await prisma.contact.update({
        where: { id: contactID },
        data: { lastContactDate: new Date() },
      });

      // Log the activity creation
      await auditLogger.logActivityCreate({
        userId: user?.id,
        activity,
        request
      });

      return NextResponse.json({ activity, note });

    } else {
      // For regular activities (call, meeting, email, etc.)
      const activity = await prisma.activity.create({
        data: {
          type: body.type,
          description: body.description,
          contactId: contactID,
          date: body.date ? new Date(`${body.date}T12:00:00Z`) : new Date(),
          createdAt: new Date(),
        },
      });

      // Update the contact's lastContactDate
      await prisma.contact.update({
        where: { id: contactID },
        data: { lastContactDate: new Date() },
      });

      // Log the activity creation
      await auditLogger.logActivityCreate({
        userId: user?.id,
        activity,
        request
      });

      return NextResponse.json(activity);
    }

  } catch (error) {
    console.error("Failed to create activity:", error);
    return NextResponse.json(
      { error: "Failed to create activity" },
      { status: 500 }
    );
  }
}

export async function DELETE(request, { params }) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const activityId = searchParams.get("activityId");

    if (!activityId) {
      return NextResponse.json(
        { error: "Activity ID is required" },
        { status: 400 }
      );
    }

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Get the activity before deleting for audit log
    const existingActivity = await prisma.activity.findUnique({
      where: { id: activityId }
    });

    if (!existingActivity) {
      return NextResponse.json(
        { error: "Activity not found" },
        { status: 404 }
      );
    }

    // Log the activity deletion
    await auditLogger.logActivityDelete({
      userId: user?.id,
      activityId,
      oldValues: existingActivity,
      request
    });

    await prisma.activity.delete({
      where: { id: activityId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete activity:", error);
    return NextResponse.json(
      { error: "Failed to delete activity" },
      { status: 500 }
    );
  }
}

export async function PATCH(request, { params }) {
  try {
    const body = await request.json();
    const { id, ...rawData } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Activity ID is required" },
        { status: 400 }
      );
    }

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Get the existing activity for audit log
    const existingActivity = await prisma.activity.findUnique({
      where: { id }
    });

    if (!existingActivity) {
      return NextResponse.json(
        { error: "Activity not found" },
        { status: 404 }
      );
    }

    // Only use valid Activity model fields
    const data = {};
    if (rawData.type) data.type = rawData.type;
    if (rawData.description) data.description = rawData.description;
    if (rawData.date) data.date = new Date(`${rawData.date}T12:00:00Z`);

    const activity = await prisma.activity.update({
      where: { id },
      data,
    });

    // Log the activity update
    await auditLogger.logActivityUpdate({
      userId: user?.id,
      activityId: id,
      oldValues: existingActivity,
      newValues: activity,
      request
    });

    return NextResponse.json(activity);
  } catch (error) {
    console.error("Failed to update activity:", error);
    return NextResponse.json(
      { error: "Failed to update activity" },
      { status: 500 }
    );
  }
}
