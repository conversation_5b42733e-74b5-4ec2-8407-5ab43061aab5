/**
 * Permission constants for the application
 * These are used to define and check permissions throughout the app
 */

// Contact permissions
export const READ_CONTACTS = 'read:contacts';
export const WRITE_CONTACTS = 'write:contacts';
export const DELETE_CONTACTS = 'delete:contacts';

// Group permissions
export const READ_GROUPS = 'read:groups';
export const WRITE_GROUPS = 'write:groups';
export const DELETE_GROUPS = 'delete:groups';

// Task permissions
export const READ_TASKS = 'read:tasks';
export const WRITE_TASKS = 'write:tasks';
export const DELETE_TASKS = 'delete:tasks';

// Note permissions
export const READ_NOTES = 'read:notes';
export const WRITE_NOTES = 'write:notes';
export const DELETE_NOTES = 'delete:notes';

// Document permissions
export const READ_DOCUMENTS = 'read:documents';
export const WRITE_DOCUMENTS = 'write:documents';
export const DELETE_DOCUMENTS = 'delete:documents';

// Pipeline permissions
export const READ_PIPELINE = 'read:pipeline';
export const WRITE_PIPELINE = 'write:pipeline';

// Metrics permissions
export const READ_METRICS = 'read:metrics';

// Email campaign permissions
export const READ_EMAIL_CAMPAIGNS = 'read:email_campaigns';
export const WRITE_EMAIL_CAMPAIGNS = 'write:email_campaigns';
export const SEND_EMAILS = 'send:emails';

// User roles
export const ROLE_ADMIN = 'admin';
export const ROLE_MANAGER = 'manager';
export const ROLE_USER = 'user';

// Permission sets for different roles
export const ADMIN_PERMISSIONS = [
  READ_CONTACTS, WRITE_CONTACTS, DELETE_CONTACTS,
  READ_GROUPS, WRITE_GROUPS, DELETE_GROUPS,
  READ_TASKS, WRITE_TASKS, DELETE_TASKS,
  READ_NOTES, WRITE_NOTES, DELETE_NOTES,
  READ_DOCUMENTS, WRITE_DOCUMENTS, DELETE_DOCUMENTS,
  READ_PIPELINE, WRITE_PIPELINE,
  READ_METRICS,
  READ_EMAIL_CAMPAIGNS, WRITE_EMAIL_CAMPAIGNS, SEND_EMAILS
];

export const MANAGER_PERMISSIONS = [
  READ_CONTACTS, WRITE_CONTACTS,
  READ_GROUPS, WRITE_GROUPS,
  READ_TASKS, WRITE_TASKS, DELETE_TASKS,
  READ_NOTES, WRITE_NOTES, DELETE_NOTES,
  READ_DOCUMENTS, WRITE_DOCUMENTS,
  READ_PIPELINE, WRITE_PIPELINE,
  READ_METRICS,
  READ_EMAIL_CAMPAIGNS, WRITE_EMAIL_CAMPAIGNS, SEND_EMAILS
];

export const USER_PERMISSIONS = [
  READ_CONTACTS, WRITE_CONTACTS,
  READ_GROUPS,
  READ_TASKS, WRITE_TASKS,
  READ_NOTES, WRITE_NOTES,
  READ_DOCUMENTS,
  READ_PIPELINE,
  READ_METRICS
];
