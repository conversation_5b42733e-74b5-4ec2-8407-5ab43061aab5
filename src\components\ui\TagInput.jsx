
import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { X, Plus } from 'lucide-react';


const TagInput = forwardRef(function TagInput({ value = [], onChange, suggestions = [], loading = false, disabled }, ref) {
  const [input, setInput] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef();

  useImperativeHandle(ref, () => ({
    isInputActive: () => document.activeElement === inputRef.current,
    hasInput: () => !!input.trim(),
    focus: () => inputRef.current && inputRef.current.focus(),
    clear: () => setInput(''),
  }), [input]);


  const addTag = (tag) => {
    if (!tag || value.includes(tag) || tag.includes(',')) return;
    onChange([...value, tag]);
    setInput('');
    setShowSuggestions(false);
  };


  const removeTag = (tag) => {
    onChange(value.filter(t => t !== tag));
  };



  const handleInputKeyDown = (e) => {
    if (e.key === 'Enter') {
      if (input.trim()) {
        addTag(input.trim());
        e.preventDefault();
        e.stopPropagation();
      }
      // If input is empty, allow form submit (do not preventDefault)
    } else if (e.key === 'Backspace' && !input && value.length) {
      removeTag(value[value.length - 1]);
    }
  };


  const handleSuggestionClick = (tag) => {
    addTag(tag);
  };

  // Filter suggestions locally
  const filteredSuggestions = (input.trim() === ''
    ? suggestions
    : suggestions.filter(s => s.toLowerCase().includes(input.trim().toLowerCase()))
  ).filter(s => !value.includes(s));


  // The main tag input container. If the + button is not visible, check for overflow/width issues in parent.
  return (
    <div className="flex flex-wrap items-center gap-1 border rounded px-2 py-1 bg-white dark:bg-gray-700 min-h-10 w-full max-w-full relative" style={{ minWidth: 220, overflow: 'visible' }}>
      {value.map(tag => (
        <span key={tag} className="flex items-center bg-primary/10 text-primary px-2 py-0.5 rounded-full text-xs mr-1 mb-1">
          {tag}
          <button
            type="button"
            className="ml-1 text-primary hover:text-red-500"
            onClick={() => removeTag(tag)}
            tabIndex={-1}
            disabled={disabled}
          >
            <X className="w-3 h-3" />
          </button>
        </span>
      ))}
      <input
        ref={inputRef}
        type="text"
        className="flex-1 min-w-[100px] max-w-full border-none outline-none bg-transparent text-xs py-1"
        value={input}
        onChange={e => setInput(e.target.value.replace(/,/g, ''))}
        onKeyDown={handleInputKeyDown}
        onFocus={() => setShowSuggestions(true)}
        onBlur={() => setTimeout(() => setShowSuggestions(false), 100)}
        placeholder="Add tag and press Enter"
        disabled={disabled}
      />
      <button
        type="button"
        className="ml-1 px-2 py-1 text-xs font-bold text-white bg-primary border-2 border-primary rounded-full flex items-center justify-center shadow-lg hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-all duration-150 z-10"
        style={{ minWidth: 32, minHeight: 32 }}
        onClick={() => input.trim() && addTag(input.trim())}
        tabIndex={0}
        disabled={disabled || !input.trim()}
        aria-label="Add tag"
      >
        <Plus className="w-5 h-5" />
      </button>
      {showSuggestions && (
        <div className="absolute z-10 mt-10 bg-white dark:bg-gray-800 border rounded shadow-lg w-60 max-h-40 overflow-y-auto">
          {loading ? (
            <div className="px-3 py-2 text-xs text-gray-500">Loading...</div>
          ) : filteredSuggestions.length > 0 ? (
            filteredSuggestions.map(s => (
              <div
                key={s}
                className="px-3 py-2 cursor-pointer hover:bg-primary/10 text-xs"
                onMouseDown={() => handleSuggestionClick(s)}
              >
                {s}
              </div>
            ))
          ) : (
            <div className="px-3 py-2 text-xs text-gray-400">No suggestions</div>
          )}
        </div>
      )}
    </div>
  );
});

export default TagInput;
