'use client';

import * as Dialog from '@radix-ui/react-dialog';
import React, { useState, useEffect, useRef } from 'react';
import { AnimatePresence } from 'framer-motion';
import StageActionForm from './StageActionForm';
import { getActionTypeById } from '@/lib/constants/stageActionTypes';
import PragmaticSortableStage from './PragmaticSortableStage';
import './pipeline-stages.css';

// Initialize mouse position tracking
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  window.lastMouseX = 0;
  window.lastMouseY = 0;

  if (!window.mouseMoveListenerAdded) {
    window.mouseMoveListenerAdded = true;
    document.addEventListener('mousemove', (e) => {
      window.lastMouseX = e.clientX;
      window.lastMouseY = e.clientY;
    });
    console.log('Added mouse position tracking for pipeline stages');
  }
}

export default function ManageStagesDialog({ stages, onSave, trigger }) {


  // Dialog state
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Stages management state
  const [stagesList, setStagesList] = useState(stages);
  const [newStageName, setNewStageName] = useState('');

  // Edit mode state
  const [editMode, setEditMode] = useState(null); // null or index of stage being edited
  const [editName, setEditName] = useState('');

  // Delete confirmation state
  const [confirmDelete, setConfirmDelete] = useState(null); // null or index of stage to confirm deletion
  const [contactsInStage, setContactsInStage] = useState(0);

  // Action management state
  const [stageActions, setStageActions] = useState({});
  const [isActionFormOpen, setIsActionFormOpen] = useState(false);
  const [currentStageForAction, setCurrentStageForAction] = useState(null);
  const [currentAction, setCurrentAction] = useState(null);
  const [isLoadingActions, setIsLoadingActions] = useState(false);

  // State for drag and drop
  const containerRef = useRef(null);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      // Reset stages management state
      setStagesList(stages);
      setNewStageName('');

      // Reset edit mode state
      setEditMode(null);
      setEditName('');

      // Reset error and delete confirmation state
      setError('');
      setConfirmDelete(null);

      // Reset action state
      setStageActions({});
      setIsActionFormOpen(false);
      setCurrentStageForAction(null);
      setCurrentAction(null);
      setIsLoadingActions(false);

      // No drag and drop state to reset
    }
  }, [isOpen, stages]);

  // Fetch actions for stages when dialog opens
  useEffect(() => {
    if (isOpen && stages.length > 0) {
      // Fetch actions for each stage
      stages.forEach(stage => {
        if (stage.id) {
          fetchStageActions(stage.id);
        }
      });
    }
  }, [isOpen, stages]);

  const handleAddStage = () => {
    if (!newStageName.trim()) {
      setError('Stage name cannot be empty');
      return;
    }

    // Check for duplicate names
    if (stagesList.some(stage => stage.name.toLowerCase() === newStageName.trim().toLowerCase())) {
      setError('A stage with this name already exists');
      return;
    }

    const newStage = {
      name: newStageName.trim(),
      order: stagesList.length + 1
    };

    setStagesList([...stagesList, newStage]);
    setNewStageName('');
    setError('');
  };

  const startEditStage = (index) => {
    setEditMode(index);
    setEditName(stagesList[index].name);
    setError('');
  };

  const cancelEditStage = () => {
    setEditMode(null);
    setEditName('');
    setError('');
  };

  const saveEditStage = async (index) => {
    if (!editName.trim()) {
      setError('Stage name cannot be empty');
      return;
    }

    // Check for duplicate names (excluding the current stage)
    if (stagesList.some((stage, i) => i !== index && stage.name.toLowerCase() === editName.trim().toLowerCase())) {
      setError('A stage with this name already exists');
      return;
    }

    const stage = stagesList[index];
    const newName = editName.trim();
    const oldName = stage.name;

    // Update the stage in our local state
    const newList = [...stagesList];
    newList[index] = { ...newList[index], name: newName };
    setStagesList(newList);
    setEditMode(null);
    setEditName('');
    setError('');

    // If this is an existing stage (has an ID), we need to update any contacts
    // that reference this stage. This will happen when we save all changes.
    if (stage.id) {
      console.log(`Stage name changed from "${oldName}" to "${newName}". Contacts will be updated when changes are saved.`);
    }
  };

  const handleMoveStage = (index, direction) => {
    // Add a temporary animation class to the stages being moved
    const animatedList = stagesList.map((stage, i) => ({
      ...stage,
      animating: i === index || i === index + direction,
      direction: i === index ? direction : i === index + direction ? -direction : 0
    }));

    // Apply animation state
    setStagesList(animatedList);

    // Use setTimeout to allow the animation to be visible
    setTimeout(() => {
      const newList = [...animatedList];
      const temp = newList[index];
      newList[index] = newList[index + direction];
      newList[index + direction] = temp;

      // Update orders and remove animation state
      newList.forEach((stage, i) => {
        stage.order = i + 1;
        stage.animating = false;
        stage.direction = 0;
      });

      setStagesList(newList);
      setError('');
    }, 300); // Short delay for the animation to be visible
  };

  /**
   * Handle stage reordering
   */
  const handleReorderStage = (sourceId, sourceIndex, targetId, targetIndex, position) => {
    if (editMode !== null || confirmDelete !== null) return;

    console.log(`Reordering stage from ${sourceIndex} to ${targetIndex} (${position})`);

    // Find the actual indices in our stagesList
    const actualSourceIndex = stagesList.findIndex(stage => (stage.id || `new-${stage.order-1}`) === sourceId);
    if (actualSourceIndex === -1) return;

    // Get the source stage
    const sourceStage = stagesList[actualSourceIndex];

    // Create a new array without the source stage
    const newList = stagesList.filter((_, index) => index !== actualSourceIndex);

    // Special case for dropping at the beginning of the list
    if (targetIndex === 0 && position === 'top') {
      // Insert at the beginning
      newList.unshift(sourceStage);

      // Update orders
      newList.forEach((stage, i) => {
        stage.order = i + 1;
      });

      // Update state
      setStagesList(newList);
      setError('');
      console.log(`Successfully moved stage from index ${actualSourceIndex} to the beginning of the list`);
      return;
    }

    // Special case for dropping at the end of the list
    if (targetIndex === stagesList.length - 1 && position === 'bottom') {
      // Insert at the end
      newList.push(sourceStage);

      // Update orders
      newList.forEach((stage, i) => {
        stage.order = i + 1;
      });

      // Update state
      setStagesList(newList);
      setError('');
      console.log(`Successfully moved stage from index ${actualSourceIndex} to the end of the list`);
      return;
    }

    // Normal case - dropping on another stage
    if (targetId) {
      const actualTargetIndex = stagesList.findIndex(stage => (stage.id || `new-${stage.order-1}`) === targetId);
      if (actualTargetIndex === -1 || actualSourceIndex === actualTargetIndex) return;

      // Calculate the actual insert index based on position
      let insertIndex = actualTargetIndex;
      if (position === 'bottom') {
        insertIndex += 1;
      }

      // Insert the stage at the calculated position
      newList.splice(insertIndex, 0, sourceStage);
    } else {
      // If no target ID, append to the end
      newList.push(sourceStage);
    }

    // Update orders
    newList.forEach((stage, i) => {
      stage.order = i + 1;
    });

    // Update state
    setStagesList(newList);
    setError('');
    console.log(`Successfully reordered stages`);
  };

  const startDeleteConfirmation = async (index) => {
    const stage = stagesList[index];

    // If it's a new stage (no ID), we can delete it immediately
    if (!stage.id) {
      handleRemoveStage(index);
      return;
    }

    // Check if there are contacts in this stage
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/pipeline/stages/${stage.id}/contacts-count`);
      const data = await response.json();

      setContactsInStage(data.count);

      if (data.count > 0) {
        // Show warning but still allow confirmation dialog
        setConfirmDelete(index);
      } else {
        // No contacts, can safely delete
        setConfirmDelete(index);
      }
    } catch (error) {
      console.error('Failed to check contacts in stage:', error);
      setError('Failed to check if stage can be deleted');
    } finally {
      setIsSubmitting(false);
    }
  };

  const cancelDeleteConfirmation = () => {
    setConfirmDelete(null);
    setContactsInStage(0);
  };

  const handleRemoveStage = (index) => {
    const newList = stagesList.filter((_, i) => i !== index);

    // Update orders
    newList.forEach((stage, i) => {
      stage.order = i + 1;
    });

    setStagesList(newList);
    setConfirmDelete(null);
    setContactsInStage(0);
    setError('');
  };

  // Fetch actions for a stage
  const fetchStageActions = async (stageId) => {
    if (!stageId) return;

    setIsLoadingActions(true);
    try {
      const response = await fetch(`/api/pipeline/stages/${stageId}/actions`);
      if (response.ok) {
        const actions = await response.json();
        setStageActions(prev => ({
          ...prev,
          [stageId]: actions
        }));
      }
    } catch (error) {
      console.error('Failed to fetch stage actions:', error);
    } finally {
      setIsLoadingActions(false);
    }
  };

  // Open the action form for a stage
  const handleAddAction = (stage) => {
    setCurrentStageForAction(stage);
    setCurrentAction(null);
    setIsActionFormOpen(true);
  };

  // Open the action form to edit an existing action
  const handleEditAction = (stage, action) => {
    setCurrentStageForAction(stage);
    setCurrentAction(action);
    setIsActionFormOpen(true);
  };

  // Handle action save success
  const handleActionSaved = () => {
    // Refresh the actions for this stage
    if (currentStageForAction) {
      fetchStageActions(currentStageForAction.id);
    }
  };

  // Delete an action
  const handleDeleteAction = async (stageId, actionId) => {
    if (!window.confirm('Are you sure you want to delete this action?')) {
      return;
    }

    try {
      const response = await fetch(`/api/pipeline/actions/${actionId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        // Remove the action from the state
        setStageActions(prev => {
          const updatedActions = prev[stageId].filter(a => a.id !== actionId);
          return {
            ...prev,
            [stageId]: updatedActions
          };
        });
      }
    } catch (error) {
      console.error('Failed to delete action:', error);
    }
  };

  const handleSave = async () => {
    if (stagesList.length === 0) {
      setError('You must have at least one pipeline stage');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      await onSave(stagesList);
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to save stages:', error);
      setError('Failed to save changes. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>
      <Dialog.Trigger asChild>
        {trigger}
      </Dialog.Trigger>

      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto overflow-x-hidden">
          <Dialog.Title className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Manage Pipeline Stages
          </Dialog.Title>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-200 rounded-md">
              {error}
            </div>
          )}

          <div className="space-y-4">
            <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Add New Stage</h3>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newStageName}
                  onChange={(e) => setNewStageName(e.target.value)}
                  placeholder="New stage name"
                  className="flex-1 rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <button
                  onClick={handleAddStage}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
                >
                  Add
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Current Stages</h3>
              {stagesList.length === 0 ? (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  No stages defined yet. Add your first stage above.
                </div>
              ) : (
                <div
                  ref={containerRef}
                  className="stages-container"
                  style={{ minHeight: '10px' }} /* Ensure there's always space for the droppable area */
                >
                  <AnimatePresence>
                    {/* Render all stages */}
                    {stagesList.map((stage, index) => {
                      const stageId = stage.id || `new-${index}`;
                      const stageActionsList = stage.id ? (stageActions[stage.id] || []) : [];

                      return (
                        <PragmaticSortableStage
                          key={stageId}
                          stage={stage}
                          index={index}
                          onReorder={handleReorderStage}
                          isEditing={editMode === index}
                          isConfirmingDelete={confirmDelete === index}
                          onEdit={() => startEditStage(index)}
                          onCancelEdit={cancelEditStage}
                          onSaveEdit={() => saveEditStage(index)}
                          onDelete={() => startDeleteConfirmation(index)}
                          onCancelDelete={cancelDeleteConfirmation}
                          onConfirmDelete={() => handleRemoveStage(index)}
                          onManageActions={() => handleAddAction(stage)}
                          editName={editName}
                          setEditName={setEditName}
                          contactsInStage={contactsInStage}
                          stageActions={stageActionsList}
                        />
                      );
                    })}
                  </AnimatePresence>
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <Dialog.Close asChild>
              <button
                className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                disabled={isSubmitting}
              >
                Cancel
              </button>
            </Dialog.Close>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </button>
          </div>

          {/* Stage Action Form */}
          <StageActionForm
            stageId={currentStageForAction?.id}
            stageName={currentStageForAction?.name}
            action={currentAction}
            isOpen={isActionFormOpen}
            onClose={() => setIsActionFormOpen(false)}
            onSuccess={handleActionSaved}
          />
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}