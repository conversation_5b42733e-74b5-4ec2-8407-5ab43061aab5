"use client";

import { useState, useEffect } from "react";
import ContactEditDialog from "../ContactEditDialog";


export default function ProfilePanel({ contact, onContactUpdated }) {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [pipelineStageName, setPipelineStageName] = useState("Not in pipeline");

  // Save handler that persists to API and updates parent state
  const handleSave = async (updatedContact) => {
    try {
      // Remove fields not in the Prisma model
      const { updated, created, ...safeContact } = updatedContact;
      console.log('[ProfilePanel] PATCH /api/contacts/' + safeContact.id, safeContact);
      const response = await fetch(`/api/contacts/${safeContact.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(safeContact),
      });
      if (response.ok) {
        const saved = await response.json();
        if (onContactUpdated) onContactUpdated(saved);
      } else {
        // Optionally show error
        console.error('Failed to update contact');
      }
    } catch (err) {
      console.error('Error updating contact:', err);
    }
  };

  // Fetch pipeline stage name when contact changes
  useEffect(() => {
    const fetchPipelineStageName = async () => {
      if (!contact.pipelineStage) {
        setPipelineStageName("Not in pipeline");
        return;
      }

      try {
        const response = await fetch(`/api/pipeline/stages/${contact.pipelineStage}`);
        if (response.ok) {
          const stage = await response.json();
          setPipelineStageName(stage.name);
        } else {
          setPipelineStageName("Unknown stage");
        }
      } catch (error) {
        console.error("Failed to fetch pipeline stage:", error);
        setPipelineStageName("Unknown stage");
      }
    };

    fetchPipelineStageName();
  }, [contact.pipelineStage]);

  return (
    <div className="card-light dark:bg-gray-800 rounded-lg h-full overflow-auto">
      <div className="px-3 py-3 lg:h-full">
        <div className="space-y-4">
          <div>
            <h3 className="text-base font-medium text-gray-900 dark:text-white mb-2">
              Contact Information
            </h3>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.firstName} {contact.lastName}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.email}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.phone}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Company</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.company}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Tags</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{(contact.tags && contact.tags.length > 0) ? contact.tags.join(', ') : <span className="text-gray-400">-</span>}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.created ? new Date(contact.created).toLocaleDateString() : <span className="text-gray-400">-</span>}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Updated</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.updated ? new Date(contact.updated).toLocaleDateString() : <span className="text-gray-400">-</span>}</dd>
              </div>
              {/* CRM fields, only if populated */}
              {contact.communicationPreferences && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Communication Preferences</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.communicationPreferences}</dd>
                </div>
              )}
              {contact.preferredLanguage && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Preferred Language</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.preferredLanguage}</dd>
                </div>
              )}
              {contact.referredBy && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Referred By</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.referredBy}</dd>
                </div>
              )}
              {contact.referralSource && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Referral Source</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.referralSource}</dd>
                </div>
              )}
              {contact.leadSource && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Lead Source</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.leadSource}</dd>
                </div>
              )}
              {contact.leadScore != null && contact.leadScore !== '' && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Lead Score</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.leadScore}</dd>
                </div>
              )}
              {contact.preferredContactMethod && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Preferred Contact Method</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.preferredContactMethod}</dd>
                </div>
              )}
              {contact.doNotContact != null && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Do Not Contact</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{contact.doNotContact ? 'Yes' : 'No'}</dd>
                </div>
              )}
            </dl>
          </div>

          <div>
            <h3 className="text-base font-medium text-gray-900 dark:text-white mb-2">
              Additional Details
            </h3>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Type
                </dt>
                <dd className="mt-1">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100">
                    {contact.type}
                  </span>
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Last Contact
                </dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  {contact.lastContactDate
                    ? new Date(contact.lastContactDate).toLocaleDateString()
                    : "Never"}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Pipeline Stage
                </dt>
                <dd className="mt-1">
                  {contact.pipelineStage ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100">
                      {pipelineStageName}
                    </span>
                  ) : (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      Not in pipeline
                    </span>
                  )}
                </dd>
              </div>
            </dl>
          </div>

          <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setIsEditModalOpen(true)}
              className="w-full px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
            >
              Edit Contact
            </button>
          </div>
        </div>
      </div>

      <ContactEditDialog
        contact={contact}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={handleSave}
      />
    </div>
  );
}
