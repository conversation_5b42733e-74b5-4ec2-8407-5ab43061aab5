// Microsoft Authentication Library (MSAL) configuration
export const msalConfig = {
  auth: {
    clientId: process.env.NEXT_PUBLIC_AZURE_AD_CLIENT_ID,
    authority: `https://login.microsoftonline.com/${process.env.NEXT_PUBLIC_AZURE_AD_TENANT_ID}`,
    clientSecret: process.env.AZURE_AD_CLIENT_SECRET,
    // redirectUri: typeof window !== "undefined" ? window.location.origin : "",
    redirectUri: "http://localhost:3000/api/auth/login",

    // redirectUri:
    //   process.env.NODE_ENV === "production"
    //     ? "https://slimcrm.com"
    //     : "http://localhost:3000",
    postLogoutRedirectUri:
      process.env.NODE_ENV === "production"
        ? "https://slimcrm.com"
        : "http://localhost:3000",
  },
  cache: {
    // cacheLocation: "localStorage",
    cacheLocation: "sessionStorage",
    storeAuthStateInCookie: true,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) {
          return;
        }
        switch (level) {
          case 0: // Error
            console.error(message);
            break;
          case 1: // Warning
            console.warn(message);
            break;
          case 2: // Info
            console.info(message);
            break;
          case 3: // Verbose
            console.debug(message);
            break;
        }
      },
      logLevel: 3, // Set to 3 during development for verbose logging, 0 for production
    },
  },
};

// Add here scopes for id token to be used at MS Identity Platform endpoints.
// The scopes needed for the application
export const loginRequest = {
  scopes: ["User.Read"],
};
