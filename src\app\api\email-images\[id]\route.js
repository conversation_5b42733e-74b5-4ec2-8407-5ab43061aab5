import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";

export async function GET(request, { params }) {
  try {
    const image = await prisma.emailImage.findUnique({
      where: { id: params.id },
    });

    if (!image) {
      return NextResponse.json({ error: "Image not found" }, { status: 404 });
    }

    // If the data is already in base64 format, extract the binary part
    let imageData;
    if (image.data.startsWith("data:")) {
      imageData = image.data.split(",")[1];
    } else {
      imageData = image.data;
    }

    const buffer = Buffer.from(imageData, "base64");

    return new NextResponse(buffer, {
      headers: {
        "Content-Type": image.contentType,
        "Cache-Control": "public, max-age=31536000",
      },
    });
  } catch (error) {
    console.error("Failed to fetch image:", error);
    return NextResponse.json(
      { error: "Failed to fetch image" },
      { status: 500 }
    );
  }
}
