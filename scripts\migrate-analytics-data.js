#!/usr/bin/env node

/**
 * Migration script to backfill ContactStageHistory and ContactLifecycleMetrics
 * from existing audit logs and contact data.
 * 
 * Run with: node scripts/migrate-analytics-data.js
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 Starting analytics data migration...');
  
  try {
    // Step 1: Backfill stage history from audit logs
    await backfillStageHistory();
    
    // Step 2: Initialize lifecycle metrics for all contacts
    await initializeLifecycleMetrics();
    
    // Step 3: Calculate initial metrics
    await calculateInitialMetrics();
    
    console.log('✅ Migration completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function backfillStageHistory() {
  console.log('📊 Backfilling stage history from audit logs...');
  
  // Get all stage change events from audit logs
  const stageChangeEvents = await prisma.auditLog.findMany({
    where: {
      eventType: 'CONTACT_STAGE_CHANGE'
    },
    orderBy: {
      createdAt: 'asc'
    }
  });
  
  console.log(`Found ${stageChangeEvents.length} stage change events`);
  
  // Group events by contact
  const contactStageChanges = {};
  
  for (const event of stageChangeEvents) {
    const contactId = event.entityId;
    if (!contactStageChanges[contactId]) {
      contactStageChanges[contactId] = [];
    }
    contactStageChanges[contactId].push(event);
  }
  
  // Process each contact's stage history
  for (const [contactId, events] of Object.entries(contactStageChanges)) {
    await processContactStageHistory(contactId, events);
  }
  
  // Handle contacts that are currently in stages but have no audit history
  await handleContactsWithoutHistory();
  
  console.log('✅ Stage history backfill completed');
}

async function processContactStageHistory(contactId, events) {
  console.log(`Processing stage history for contact ${contactId} (${events.length} events)`);
  
  let currentStage = null;
  let stageEntryTime = null;
  
  for (let i = 0; i < events.length; i++) {
    const event = events[i];
    const oldStage = event.oldValues?.pipelineStage;
    const newStage = event.newValues?.pipelineStage;
    const eventTime = event.createdAt;
    
    // If there was a previous stage, close it
    if (currentStage && oldStage) {
      const duration = Math.ceil((eventTime - stageEntryTime) / (1000 * 60 * 60 * 24));
      
      await prisma.contactStageHistory.create({
        data: {
          contactId,
          stageId: oldStage,
          enteredAt: stageEntryTime,
          exitedAt: eventTime,
          duration,
          userId: event.userId,
          createdAt: event.createdAt
        }
      });
    }
    
    // Start tracking the new stage
    if (newStage) {
      currentStage = newStage;
      stageEntryTime = eventTime;
    } else {
      currentStage = null;
      stageEntryTime = null;
    }
  }
  
  // If contact is currently in a stage, create an open record
  if (currentStage && stageEntryTime) {
    const contact = await prisma.contact.findUnique({
      where: { id: contactId },
      select: { pipelineStage: true }
    });
    
    if (contact?.pipelineStage === currentStage) {
      const now = new Date();
      const duration = Math.ceil((now - stageEntryTime) / (1000 * 60 * 60 * 24));
      
      await prisma.contactStageHistory.create({
        data: {
          contactId,
          stageId: currentStage,
          enteredAt: stageEntryTime,
          exitedAt: null, // Still in this stage
          duration: null, // Will be calculated when they exit
          userId: events[events.length - 1]?.userId,
          createdAt: stageEntryTime
        }
      });
    }
  }
}

async function handleContactsWithoutHistory() {
  console.log('🔍 Handling contacts without stage history...');
  
  // Find contacts that have a pipeline stage but no stage history
  const contactsWithStages = await prisma.contact.findMany({
    where: {
      pipelineStage: {
        not: null
      }
    },
    select: {
      id: true,
      pipelineStage: true,
      createdAt: true,
      stageHistory: true
    }
  });
  
  for (const contact of contactsWithStages) {
    if (contact.stageHistory.length === 0) {
      // Create a stage history entry starting from contact creation
      await prisma.contactStageHistory.create({
        data: {
          contactId: contact.id,
          stageId: contact.pipelineStage,
          enteredAt: contact.createdAt,
          exitedAt: null,
          duration: null,
          userId: null, // Unknown who set the initial stage
          createdAt: contact.createdAt
        }
      });
      
      console.log(`Created initial stage history for contact ${contact.id}`);
    }
  }
}

async function initializeLifecycleMetrics() {
  console.log('📈 Initializing lifecycle metrics...');
  
  const contacts = await prisma.contact.findMany({
    select: {
      id: true,
      createdAt: true,
      lastContactDate: true,
      lastActivityDate: true,
      pipelineStage: true,
      leadSource: true
    }
  });
  
  console.log(`Initializing metrics for ${contacts.length} contacts`);
  
  for (const contact of contacts) {
    // Check if metrics already exist
    const existingMetrics = await prisma.contactLifecycleMetrics.findUnique({
      where: { contactId: contact.id }
    });
    
    if (!existingMetrics) {
      await prisma.contactLifecycleMetrics.create({
        data: {
          contactId: contact.id,
          firstContactDate: contact.createdAt,
          lastActivityDate: contact.lastActivityDate,
          leadSource: contact.leadSource,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }
  }
  
  console.log('✅ Lifecycle metrics initialization completed');
}

async function calculateInitialMetrics() {
  console.log('🧮 Calculating initial metrics...');
  
  const contacts = await prisma.contact.findMany({
    include: {
      activities: true,
      stageHistory: true,
      lifecycleMetrics: true
    }
  });
  
  for (const contact of contacts) {
    if (!contact.lifecycleMetrics) continue;
    
    // Calculate basic metrics
    const totalActivities = contact.activities.length;
    const totalStageChanges = contact.stageHistory.length;
    
    // Calculate days in pipeline
    const firstStageEntry = contact.stageHistory
      .sort((a, b) => a.enteredAt - b.enteredAt)[0];
    const daysInPipeline = firstStageEntry 
      ? Math.ceil((new Date() - firstStageEntry.enteredAt) / (1000 * 60 * 60 * 24))
      : null;
    
    // Calculate current stage days
    const currentStageEntry = contact.stageHistory
      .filter(h => h.exitedAt === null)[0];
    const currentStageDays = currentStageEntry
      ? Math.ceil((new Date() - currentStageEntry.enteredAt) / (1000 * 60 * 60 * 24))
      : null;
    
    // Calculate engagement score (simple algorithm)
    const recentActivities = contact.activities.filter(
      a => a.createdAt > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length;
    const engagementScore = Math.min(100, (recentActivities * 10) + (totalActivities * 2));
    
    // Determine if dormant (no activity in 30 days)
    const lastActivity = contact.activities
      .sort((a, b) => b.createdAt - a.createdAt)[0];
    const isDormant = !lastActivity || 
      (new Date() - lastActivity.createdAt) > (30 * 24 * 60 * 60 * 1000);
    
    // Update metrics
    await prisma.contactLifecycleMetrics.update({
      where: { contactId: contact.id },
      data: {
        totalActivities,
        totalStageChanges,
        daysInPipeline,
        currentStageDays,
        engagementScore,
        isDormant,
        lastActivityDate: lastActivity?.createdAt,
        lastCalculated: new Date(),
        updatedAt: new Date()
      }
    });
  }
  
  console.log('✅ Initial metrics calculation completed');
}

// Run the migration
main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
