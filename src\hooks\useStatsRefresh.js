'use client';

import { useState, useCallback, useEffect, createContext, useContext } from 'react';

// Create a context for the refresh functionality
const RefreshContext = createContext(null);

// Create a simple event emitter
const listeners = new Set();

// Provider component
export function RefreshProvider({ children }) {
  // Function to trigger a refresh
  const triggerRefresh = useCallback((dataTypes) => {
    // Notify all listeners
    listeners.forEach(listener => listener(dataTypes));
  }, []);

  // Value to provide through context
  const value = { triggerRefresh };

  return (
    <RefreshContext.Provider value={value}>
      {children}
    </RefreshContext.Provider>
  );
}

// Hook to use the refresh functionality
export function useStatsRefresh() {
  const context = useContext(RefreshContext);
  if (!context) {
    throw new Error('useStatsRefresh must be used within a RefreshProvider');
  }
  return context;
}

// Hook to listen for refresh events
export function useStatsListener(dataTypesToWatch) {
  const [refreshCounter, setRefreshCounter] = useState(0);

  // Register a listener on mount and clean up on unmount
  useEffect(() => {
    const listener = (dataTypes) => {
      // If no specific data types are provided, or if any of the watched types are included
      if (!dataTypes || !dataTypes.length || !dataTypesToWatch) {
        // Refresh everything if no specific types are provided
        setRefreshCounter(prev => prev + 1);
      } else if (Array.isArray(dataTypesToWatch)) {
        // Check if any of the watched types are included in the triggered types
        const shouldRefresh = dataTypesToWatch.some(type => dataTypes.includes(type));
        if (shouldRefresh) {
          setRefreshCounter(prev => prev + 1);
        }
      }
    };

    listeners.add(listener);

    return () => {
      listeners.delete(listener);
    };
  }, [dataTypesToWatch]);

  return refreshCounter;
}
