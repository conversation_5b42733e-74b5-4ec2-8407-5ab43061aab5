import prisma from '../prisma/client';

class AuditLogger {
  // Helper method to extract request metadata
  _extractMetadata(request, additionalData = {}) {
    const ip = request?.headers?.get?.('x-forwarded-for') ||
               request?.headers?.get?.('x-real-ip') ||
               request?.ip ||
               'unknown';
    const userAgent = request?.headers?.get?.('user-agent') || 'unknown';

    return {
      ip,
      userAgent,
      timestamp: new Date().toISOString(),
      ...additionalData
    };
  }

  // Generic audit logging method
  async log({
    eventType,
    userId = null,
    entityType = null,
    entityId = null,
    oldValues = null,
    newValues = null,
    metadata = {},
    ip = null,
    userAgent = null,
    request = null
  }) {
    try {
      const finalMetadata = request ?
        this._extractMetadata(request, metadata) :
        { ...metadata, ip, userAgent, timestamp: new Date().toISOString() };

      await prisma.auditLog.create({
        data: {
          eventType,
          userId,
          entityType,
          entityId,
          oldValues: oldValues ? JSON.parse(JSON.stringify(oldValues)) : null,
          newValues: newValues ? JSON.parse(JSON.stringify(newValues)) : null,
          metadata: finalMetadata,
          ip: finalMetadata.ip,
          userAgent: finalMetadata.userAgent
        }
      });
    } catch (error) {
      console.error('Failed to create audit log:', error);
      // Don't throw - logging should not interrupt the main flow
    }
  }

  // Entity-specific logging methods
  async logContactCreate({ userId, contact, request }) {
    await this.log({
      eventType: 'CONTACT_CREATE',
      userId,
      entityType: 'Contact',
      entityId: contact.id,
      newValues: contact,
      request
    });
  }

  async logContactUpdate({ userId, contactId, oldValues, newValues, request }) {
    await this.log({
      eventType: 'CONTACT_UPDATE',
      userId,
      entityType: 'Contact',
      entityId: contactId,
      oldValues,
      newValues,
      request
    });
  }

  async logContactDelete({ userId, contactId, oldValues, request }) {
    await this.log({
      eventType: 'CONTACT_DELETE',
      userId,
      entityType: 'Contact',
      entityId: contactId,
      oldValues,
      request
    });
  }

  async logActivityCreate({ userId, activity, request }) {
    await this.log({
      eventType: 'ACTIVITY_CREATE',
      userId,
      entityType: 'Activity',
      entityId: activity.id,
      newValues: activity,
      request
    });
  }

  async logActivityUpdate({ userId, activityId, oldValues, newValues, request }) {
    await this.log({
      eventType: 'ACTIVITY_UPDATE',
      userId,
      entityType: 'Activity',
      entityId: activityId,
      oldValues,
      newValues,
      request
    });
  }

  async logActivityDelete({ userId, activityId, oldValues, request }) {
    await this.log({
      eventType: 'ACTIVITY_DELETE',
      userId,
      entityType: 'Activity',
      entityId: activityId,
      oldValues,
      request
    });
  }

  async logNoteCreate({ userId, note, request }) {
    await this.log({
      eventType: 'NOTE_CREATE',
      userId,
      entityType: 'Note',
      entityId: note.id,
      newValues: note,
      request
    });
  }

  async logNoteUpdate({ userId, noteId, oldValues, newValues, request }) {
    await this.log({
      eventType: 'NOTE_UPDATE',
      userId,
      entityType: 'Note',
      entityId: noteId,
      oldValues,
      newValues,
      request
    });
  }

  async logNoteDelete({ userId, noteId, oldValues, request }) {
    await this.log({
      eventType: 'NOTE_DELETE',
      userId,
      entityType: 'Note',
      entityId: noteId,
      oldValues,
      request
    });
  }

  async logDocumentCreate({ userId, document, request }) {
    await this.log({
      eventType: 'DOCUMENT_CREATE',
      userId,
      entityType: 'Document',
      entityId: document.id,
      newValues: document,
      request
    });
  }

  async logDocumentUpdate({ userId, documentId, oldValues, newValues, request }) {
    await this.log({
      eventType: 'DOCUMENT_UPDATE',
      userId,
      entityType: 'Document',
      entityId: documentId,
      oldValues,
      newValues,
      request
    });
  }

  async logDocumentDelete({ userId, documentId, oldValues, request }) {
    await this.log({
      eventType: 'DOCUMENT_DELETE',
      userId,
      entityType: 'Document',
      entityId: documentId,
      oldValues,
      request
    });
  }

  // Authentication logging
  async logLogin({ userId, request, metadata = {} }) {
    await this.log({
      eventType: 'USER_LOGIN',
      userId,
      entityType: 'User',
      entityId: userId,
      metadata,
      request
    });
  }

  async logLogout({ userId, request, metadata = {} }) {
    await this.log({
      eventType: 'USER_LOGOUT',
      userId,
      entityType: 'User',
      entityId: userId,
      metadata,
      request
    });
  }

  // User management logging
  async logUserCreate({ userId, user, request }) {
    await this.log({
      eventType: 'USER_CREATE',
      userId,
      entityType: 'User',
      entityId: user.id,
      newValues: user,
      request
    });
  }

  async logUserUpdate({ userId, targetUserId, oldValues, newValues, request }) {
    await this.log({
      eventType: 'USER_UPDATE',
      userId,
      entityType: 'User',
      entityId: targetUserId,
      oldValues,
      newValues,
      request
    });
  }

  async logUserDelete({ userId, targetUserId, oldValues, request }) {
    await this.log({
      eventType: 'USER_DELETE',
      userId,
      entityType: 'User',
      entityId: targetUserId,
      oldValues,
      request
    });
  }

  // Email logging
  async logEmailSent({ userId, emailData, recipients, request }) {
    await this.log({
      eventType: 'EMAIL_SENT',
      userId,
      entityType: 'Email',
      entityId: emailData.id || null,
      newValues: {
        ...emailData,
        recipients: recipients
      },
      request
    });
  }

  async logEmailTemplateCreate({ userId, template, request }) {
    await this.log({
      eventType: 'EMAIL_TEMPLATE_CREATE',
      userId,
      entityType: 'EmailTemplate',
      entityId: template.id,
      newValues: template,
      request
    });
  }

  async logEmailTemplateUpdate({ userId, templateId, oldValues, newValues, request }) {
    await this.log({
      eventType: 'EMAIL_TEMPLATE_UPDATE',
      userId,
      entityType: 'EmailTemplate',
      entityId: templateId,
      oldValues,
      newValues,
      request
    });
  }

  async logEmailTemplateDelete({ userId, templateId, oldValues, request }) {
    await this.log({
      eventType: 'EMAIL_TEMPLATE_DELETE',
      userId,
      entityType: 'EmailTemplate',
      entityId: templateId,
      oldValues,
      request
    });
  }

  // Pipeline logging
  async logPipelineStageCreate({ userId, stage, request }) {
    await this.log({
      eventType: 'PIPELINE_STAGE_CREATE',
      userId,
      entityType: 'PipelineStage',
      entityId: stage.id,
      newValues: stage,
      request
    });
  }

  async logPipelineStageUpdate({ userId, stageId, oldValues, newValues, request }) {
    await this.log({
      eventType: 'PIPELINE_STAGE_UPDATE',
      userId,
      entityType: 'PipelineStage',
      entityId: stageId,
      oldValues,
      newValues,
      request
    });
  }

  async logPipelineStageDelete({ userId, stageId, oldValues, request }) {
    await this.log({
      eventType: 'PIPELINE_STAGE_DELETE',
      userId,
      entityType: 'PipelineStage',
      entityId: stageId,
      oldValues,
      request
    });
  }

  async logContactStageChange({ userId, contactId, oldStage, newStage, request }) {
    await this.log({
      eventType: 'CONTACT_STAGE_CHANGE',
      userId,
      entityType: 'Contact',
      entityId: contactId,
      oldValues: { pipelineStage: oldStage },
      newValues: { pipelineStage: newStage },
      request
    });
  }

  // Workflow logging
  async logWorkflowCreate({ userId, workflow, request }) {
    await this.log({
      eventType: 'WORKFLOW_CREATE',
      userId,
      entityType: 'Workflow',
      entityId: workflow.id,
      newValues: workflow,
      request
    });
  }

  async logWorkflowUpdate({ userId, workflowId, oldValues, newValues, request }) {
    await this.log({
      eventType: 'WORKFLOW_UPDATE',
      userId,
      entityType: 'Workflow',
      entityId: workflowId,
      oldValues,
      newValues,
      request
    });
  }

  async logWorkflowDelete({ userId, workflowId, oldValues, request }) {
    await this.log({
      eventType: 'WORKFLOW_DELETE',
      userId,
      entityType: 'Workflow',
      entityId: workflowId,
      oldValues,
      request
    });
  }

  async logWorkflowExecution({ userId, workflowId, contactId, triggerData, request }) {
    await this.log({
      eventType: 'WORKFLOW_EXECUTED',
      userId,
      entityType: 'Workflow',
      entityId: workflowId,
      metadata: {
        contactId,
        triggerData
      },
      request
    });
  }

  // Activity Type logging
  async logActivityTypeCreate({ userId, activityType, request }) {
    await this.log({
      eventType: 'ACTIVITY_TYPE_CREATE',
      userId,
      entityType: 'ActivityType',
      entityId: activityType.id,
      newValues: activityType,
      request
    });
  }

  async logActivityTypeUpdate({ userId, activityTypeId, oldValues, newValues, request }) {
    await this.log({
      eventType: 'ACTIVITY_TYPE_UPDATE',
      userId,
      entityType: 'ActivityType',
      entityId: activityTypeId,
      oldValues,
      newValues,
      request
    });
  }

  async logActivityTypeDelete({ userId, activityTypeId, oldValues, request }) {
    await this.log({
      eventType: 'ACTIVITY_TYPE_DELETE',
      userId,
      entityType: 'ActivityType',
      entityId: activityTypeId,
      oldValues,
      request
    });
  }

  // Tag logging
  async logTagCreate({ userId, tag, request }) {
    await this.log({
      eventType: 'TAG_CREATE',
      userId,
      entityType: 'Tag',
      entityId: tag.id,
      newValues: tag,
      request
    });
  }

  async logTagUpdate({ userId, tagId, oldValues, newValues, request }) {
    await this.log({
      eventType: 'TAG_UPDATE',
      userId,
      entityType: 'Tag',
      entityId: tagId,
      oldValues,
      newValues,
      request
    });
  }

  async logTagDelete({ userId, tagId, oldValues, request }) {
    await this.log({
      eventType: 'TAG_DELETE',
      userId,
      entityType: 'Tag',
      entityId: tagId,
      oldValues,
      request
    });
  }

  // Legacy methods for backward compatibility
  async logAccountOpeningAttempt({
    contactId,
    provider,
    status,
    errorMessage = null,
    ip = null,
    userAgent = null
  }) {
    await this.log({
      eventType: 'ACCOUNT_OPENING_ATTEMPT',
      entityType: 'Contact',
      entityId: contactId,
      metadata: {
        provider,
        status,
        errorMessage
      },
      ip,
      userAgent
    });
  }

  async logAuthCallback({
    provider,
    status,
    errorMessage = null,
    ip = null
  }) {
    await this.log({
      eventType: 'AUTH_CALLBACK',
      metadata: {
        provider,
        status,
        errorMessage
      },
      ip
    });
  }

  // Query methods
  async getAccountOpeningAttempts(contactId) {
    try {
      return await prisma.auditLog.findMany({
        where: {
          entityId: contactId,
          eventType: 'ACCOUNT_OPENING_ATTEMPT'
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
    } catch (error) {
      console.error('Failed to fetch account opening attempts:', error);
      return [];
    }
  }

  async getAuditLogs({
    page = 1,
    pageSize = 50,
    eventType = null,
    entityType = null,
    userId = null,
    startDate = null,
    endDate = null,
    search = null
  }) {
    try {
      const skip = (page - 1) * pageSize;
      const where = {};

      if (eventType) where.eventType = eventType;
      if (entityType) where.entityType = entityType;
      if (userId) where.userId = userId;

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = new Date(startDate);
        if (endDate) where.createdAt.lte = new Date(endDate);
      }

      if (search) {
        where.OR = [
          { eventType: { contains: search, mode: 'insensitive' } },
          { entityType: { contains: search, mode: 'insensitive' } },
          { ip: { contains: search, mode: 'insensitive' } }
        ];
      }

      const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          skip,
          take: pageSize
        }),
        prisma.auditLog.count({ where })
      ]);

      return {
        logs,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      };
    } catch (error) {
      console.error('Failed to fetch audit logs:', error);
      return {
        logs: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0
      };
    }
  }

  async getEventTypes() {
    try {
      const result = await prisma.auditLog.groupBy({
        by: ['eventType'],
        _count: {
          eventType: true
        },
        orderBy: {
          eventType: 'asc'
        }
      });

      return result.map(item => ({
        eventType: item.eventType,
        count: item._count.eventType
      }));
    } catch (error) {
      console.error('Failed to fetch event types:', error);
      return [];
    }
  }

  async getEntityTypes() {
    try {
      const result = await prisma.auditLog.groupBy({
        by: ['entityType'],
        _count: {
          entityType: true
        },
        where: {
          entityType: {
            not: null
          }
        },
        orderBy: {
          entityType: 'asc'
        }
      });

      return result.map(item => ({
        entityType: item.entityType,
        count: item._count.entityType
      }));
    } catch (error) {
      console.error('Failed to fetch entity types:', error);
      return [];
    }
  }
}

export const auditLogger = new AuditLogger();