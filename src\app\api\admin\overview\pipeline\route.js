import { NextResponse } from 'next/server';
import { getSignedInUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma/client';

// GET /api/admin/overview/pipeline - Get detailed pipeline analytics
export async function GET(request) {
  try {
    // Check authentication and authorization
    const { user } = await getSignedInUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const timeframe = parseInt(searchParams.get('timeframe') || '30'); // days
    const includeHistorical = searchParams.get('includeHistorical') === 'true';

    // Calculate date range
    const now = new Date();
    const startDate = new Date(now.getTime() - timeframe * 24 * 60 * 60 * 1000);

    // Get all pipeline stages
    const stages = await prisma.pipelineStage.findMany({
      orderBy: { order: 'asc' }
    });

    // Get detailed analytics for each stage
    const stageAnalytics = await Promise.all(
      stages.map(stage => getStageAnalytics(stage, startDate, now, includeHistorical))
    );

    // Calculate overall pipeline metrics
    const overallMetrics = await getOverallPipelineMetrics(startDate, now);

    // Get conversion funnel data
    const conversionFunnel = await getConversionFunnel(stages, startDate, now);

    // Get velocity trends
    const velocityTrends = await getVelocityTrends(stages, startDate, now);

    return NextResponse.json({
      stages: stageAnalytics,
      overallMetrics,
      conversionFunnel,
      velocityTrends,
      metadata: {
        timeframe,
        startDate,
        endDate: now,
        includeHistorical
      }
    });

  } catch (error) {
    console.error('Error fetching pipeline analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pipeline analytics' },
      { status: 500 }
    );
  }
}

async function getStageAnalytics(stage, startDate, endDate, includeHistorical) {
  // Current contacts in stage
  const currentContacts = await prisma.contact.count({
    where: { pipelineStage: stage.id }
  });

  // Contacts that entered this stage in timeframe
  const entered = await prisma.contactStageHistory.count({
    where: {
      stageId: stage.id,
      enteredAt: { gte: startDate, lte: endDate }
    }
  });

  // Contacts that exited this stage in timeframe
  const exited = await prisma.contactStageHistory.count({
    where: {
      stageId: stage.id,
      exitedAt: { gte: startDate, lte: endDate }
    }
  });

  // Average time in stage (completed entries only)
  const completedEntries = await prisma.contactStageHistory.findMany({
    where: {
      stageId: stage.id,
      exitedAt: { not: null },
      ...(includeHistorical ? {} : { enteredAt: { gte: startDate } })
    },
    select: { duration: true }
  });

  const averageDuration = completedEntries.length > 0
    ? completedEntries.reduce((sum, entry) => sum + (entry.duration || 0), 0) / completedEntries.length
    : null;

  // Median time in stage
  const durations = completedEntries.map(e => e.duration || 0).sort((a, b) => a - b);
  const medianDuration = durations.length > 0
    ? durations.length % 2 === 0
      ? (durations[durations.length / 2 - 1] + durations[durations.length / 2]) / 2
      : durations[Math.floor(durations.length / 2)]
    : null;

  // Activities in this stage during timeframe
  const stageActivities = await prisma.activity.count({
    where: {
      createdAt: { gte: startDate, lte: endDate },
      contact: { pipelineStage: stage.id }
    }
  });

  // Contacts stuck in stage (more than 2x average duration)
  const stuckThreshold = averageDuration ? averageDuration * 2 : 60; // Default 60 days
  const stuckContacts = await prisma.contactStageHistory.count({
    where: {
      stageId: stage.id,
      exitedAt: null,
      enteredAt: { lte: new Date(endDate.getTime() - stuckThreshold * 24 * 60 * 60 * 1000) }
    }
  });

  // Conversion rate to next stage
  const nextStage = await prisma.pipelineStage.findFirst({
    where: { order: stage.order + 1 }
  });

  let conversionRate = null;
  if (nextStage) {
    const convertedToNext = await prisma.contactStageHistory.count({
      where: {
        stageId: nextStage.id,
        enteredAt: { gte: startDate, lte: endDate },
        contact: {
          stageHistory: {
            some: {
              stageId: stage.id,
              exitedAt: { not: null }
            }
          }
        }
      }
    });
    conversionRate = entered > 0 ? (convertedToNext / entered) * 100 : 0;
  }

  // Drop-off rate (contacts that left pipeline from this stage)
  const droppedOff = await prisma.contactStageHistory.count({
    where: {
      stageId: stage.id,
      exitedAt: { gte: startDate, lte: endDate },
      contact: { pipelineStage: null }
    }
  });
  const dropOffRate = entered > 0 ? (droppedOff / entered) * 100 : 0;

  return {
    stage: {
      id: stage.id,
      name: stage.name,
      order: stage.order
    },
    metrics: {
      currentContacts,
      entered,
      exited,
      averageDuration,
      medianDuration,
      stageActivities,
      stuckContacts,
      conversionRate,
      dropOffRate,
      activitiesPerContact: currentContacts > 0 ? stageActivities / currentContacts : 0
    }
  };
}

async function getOverallPipelineMetrics(startDate, endDate) {
  // Total contacts in pipeline
  const totalInPipeline = await prisma.contact.count({
    where: { pipelineStage: { not: null } }
  });

  // New contacts entered pipeline
  const newInPipeline = await prisma.contactStageHistory.count({
    where: {
      enteredAt: { gte: startDate, lte: endDate },
      contact: {
        stageHistory: {
          none: {
            enteredAt: { lt: startDate }
          }
        }
      }
    }
  });

  // Contacts that completed pipeline (reached final stage)
  const finalStages = await prisma.pipelineStage.findMany({
    where: { name: { in: ['Closed Won', 'Client', 'Converted'] } }
  });

  const completed = await prisma.contactStageHistory.count({
    where: {
      stageId: { in: finalStages.map(s => s.id) },
      enteredAt: { gte: startDate, lte: endDate }
    }
  });

  // Average pipeline duration (from first stage to completion)
  const completedJourneys = await prisma.contactStageHistory.findMany({
    where: {
      stageId: { in: finalStages.map(s => s.id) },
      enteredAt: { gte: startDate, lte: endDate }
    },
    include: {
      contact: {
        include: {
          stageHistory: {
            orderBy: { enteredAt: 'asc' }
          }
        }
      }
    }
  });

  const pipelineDurations = completedJourneys.map(journey => {
    const firstEntry = journey.contact.stageHistory[0];
    const lastEntry = journey;
    return firstEntry ? Math.ceil((lastEntry.enteredAt - firstEntry.enteredAt) / (1000 * 60 * 60 * 24)) : 0;
  });

  const averagePipelineDuration = pipelineDurations.length > 0
    ? pipelineDurations.reduce((sum, duration) => sum + duration, 0) / pipelineDurations.length
    : null;

  // Pipeline velocity (contacts moving per day)
  const totalMovements = await prisma.contactStageHistory.count({
    where: { enteredAt: { gte: startDate, lte: endDate } }
  });
  const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
  const velocity = days > 0 ? totalMovements / days : 0;

  // Overall conversion rate
  const overallConversionRate = newInPipeline > 0 ? (completed / newInPipeline) * 100 : 0;

  return {
    totalInPipeline,
    newInPipeline,
    completed,
    averagePipelineDuration,
    velocity,
    overallConversionRate,
    timeframe: days
  };
}

async function getConversionFunnel(stages, startDate, endDate) {
  const funnelData = [];

  for (let i = 0; i < stages.length; i++) {
    const stage = stages[i];
    const nextStage = stages[i + 1];

    const entered = await prisma.contactStageHistory.count({
      where: {
        stageId: stage.id,
        enteredAt: { gte: startDate, lte: endDate }
      }
    });

    let converted = 0;
    if (nextStage) {
      converted = await prisma.contactStageHistory.count({
        where: {
          stageId: nextStage.id,
          enteredAt: { gte: startDate, lte: endDate },
          contact: {
            stageHistory: {
              some: {
                stageId: stage.id,
                exitedAt: { not: null }
              }
            }
          }
        }
      });
    }

    const conversionRate = entered > 0 ? (converted / entered) * 100 : 0;

    funnelData.push({
      stage: stage.name,
      stageId: stage.id,
      order: stage.order,
      entered,
      converted,
      conversionRate,
      retained: entered - converted
    });
  }

  return funnelData;
}

async function getVelocityTrends(stages, startDate, endDate) {
  // Calculate weekly velocity for each stage
  const weeks = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24 * 7));
  const trends = [];

  for (let week = 0; week < weeks; week++) {
    const weekStart = new Date(startDate.getTime() + week * 7 * 24 * 60 * 60 * 1000);
    const weekEnd = new Date(Math.min(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000, endDate.getTime()));

    const weekData = {
      week: week + 1,
      startDate: weekStart,
      endDate: weekEnd,
      stages: {}
    };

    for (const stage of stages) {
      const movements = await prisma.contactStageHistory.count({
        where: {
          stageId: stage.id,
          enteredAt: { gte: weekStart, lte: weekEnd }
        }
      });

      weekData.stages[stage.name] = movements;
    }

    trends.push(weekData);
  }

  return trends;
}
