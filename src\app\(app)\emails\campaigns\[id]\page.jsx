"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import {
  Calendar,
  Clock,
  Users,
  ArrowLeft,
  Mail,
  MousePointer,
  AlertCircle,
  Info,
  Send,
  Filter,
  CheckCircle2,
  X,
  Search,
} from "lucide-react";
import { Bar, Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
} from "chart.js";
import Tooltip from "@/components/ui/Tooltip";
import MetricCard from "@/components/ui/emails/MetricCard";
import Preview from "@/components/ui/editor/Preview";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend
);

const formatPercentage = (value) => {
  if (!value || isNaN(value)) return "0%";
  return `${(value * 100).toFixed(1)}%`;
};

export default function CampaignDetails() {
  const params = useParams();
  const router = useRouter();
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showDistributionModal, setShowDistributionModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  const handleTemplateClick = (template) => {
    setSelectedTemplate(template);
    setShowTemplateModal(true);
  };

  const {
    data: campaign,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["campaign", params.id],
    queryFn: async () => {
      const response = await fetch(`/api/email-campaigns/${params.id}`);
      if (!response.ok) throw new Error("Failed to fetch campaign");
      return response.json();
    },
  });

  console.log({ campaign });

  const { data: analytics } = useQuery({
    queryKey: ["campaign-analytics", params.id],
    queryFn: async () => {
      const response = await fetch(
        `/api/email-campaigns/${params.id}/analytics`
      );
      if (!response.ok) throw new Error("Failed to fetch analytics");
      return response.json();
    },
    enabled: !!campaign,
  });

  if (isLoading) return <div>Loading campaign details...</div>;
  if (error) return <div>Error: {error.message}</div>;

  const engagementData = {
    labels:
      analytics?.dailyStats?.map((stat) =>
        new Date(stat.date).toLocaleDateString()
      ) || [],
    datasets: [
      {
        label: "Opens",
        data: analytics?.dailyStats?.map((stat) => stat.opens) || [],
        borderColor: "#10B981",
        backgroundColor: "#10B981",
        tension: 0.4,
      },
      {
        label: "Clicks",
        data: analytics?.dailyStats?.map((stat) => stat.clicks) || [],
        borderColor: "#F59E0B",
        backgroundColor: "#F59E0B",
        tension: 0.4,
      },
    ],
  };

  const engagementOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        grid: { color: "rgba(156, 163, 175, 0.1)" },
      },
      x: {
        grid: { display: false },
      },
    },
    plugins: {
      legend: {
        position: "top",
      },
    },
  };

  const deliveryData = {
    labels: ["Delivered", "Opened", "Clicked", "Bounced"],
    datasets: [
      {
        data: [
          analytics?.delivered || 0,
          analytics?.opened || 0,
          analytics?.clicked || 0,
          analytics?.bounced || 0,
        ],
        backgroundColor: ["#4F46E5", "#10B981", "#F59E0B", "#EF4444"],
        borderRadius: 4,
      },
    ],
  };

  const deliveryOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        grid: { color: "rgba(156, 163, 175, 0.1)" },
      },
    },
    plugins: {
      legend: {
        display: false,
      },
    },
  };

  // Additional chart configurations
  const deviceBreakdownData = {
    labels: ["Mobile", "Desktop", "Tablet"],
    datasets: [
      {
        data: [
          analytics?.deviceBreakdown?.mobile || 0,
          analytics?.deviceBreakdown?.desktop || 0,
          analytics?.deviceBreakdown?.tablet || 0,
        ],
        backgroundColor: ["#4F46E5", "#10B981", "#F59E0B"],
        borderRadius: 4,
      },
    ],
  };

  const locationData = {
    labels: analytics?.topLocations?.map((loc) => loc.location) || [],
    datasets: [
      {
        data: analytics?.topLocations?.map((loc) => loc.percentage) || [],
        backgroundColor: [
          "#4F46E5",
          "#10B981",
          "#F59E0B",
          "#8B5CF6",
          "#EC4899",
        ],
        borderRadius: 4,
      },
    ],
  };

  return (
    <div className="max-w-7xl mx-auto py-8 px-4">
      <div className="mb-6">
        <Link
          href="/emails?tab=campaigns"
          className="inline-flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Campaigns
        </Link>
      </div>

      {/* Campaign Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold mb-2">{campaign.name}</h1>
        <p className="text-gray-600 dark:text-gray-400">
          {campaign.description}
        </p>
      </div>

      {/* Top Row: Campaign Details, Template Preview, Distribution List */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Campaign Details Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-lg font-medium mb-4">Campaign Details</h2>
          <div className="space-y-3">
            <div className="flex items-center">
              <Clock className="h-5 w-5 mr-2 text-gray-500" />
              <span>
                Type:{" "}
                {campaign.campaignType === "one-time"
                  ? "One-Time Campaign"
                  : "Automated Drip Campaign"}
              </span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-gray-500" />
              <span>
                Send Type:{" "}
                {campaign.sendType === "scheduled"
                  ? `Scheduled for ${new Date(
                      campaign.scheduledDate
                    ).toLocaleDateString()} ${campaign.scheduledTime}`
                  : "Immediate"}
              </span>
            </div>
            <div className="flex items-center">
              <Users className="h-5 w-5 mr-2 text-gray-500" />
              <span>Status: {campaign.status}</span>
            </div>
          </div>
        </div>

        {/* Template Preview Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-lg font-medium mb-4">Template Preview</h2>
          <div
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors h-[calc(100%-3rem)]"
            onClick={() => setShowTemplateModal(true)}
          >
            <h3 className="font-medium mb-2">{campaign.template?.name}</h3>
            <p className="text-sm text-gray-500 mb-4">
              Subject: {campaign.template?.subject}
            </p>
            <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-md text-sm line-clamp-3 mb-2">
              <div className="text-xs text-gray-500 mb-1">Preview:</div>
              {campaign.template?.content ? (
                <div className="line-clamp-2 text-gray-600 dark:text-gray-300">
                  {campaign.template.content.replace(/<[^>]*>/g, " ")}
                </div>
              ) : (
                <div className="text-gray-400 italic">No content available</div>
              )}
            </div>
            <p className="text-xs text-blue-500 mt-auto">
              Click to view full template
            </p>
          </div>
        </div>

        {/* Distribution List Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-lg font-medium mb-4">Distribution List</h2>
          <div
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors h-[calc(100%-3rem)]"
            onClick={() => setShowDistributionModal(true)}
          >
            <h3 className="font-medium mb-2">
              {campaign.distributionList?.name}
            </h3>
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <Users className="h-4 w-4 mr-2" />
              <span>
                {campaign.distributionList?.contacts?.length +
                  campaign.distributionList?.groups.reduce(
                    (acc, group) => acc + (group?.contacts?.length || 0),
                    0
                  )}{" "}
                total recipients
              </span>
            </div>

            <div className="space-y-2 mb-2">
              {campaign.distributionList?.contacts?.length > 0 && (
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                  <span>
                    {campaign.distributionList.contacts.length} direct contacts
                  </span>
                </div>
              )}

              {campaign.distributionList?.groups
                ?.map((group, idx) => (
                  <div key={idx} className="flex items-center text-sm">
                    <div
                      className="w-2 h-2 rounded-full mr-2"
                      style={{ backgroundColor: group.color || "#6B7280" }}
                    ></div>
                    <span>
                      {group.name} ({group.contacts?.length || 0})
                    </span>
                  </div>
                ))
                .slice(0, 2)}

              {campaign.distributionList?.groups?.length > 2 && (
                <div className="text-xs text-gray-500">
                  +{campaign.distributionList.groups.length - 2} more groups
                </div>
              )}
            </div>

            <p className="text-xs text-blue-500 mt-auto">
              Click to view all recipients
            </p>
          </div>
        </div>
      </div>

      {/* Drip Sequence Section - Only show if it's a drip campaign */}
      {campaign.campaignType === "drip" && campaign.sequence && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <h2 className="text-lg font-medium mb-6">Email Sequence</h2>
          <div className="relative">
            {/* Vertical line connector */}
            <div className="absolute left-[27px] top-[40px] bottom-8 w-[2px] bg-blue-200/50 dark:bg-blue-900/50" />

            <div className="space-y-8">
              {campaign?.sequence?.steps.map((step, index) => (
                <div key={index} className="relative flex items-start gap-6">
                  {/* Step number circle */}
                  <div className="relative z-10 flex-shrink-0 w-14 h-14 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 font-semibold border-4 border-white dark:border-gray-800">
                    {index + 1}
                  </div>
                  {/* Step content */}
                  <div className="flex-1 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <h3 className="font-medium mb-2">
                      {step.name || `Step ${index + 1}`}
                    </h3>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      Sent {step.delay} {step.delayUnit} after previous email
                    </div>
                    <div
                      className="bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600 p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                      onClick={() => handleTemplateClick(step.template)}
                    >
                      <div className="font-medium mb-1">
                        {step.template?.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                        Subject: {step.template?.subject}
                      </div>
                      <div className="text-xs text-blue-500">
                        Click to preview template
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Analytics Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <h2 className="text-lg font-medium mb-4">Campaign Analytics</h2>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-[#4F46E5]/10 dark:bg-[#4F46E5]/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Emails Sent</h3>
              <Tooltip content="Total number of emails delivered in this campaign">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Emails Sent"
              value={analytics?.delivered || 0}
              trend={0}
              icon={Send}
            />
          </div>

          <div className="bg-[#10B981]/10 dark:bg-[#10B981]/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Open Rate</h3>
              <Tooltip content="Percentage of delivered emails that were opened">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Open Rate"
              value={formatPercentage(analytics?.openRate)}
              trend={0}
              icon={Mail}
              subtitle={`${analytics?.uniqueOpens || 0} unique opens`}
            />
          </div>

          <div className="bg-[#F59E0B]/10 dark:bg-[#F59E0B]/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Click Rate</h3>
              <Tooltip content="Percentage of opened emails that were clicked">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Click Rate"
              value={formatPercentage(analytics?.clickRate)}
              trend={0}
              icon={MousePointer}
              subtitle={`${analytics?.uniqueClicks || 0} unique clicks`}
            />
          </div>

          <div className="bg-red-500/10 dark:bg-red-500/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-base font-medium">Bounce Rate</h3>
              <Tooltip content="Percentage of emails that bounced">
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <MetricCard
              title="Bounce Rate"
              value={formatPercentage(analytics?.bounceRate)}
              trend={0}
              icon={AlertCircle}
              trendReversed
            />
          </div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium mb-4">Engagement Over Time</h3>
            <div className="h-[300px]">
              <Line data={engagementData} options={engagementOptions} />
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium mb-4">Delivery Statistics</h3>
            <div className="h-[300px]">
              <Bar data={deliveryData} options={deliveryOptions} />
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium mb-4">Device Breakdown</h3>
            <div className="h-[300px]">
              <Bar data={deviceBreakdownData} options={deliveryOptions} />
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium mb-4">Top Locations</h3>
            <div className="h-[300px]">
              <Bar data={locationData} options={deliveryOptions} />
            </div>
          </div>
        </div>
      </div>

      {/* Template Preview Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] flex flex-col">
            <div className="flex items-center justify-between p-4 border-b dark:border-gray-700">
              <h3 className="text-lg font-medium">
                {selectedTemplate?.name || campaign.template?.name}
              </h3>
              <button
                onClick={() => {
                  setShowTemplateModal(false);
                  setSelectedTemplate(null);
                }}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="p-2 overflow-auto flex-grow">
              <div className="bg-gray-100 dark:bg-gray-700 p-2 mb-4 rounded text-sm">
                <strong>Subject:</strong>{" "}
                {selectedTemplate?.subject || campaign.template?.subject}
              </div>
              <Preview
                content={
                  selectedTemplate?.content ||
                  campaign.template?.content ||
                  "<p>No content available</p>"
                }
                className="max-h-[60vh] overflow-auto"
              />
            </div>
            <div className="p-4 border-t dark:border-gray-700 flex justify-end">
              <button
                onClick={() => {
                  setShowTemplateModal(false);
                  setSelectedTemplate(null);
                }}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Distribution List Modal */}
      {showDistributionModal && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] flex flex-col">
            <div className="flex items-center justify-between p-4 border-b dark:border-gray-700">
              <h3 className="text-lg font-medium">
                {campaign.distributionList?.name} - Recipients
              </h3>
              <button
                onClick={() => setShowDistributionModal(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-4 border-b dark:border-gray-700">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search recipients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border rounded-lg dark:bg-gray-700 dark:border-gray-600"
                />
              </div>
            </div>

            <div className="overflow-auto flex-grow p-2">
              <div className="text-sm text-gray-500 mb-2 px-2">
                {campaign.distributionList?.contacts?.length +
                  campaign.distributionList?.groups.reduce(
                    (acc, group) => acc + (group?.contacts?.length || 0),
                    0
                  )}{" "}
                total recipients
              </div>

              {/* Direct Contacts */}
              {campaign.distributionList?.contacts?.length > 0 && (
                <div className="mb-6">
                  <div className="bg-gray-100 dark:bg-gray-700 rounded-t-md px-3 py-2 border-l-4 border-blue-500 flex items-center">
                    <div>
                      <h4 className="font-medium text-gray-800 dark:text-gray-200">
                        Direct Contacts
                      </h4>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {campaign.distributionList.contacts.length}{" "}
                        {campaign.distributionList.contacts.length === 1
                          ? "contact"
                          : "contacts"}
                      </div>
                    </div>
                  </div>
                  <div className="border border-gray-200 dark:border-gray-700 rounded-b-md mb-2">
                    {campaign.distributionList.contacts.filter(
                      (contact) =>
                        (contact.firstName?.toLowerCase() || "").includes(
                          searchTerm.toLowerCase()
                        ) ||
                        (contact.lastName?.toLowerCase() || "").includes(
                          searchTerm.toLowerCase()
                        ) ||
                        `${contact.firstName || ""} ${contact.lastName || ""}`
                          .toLowerCase()
                          .includes(searchTerm.toLowerCase()) ||
                        (contact.email?.toLowerCase() || "").includes(
                          searchTerm.toLowerCase()
                        )
                    ).length > 0 ? (
                      <div className="divide-y divide-gray-200 dark:divide-gray-700">
                        {campaign.distributionList.contacts
                          .filter(
                            (contact) =>
                              (contact.firstName?.toLowerCase() || "").includes(
                                searchTerm.toLowerCase()
                              ) ||
                              (contact.lastName?.toLowerCase() || "").includes(
                                searchTerm.toLowerCase()
                              ) ||
                              `${contact.firstName || ""} ${
                                contact.lastName || ""
                              }`
                                .toLowerCase()
                                .includes(searchTerm.toLowerCase()) ||
                              (contact.email?.toLowerCase() || "").includes(
                                searchTerm.toLowerCase()
                              )
                          )
                          .map((contact) => (
                            <div
                              key={contact.id}
                              className="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700"
                            >
                              <div className="flex-1">
                                <div className="font-medium">
                                  {contact.firstName || ""}{" "}
                                  {contact.lastName || ""}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {contact.email || "No email"}
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    ) : (
                      <div className="p-3 text-center text-gray-500 dark:text-gray-400 text-sm">
                        No contacts match your search
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Group Contacts */}
              {campaign.distributionList?.groups?.length > 0 && (
                <div>
                  {campaign.distributionList.groups.map((group) => {
                    const groupContacts = group.contacts.filter(
                      (contact) =>
                        (
                          contact.contact.firstName?.toLowerCase() || ""
                        ).includes(searchTerm.toLowerCase()) ||
                        (
                          contact.contact.lastName?.toLowerCase() || ""
                        ).includes(searchTerm.toLowerCase()) ||
                        `${contact.contact.firstName || ""} ${
                          contact.contact.lastName || ""
                        }`
                          .toLowerCase()
                          .includes(searchTerm.toLowerCase()) ||
                        (contact.contact.email?.toLowerCase() || "").includes(
                          searchTerm.toLowerCase()
                        )
                    );

                    if (groupContacts.length === 0 && searchTerm) return null;

                    return (
                      <div key={group.id} className="mb-6">
                        <div className="bg-gray-100 dark:bg-gray-700 rounded-t-md px-3 py-2 border-l-4 border-primary flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-gray-800 dark:text-gray-200">
                              Group: {group.name}
                            </h4>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {group.contacts.length}{" "}
                              {group.contacts.length === 1
                                ? "contact"
                                : "contacts"}
                            </div>
                          </div>
                          {group.color && (
                            <div
                              className="h-4 w-4 rounded-full"
                              style={{ backgroundColor: group.color }}
                              title={`Group color: ${group.color}`}
                            ></div>
                          )}
                        </div>
                        <div className="border border-gray-200 dark:border-gray-700 rounded-b-md mb-2">
                          {groupContacts.length > 0 ? (
                            <div className="divide-y divide-gray-200 dark:divide-gray-700">
                              {groupContacts.map((contact) => (
                                <div
                                  key={contact.id}
                                  className="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700"
                                >
                                  <div className="flex-1">
                                    <div className="font-medium">
                                      {contact.contact.firstName || ""}{" "}
                                      {contact.contact.lastName || ""}
                                    </div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                      {contact.contact.email || "No email"}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="p-3 text-center text-gray-500 dark:text-gray-400 text-sm">
                              No contacts in this group
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* No results message */}
              {searchTerm &&
                campaign.distributionList?.contacts?.filter(
                  (contact) =>
                    (contact.firstName?.toLowerCase() || "").includes(
                      searchTerm.toLowerCase()
                    ) ||
                    (contact.lastName?.toLowerCase() || "").includes(
                      searchTerm.toLowerCase()
                    ) ||
                    `${contact.firstName || ""} ${contact.lastName || ""}`
                      .toLowerCase()
                      .includes(searchTerm.toLowerCase()) ||
                    (contact.email?.toLowerCase() || "").includes(
                      searchTerm.toLowerCase()
                    )
                ).length === 0 &&
                campaign.distributionList?.groups?.every(
                  (group) =>
                    group.contacts.filter(
                      (contact) =>
                        (contact.firstName?.toLowerCase() || "").includes(
                          searchTerm.toLowerCase()
                        ) ||
                        (contact.lastName?.toLowerCase() || "").includes(
                          searchTerm.toLowerCase()
                        ) ||
                        `${contact.firstName || ""} ${contact.lastName || ""}`
                          .toLowerCase()
                          .includes(searchTerm.toLowerCase()) ||
                        (contact.email?.toLowerCase() || "").includes(
                          searchTerm.toLowerCase()
                        )
                    ).length === 0
                ) && (
                  <div className="text-center py-8 text-gray-500">
                    No recipients found matching "{searchTerm}"
                  </div>
                )}
            </div>

            <div className="p-4 border-t dark:border-gray-700 flex justify-end">
              <button
                onClick={() => setShowDistributionModal(false)}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
