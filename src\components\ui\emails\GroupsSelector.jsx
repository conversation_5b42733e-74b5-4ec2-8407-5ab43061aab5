import { CheckIcon } from "lucide-react";
import { useEffect, useMemo, useState } from "react";

export default function GroupsSelector({
  selectedGroups = [],
  onChange,
  selectedContactIds = [],
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [groups, setGroups] = useState([]);

  const fetchGroups = async () => {
    try {
      const params = new URLSearchParams({ contacts: true });
      const response = await fetch(`/api/groups?${params.toString()}`);

      if (response.ok) {
        const data = await response.json();
        setGroups(data);
      }
    } catch (error) {
      console.error("Error fetching groups:", error);
    }
  };

  useEffect(() => {
    fetchGroups();
  }, []);

  const toggleGroup = (newGroup) => {
    let newSelectedGroups = [];

    // Check to see if the newGroup is inside of the selectedGroups array
    let groupIndex = selectedGroups.findIndex(
      (group) => group.id === newGroup.id
    );
    if (groupIndex === -1) {
      // add the new group to the selection
      newSelectedGroups = [...selectedGroups, newGroup];
    } else {
      // is not a new group, remove it from the selection
      newSelectedGroups = selectedGroups.filter(
        (group) => group.id !== newGroup.id
      );
    }

    onChange(newSelectedGroups);
  };

  const filteredGroups = useMemo(() => {
    return groups.filter((group) => {
      return group.name.toLowerCase().includes(searchTerm.toLowerCase());
    });
  }, [searchTerm, groups]);

  return (
    <div className="border rounded-md dark:border-gray-700">
      <div className="p-3 border-b dark:border-gray-700">
        <div className="flex items-center gap-2 mb-3">
          <input
            type="text"
            placeholder="Search groups..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-3 py-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
          />
        </div>
      </div>

      <div className="overflow-y-auto max-h-[400px]">
        {filteredGroups.map((group) => (
          <div
            key={group.id}
            onClick={() => toggleGroup(group)}
            className={`flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 ${
              selectedGroups.map((group) => group.id).includes(group.id)
                ? "bg-blue-50 dark:bg-blue-900/20"
                : ""
            }`}
          >
            <div>
              <div className="font-medium">{group.name}</div>
              <div className="text-sm text-gray-500">
                {group.contacts.length || 0} contacts
              </div>
              <div className="text-sm text-gray-500">{group.description}</div>
            </div>
            {selectedGroups.map((group) => group.id).includes(group.id) && (
              <CheckIcon className="h-5 w-5 text-blue-500" />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
