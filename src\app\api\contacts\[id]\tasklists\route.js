import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { NextResponse } from 'next/server';


// GET: List all tasklists for a contact
export async function GET(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);
    const { id } = await params;
    // First verify the contact exists and is accessible
    let whereClause = { id: String(id) };
    if (user.role !== 'admin') {
      whereClause.userId = String(user.id);
    }
    const contact = await prisma.contact.findFirst({ where: whereClause });
    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }
    // Now fetch tasklists for this contact
    const tasklists = await prisma.tasklist.findMany({
      where: { contactId: id },
      orderBy: { createdAt: 'desc' },
      include: { items: { orderBy: { order: 'asc' } } },
    });
    return NextResponse.json(tasklists);
  } catch (error) {
    console.error('Failed to fetch tasklists:', error);
    return NextResponse.json({ error: 'Failed to fetch tasklists' }, { status: 500 });
  }
}

// POST: Create a new tasklist for a contact
export async function POST(request, { params }) {
  try {
    // Get the current user
        const { user } = await getSignedInUser(request);
    
        const { id } = await params;
        let whereClause = { id: String(id) };
    
        if (user.role !== "admin") {
          whereClause.userId = String(user.id);
        }
    
        const contact = await prisma.contact.findFirst({
          where: whereClause,
        });
    
    
        if (!contact) {
          return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
        }

        const data = await request.json();
        const { items, ...tasklistData } = data;



        /*
data: {
          title: "a",
          description: "b",
          status: "in_progress",
          contactId: "67e8a77dd7ad924f24d482fb",
          items: {
            create: [
              {
                title: "c",
                description: "d",
                completed: false,
                priority: "medium",
                dueDate: "2025-05-30"
              },
              {
                title: "e",
                description: "f",
                completed: false,
                priority: "high",
                dueDate: "2025-06-06"
              }
            ]
          }
        }
        */

        // Convert dueDate to ISO string if present and not empty
        const itemsForCreate = (items || []).map(item => ({
        ...item,
        title: item.title || 'Untitled Task',
        description: item.description || '',
        dueDate: item.dueDate ? new Date(item.dueDate).toISOString() : null,
        priority: item.priority || 'medium',
        completedAt: item.completed ? new Date() : null,
        createdAt: new Date(),
        updatedAt: new Date(),
        order: item.order || 0,
        completed: item.completed || false,
        }));

        const tasklist = await prisma.tasklist.create({
        data: {
            ...tasklistData,
            contactId: id,
            items: {
            create: itemsForCreate,
            },
        },
        });
        return NextResponse.json(tasklist);
  } catch (error) {
    console.error('Failed to create tasklist:', error);
    return NextResponse.json({ error: 'Failed to create tasklist' }, { status: 500 });
  }
}
