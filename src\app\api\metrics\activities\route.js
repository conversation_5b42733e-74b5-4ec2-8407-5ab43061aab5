import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma/client';

export async function GET() {
  try {
    // Get activities for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const activities = await prisma.activity.groupBy({
      by: ['createdAt'],
      _count: true,
      where: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Format the data for the chart
    const formattedData = activities.map(activity => ({
      date: activity.createdAt.toISOString().split('T')[0],
      count: activity._count,
    }));

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Failed to fetch activity metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activity metrics' },
      { status: 500 }
    );
  }
}