'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import ActivityForm from '@/components/contacts/profile/ActivityForm';
import TaskForm from '@/components/contacts/profile/TaskForm';
import NoteForm from '@/components/contacts/profile/NoteForm';
import ColumnCustomizer from './ColumnCustomizer';
import ColumnHeaderMenu from './ColumnHeaderMenu';
import ContactEditDialog from './ContactEditDialog';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';
import ContactDialog from './ContactDialog';
import ImportDialog from './ImportDialog';
import ExportMenu from './ExportMenu';
import { CONTACT_FIELDS, DEFAULT_FIELDS, getFieldById } from '@/lib/constants/contactFields';

export default function ContactList({ contacts = [], filters = {}, onRefresh, onClearFilters, onColumnFiltersChange, onSearchTermChange, pipelineStages = {}, pipelineStagesLoaded = false, groupContactsCache = {} }) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  // Sort icon component
  const SortIcon = ({ columnKey }) => {
    if (sortConfig.key !== columnKey) {
      return <span className="text-gray-300 dark:text-gray-600">↕</span>;
    }
    return sortConfig.direction === 'asc' ? (
      <span className="text-primary">↑</span>
    ) : (
      <span className="text-primary">↓</span>
    );
  };

  // Column customization state
  const [selectedFields, setSelectedFields] = useState(DEFAULT_FIELDS);

  // Column filtering state
  const [columnFilters, setColumnFilters] = useState({});

  // Pipeline stages and group contacts are now passed as props from parent

  // Dialog states
  const [activityDialogOpen, setActivityDialogOpen] = useState(false);
  const [taskDialogOpen, setTaskDialogOpen] = useState(false);
  const [noteDialogOpen, setNoteDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0); // Used to trigger a refresh

  // Helper function to validate field IDs
  const validateFieldIds = (fieldIds) => {
    // Filter out any field IDs that don't exist in CONTACT_FIELDS
    const validFieldIds = fieldIds.filter(id => {
      const isValid = CONTACT_FIELDS.some(field => field.id === id);
      if (!isValid) {
        console.warn(`Invalid field ID found in saved fields: ${id}`);
      }
      return isValid;
    });

    // If no valid fields remain, return default fields
    if (validFieldIds.length === 0) {
      console.warn('No valid fields found in saved fields, using defaults');
      return DEFAULT_FIELDS;
    }

    return validFieldIds;
  };

  // Load selected fields from local storage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedFields = localStorage.getItem('contactTableFields');
      if (savedFields) {
        try {
          const parsedFields = JSON.parse(savedFields);
          // Ensure we have valid fields
          if (parsedFields && Array.isArray(parsedFields) && parsedFields.length > 0) {
            const validatedFields = validateFieldIds(parsedFields);
            setSelectedFields(validatedFields);

            // If the validated fields are different from the parsed fields,
            // update local storage with the valid fields
            if (validatedFields.length !== parsedFields.length) {
              localStorage.setItem('contactTableFields', JSON.stringify(validatedFields));
            }
          } else {
            // Fallback to default fields
            setSelectedFields(DEFAULT_FIELDS);
          }
        } catch (error) {
          console.error('Failed to parse saved fields:', error);
          // Fallback to default fields
          setSelectedFields(DEFAULT_FIELDS);
        }
      }
    }
  }, []);



  // Save selected fields to local storage when they change
  const handleFieldsChange = (fields) => {
    // Validate fields before saving
    const validatedFields = validateFieldIds(fields);
    setSelectedFields(validatedFields);

    if (typeof window !== 'undefined') {
      localStorage.setItem('contactTableFields', JSON.stringify(validatedFields));
    }
  };

  // Handle hiding a column
  const handleHideColumn = (fieldId) => {
    if (selectedFields.length > 1) {
      const newFields = selectedFields.filter(id => id !== fieldId);
      handleFieldsChange(newFields);
    }
  };

  // Handle column filtering
  const handleColumnFilter = (fieldId, filterValue) => {
    const updatedFilters = { ...columnFilters };
    if (filterValue) {
      updatedFilters[fieldId] = filterValue.toLowerCase();
    } else {
      delete updatedFilters[fieldId];
    }

    // Update local state
    setColumnFilters(updatedFilters);

    // Notify parent component about column filter changes
    if (onColumnFiltersChange) {
      // Use setTimeout to avoid state updates during render
      setTimeout(() => {
        onColumnFiltersChange(updatedFilters);
      }, 0);
    }
  };

  // Clear all column filters
  const clearColumnFilters = () => {
    setColumnFilters({});
    if (onColumnFiltersChange) {
      // Use setTimeout to avoid state updates during render
      setTimeout(() => {
        onColumnFiltersChange({});
      }, 0);
    }
  };

  // Clear search term
  const clearSearchTerm = () => {
    setSearchTerm('');
    if (onSearchTermChange) {
      // Use setTimeout to avoid state updates during render
      setTimeout(() => {
        onSearchTermChange('');
      }, 0);
    }
  };

  const handleSort = (key) => {
    setSortConfig((current) => ({
      key,
      direction: current.key === key && current.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const filteredAndSortedContacts = useMemo(() => {
    // Add pipeline stage names to contacts
    let result = contacts.map(contact => {
      if (contact.pipelineStage && pipelineStages[contact.pipelineStage]) {
        return {
          ...contact,
          _pipelineStageName: pipelineStages[contact.pipelineStage]
        };
      }
      return contact;
    });

    // Apply search
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      result = result.filter(contact => {
        // Search in all fields
        return (
          contact.firstName?.toLowerCase().includes(search) ||
          contact.lastName?.toLowerCase().includes(search) ||
          contact.email?.toLowerCase().includes(search) ||
          contact.phone?.toLowerCase().includes(search) ||
          contact.company?.toLowerCase().includes(search) ||
          contact.type?.toLowerCase().includes(search) ||
          (contact._pipelineStageName && contact._pipelineStageName.toLowerCase().includes(search)) ||
          (contact.lastContactDate && new Date(contact.lastContactDate).toLocaleDateString().toLowerCase().includes(search)) ||
          (contact.createdAt && new Date(contact.createdAt).toLocaleDateString().toLowerCase().includes(search)) ||
          (contact.updatedAt && new Date(contact.updatedAt).toLocaleDateString().toLowerCase().includes(search))
        );
      });
    }

    // Apply global filters
    if (filters.type) {
      result = result.filter(contact => contact.type === filters.type);
    }

    // Apply pipeline stage filter
    if (filters.pipelineStage) {
      if (filters.pipelineStage === 'unassigned') {
        // Filter for contacts without a pipeline stage
        result = result.filter(contact => !contact.pipelineStage);
      } else {
        // Filter for contacts with the selected pipeline stage
        result = result.filter(contact => contact.pipelineStage === filters.pipelineStage);
      }
    }

    // Apply group filter
    if (filters.groupId && groupContactsCache[filters.groupId]) {
      const contactIds = new Set(groupContactsCache[filters.groupId]);

      // Try multiple methods to match contacts by ID
      // This handles different ID formats (string vs ObjectId)
      const matchingContacts = result.filter(contact => {
        const contactId = contact.id;
        const stringId = String(contactId);

        // Try Set.has() with both formats
        if (contactIds.has(contactId) || contactIds.has(stringId)) {
          return true;
        }

        // Fallback: Try direct string comparison
        return Array.from(contactIds).some(groupContactId =>
          groupContactId.toString() === contactId.toString()
        );
      });

      result = matchingContacts;
    }

    // Apply column filters
    if (Object.keys(columnFilters).length > 0) {
      result = result.filter(contact => {
        return Object.entries(columnFilters).every(([fieldId, filterValue]) => {
          const field = getFieldById(fieldId);
          if (!field) {
            return true; // Skip invalid fields
          }

          // Special handling for pipeline stage
          if (fieldId === 'pipelineStage') {
            // Special case for 'Not Assigned'
            if (filterValue === '_not_assigned_') {
              return !contact.pipelineStage;
            }

            // If the contact has no pipeline stage, it doesn't match any other filter
            if (!contact.pipelineStage) {
              return false;
            }

            // Direct match on pipeline stage ID
            if (contact.pipelineStage === filterValue) {
              return true;
            }

            // If the contact has a pipeline stage name cached, check if it matches
            if (contact._pipelineStageName) {
              return contact._pipelineStageName.toLowerCase().includes(filterValue.toLowerCase());
            }

            // Check if the stage name from our map matches
            const stageName = pipelineStages[contact.pipelineStage];
            if (stageName) {
              return stageName.toLowerCase().includes(filterValue.toLowerCase());
            }

            return false;
          }

          // Get the rendered value for this field
          const renderedValue = field.render(contact);
          if (renderedValue === undefined || renderedValue === null) {
            return false;
          }

          // Convert to string and check if it includes the filter value
          const stringValue = String(renderedValue).toLowerCase();
          return stringValue.includes(filterValue);
        });
      });
    }

    // Apply sorting
    if (sortConfig.key) {
      result.sort((a, b) => {
        // Handle special cases for date fields
        if (['createdAt', 'updatedAt', 'lastContactDate'].includes(sortConfig.key)) {
          const aDate = a[sortConfig.key] ? new Date(a[sortConfig.key]) : new Date(0);
          const bDate = b[sortConfig.key] ? new Date(b[sortConfig.key]) : new Date(0);
          return sortConfig.direction === 'asc' ? aDate - bDate : bDate - aDate;
        }

        // Handle name fields specially
        if (sortConfig.key === 'firstName') {
          const aValue = a.firstName || '';
          const bValue = b.firstName || '';
          if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
          if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
          // If first names are equal, sort by last name
          const aLastName = a.lastName || '';
          const bLastName = b.lastName || '';
          if (aLastName < bLastName) return sortConfig.direction === 'asc' ? -1 : 1;
          if (aLastName > bLastName) return sortConfig.direction === 'asc' ? 1 : -1;
          return 0;
        }

        if (sortConfig.key === 'lastName') {
          const aValue = a.lastName || '';
          const bValue = b.lastName || '';
          if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
          if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
          // If last names are equal, sort by first name
          const aFirstName = a.firstName || '';
          const bFirstName = b.firstName || '';
          if (aFirstName < bFirstName) return sortConfig.direction === 'asc' ? -1 : 1;
          if (aFirstName > bFirstName) return sortConfig.direction === 'asc' ? 1 : -1;
          return 0;
        }

        // Handle pipeline stage field specially
        if (sortConfig.key === 'pipelineStage') {
          const getStageValue = (contact) => {
            if (contact._pipelineStageName) {
              return contact._pipelineStageName;
            }
            if (!contact.pipelineStage) return '';
            return String(contact.pipelineStage);
          };

          const aValue = getStageValue(a);
          const bValue = getStageValue(b);
          if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
          if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
          return 0;
        }

        // Handle regular fields
        const aValue = a[sortConfig.key] || '';
        const bValue = b[sortConfig.key] || '';
        if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return result;
  }, [contacts, searchTerm, sortConfig, filters, columnFilters, pipelineStages, groupContactsCache]);

  const handleRowClick = (contactId) => {
    router.push(`/contacts/${contactId}`);
  };

  const handleEmail = (e, contact) => {
    e.stopPropagation();
    // Open the default email client
    window.open(`mailto:${contact.email}`);
  };

  const handleAddActivity = (e, contact) => {
    e.stopPropagation();
    setSelectedContact(contact);
    setActivityDialogOpen(true);
  };

  const handleAddTask = (e, contact) => {
    e.stopPropagation();
    setSelectedContact(contact);
    setTaskDialogOpen(true);
  };

  const handleAddNote = (e, contact) => {
    e.stopPropagation();
    setSelectedContact(contact);
    setNoteDialogOpen(true);
  };

  const handleEditContact = (e, contact) => {
    e.stopPropagation();
    setSelectedContact(contact);
    setEditDialogOpen(true);
  };

  const handleDeleteContact = (e, contact) => {
    e.stopPropagation();
    setSelectedContact(contact);
    setDeleteDialogOpen(true);
  };

  const handleSaveContact = async (contactData) => {
    try {
      const response = await fetch(`/api/contacts/${contactData.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData),
      });

      if (response.ok) {
        // Trigger a refresh by incrementing the refreshTrigger
        setRefreshTrigger(prev => prev + 1);
        // Call the onRefresh prop if provided
        if (onRefresh) onRefresh();
      } else {
        console.error('Failed to update contact');
      }
    } catch (error) {
      console.error('Error updating contact:', error);
    }
  };

  const handleConfirmDelete = async () => {
    if (!selectedContact) return;

    try {
      const response = await fetch(`/api/contacts/${selectedContact.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Trigger a refresh by incrementing the refreshTrigger
        setRefreshTrigger(prev => prev + 1);
        // Call the onRefresh prop if provided
        if (onRefresh) onRefresh();
      } else {
        console.error('Failed to delete contact');
      }
    } catch (error) {
      console.error('Error deleting contact:', error);
    }
  };

  // Close dialogs when no contact is selected
  useEffect(() => {
    if (!selectedContact) {
      setActivityDialogOpen(false);
      setTaskDialogOpen(false);
      setNoteDialogOpen(false);
      setEditDialogOpen(false);
      setDeleteDialogOpen(false);
    }
  }, [selectedContact]);

  // (Removed: pipeline stages fetching, now handled by parent)

  // Listen for the clearAllFilters custom event
  useEffect(() => {
    const handleClearAllFiltersEvent = () => {
      // Clear column filters
      setColumnFilters({});

      // Clear search term
      setSearchTerm('');

      // Reset sort config
      setSortConfig({ key: null, direction: 'asc' });

      // Notify parent components with a slight delay to avoid React warnings
      setTimeout(() => {
        if (onColumnFiltersChange) {
          onColumnFiltersChange({});
        }

        if (onSearchTermChange) {
          onSearchTermChange('');
        }
      }, 0);
    };

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('clearAllFilters', handleClearAllFiltersEvent);
    }

    // Clean up
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('clearAllFilters', handleClearAllFiltersEvent);
      }
    };
  }, [onColumnFiltersChange, onSearchTermChange]);

  // (Removed: group contacts fetching, now handled by parent)

  // Handler functions for activity/task/note save success (now handled in forms)
  // (Removed: now handled in forms)

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center mb-4">
        {/* Search Bar - Extended */}
        <div className="relative flex-grow mr-4">
          <input
            type="text"
            placeholder="Search contacts..."
            value={searchTerm}
            onChange={(e) => {
              const newSearchTerm = e.target.value;
              setSearchTerm(newSearchTerm);

              // Notify parent component about search term changes
              if (onSearchTermChange) {
                // Use setTimeout to avoid state updates during render
                setTimeout(() => {
                  onSearchTermChange(newSearchTerm);
                }, 0);
              }
            }}
            className="w-full px-4 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Action Buttons - Moved to the right */}
        <div className="flex items-center space-x-2">
          {/* Import Button */}
          {onRefresh && (
            <ImportDialog
              onImportComplete={(result) => {
                if (onRefresh) onRefresh();
                alert(
                  `Imported ${result.imported} contacts. Failed: ${result.failed}`
                );
              }}
              trigger={
                <button
                  className="px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 flex items-center text-sm min-w-[100px] justify-center"
                  title="Import contacts from CSV"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    />
                  </svg>
                  Import
                </button>
              }
            />
          )}

          {/* Export Button */}
          {contacts && contacts.length > 0 && (
            <ExportMenu contacts={contacts} selectedFields={selectedFields} />
          )}

          {/* Add Contact Button */}
          {onRefresh && (
            <ContactDialog
              onSave={async (formData) => {
                try {
                  const response = await fetch("/api/contacts", {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(formData),
                  });

                  if (response.ok) {
                    if (onRefresh) onRefresh();
                  }
                } catch (error) {
                  console.error("Error creating contact:", error);
                }
              }}
              trigger={
                <button
                  className="px-3 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover flex items-center text-sm min-w-[100px] justify-center"
                  title="Add a new contact"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  Add Contact
                </button>
              }
            />
          )}

          {!pipelineStagesLoaded && (
            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
              <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading stages...
            </div>
          )}

          <ColumnCustomizer
            selectedFields={selectedFields}
            onFieldsChange={handleFieldsChange}
          />
        </div>
      </div>

      <div className="flex-1 overflow-x-auto overflow-y-auto card-light dark:bg-gray-800 rounded-lg show-scrollbar">
        <table className="min-w-full border-collapse border-spacing-0">
          <thead className="bg-gray-200 dark:bg-gray-800 sticky top-0">
            <tr className="border-b border-gray-300 dark:border-gray-700">
              {selectedFields.map(fieldId => {
                const field = getFieldById(fieldId);
                // Skip rendering if field is not found
                if (!field) return null;

                return (
                  <th
                    key={fieldId}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort(field.sortKey)}
                  >
                    <div className="flex items-center">
                      <span className={columnFilters[fieldId] ? 'text-primary font-medium' : ''}>
                        {field.label}
                        {columnFilters[fieldId] && (
                          <span className="ml-1 text-xs">(Filtered)</span>
                        )}
                      </span>
                      <SortIcon columnKey={field.sortKey} />
                      <ColumnHeaderMenu
                        fieldId={fieldId}
                        onHideColumn={handleHideColumn}
                        onSort={(key, direction) => {
                          setSortConfig({ key, direction });
                        }}
                        onFilter={handleColumnFilter}
                        activeFilters={columnFilters}
                        contacts={contacts}
                        pipelineStages={pipelineStages}
                      />
                    </div>
                  </th>
                );
              })}
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky right-0 bg-gray-200 dark:bg-gray-800 border-t border-b border-gray-300 dark:border-gray-700 z-10"

              >
                <span>Actions</span>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-900">
            {filteredAndSortedContacts.length === 0 ? (
              <tr>
                <td colSpan={selectedFields.length + 1} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                  No contacts found
                </td>
              </tr>
            ) : (
              filteredAndSortedContacts.map((contact) => (
                <tr
                  key={contact.id}
                  onClick={() => handleRowClick(contact.id)}
                  className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer group border-b border-gray-300 dark:border-gray-700"
                >
                  {selectedFields.map(fieldId => {
                    const field = getFieldById(fieldId);
                    // Skip rendering if field is not found
                    if (!field) return null;

                    const value = field.render(contact);
                    return (
                  <td key={fieldId} className="px-6 py-4 whitespace-nowrap border-t border-b border-gray-300 dark:border-gray-700">
                        {field.className ? (
                          <span className={field.className}>
                            {value}
                          </span>
                        ) : (
                          <div className="text-sm text-gray-500 dark:text-gray-300">
                            {value}
                          </div>
                        )}
                      </td>
                    );
                  })}
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium sticky right-0 cursor-pointer group bg-white dark:bg-gray-900 group-hover:bg-gray-100 dark:group-hover:bg-gray-800 border-t border-b border-gray-300 dark:border-gray-700 z-0" >  
                    <div className="flex justify-end items-center">
                      <div className="flex space-x-3">
                        {contact.email && (
                          <button
                            onClick={(e) => handleEmail(e, contact)}
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                            title="Send Email"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                          </button>
                        )}
                        <button
                          onClick={(e) => handleAddActivity(e, contact)}
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                          title="Add Activity"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </button>
                        <button
                          onClick={(e) => handleAddTask(e, contact)}
                          className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"
                          title="Add Task"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                        </button>
                        <button
                          onClick={(e) => handleAddNote(e, contact)}
                          className="text-amber-600 hover:text-amber-900 dark:text-amber-400 dark:hover:text-amber-300"
                          title="Add Note"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                      </div>

                      {/* Separator */}
                      <div className="mx-2 h-6 border-l border-gray-300 dark:border-gray-600"></div>

                      {/* Edit and Delete buttons */}
                      <div className="flex space-x-3">
                        <button
                          onClick={(e) => handleEditContact(e, contact)}
                          className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                          title="Edit Contact"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                          </svg>
                        </button>
                        <button
                          onClick={(e) => handleDeleteContact(e, contact)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          title="Delete Contact"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
        Showing {filteredAndSortedContacts.length} of {contacts.length} contacts
      </div>

      {/* Activity Form */}
      <ActivityForm
        contactId={selectedContact?.id}
        activity={null}
        isOpen={activityDialogOpen}
        onClose={() => setActivityDialogOpen(false)}
      />

      {/* Task Form */}
      <TaskForm
        contactId={selectedContact?.id}
        task={null}
        isOpen={taskDialogOpen}
        onClose={() => setTaskDialogOpen(false)}
      />

      {/* Note Form */}
      <NoteForm
        contactId={selectedContact?.id}
        note={null}
        isOpen={noteDialogOpen}
        onClose={() => setNoteDialogOpen(false)}
      />

      {/* Edit Contact Dialog */}
      {selectedContact && (
        <ContactEditDialog
          contact={selectedContact}
          isOpen={editDialogOpen}
          onClose={() => setEditDialogOpen(false)}
          onSave={handleSaveContact}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {selectedContact && (
        <DeleteConfirmationDialog
          isOpen={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
          onConfirm={handleConfirmDelete}
          contactName={`${selectedContact.firstName} ${selectedContact.lastName}`}
        />
      )}
    </div>
  );
}