import { NextResponse } from 'next/server';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

export async function GET(request) {
  try {
    const { user } = await getSignedInUser(request);
    
    // Check if user is admin
    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    const entityTypes = await auditLogger.getEntityTypes();
    return NextResponse.json(entityTypes);
  } catch (error) {
    console.error('Error fetching entity types:', error);
    return NextResponse.json(
      { error: 'Failed to fetch entity types' },
      { status: 500 }
    );
  }
}
