import { NextResponse } from 'next/server';
import { getSignedInUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma/client';
import { auditLogger } from '@/lib/services/auditLogger';

// GET /api/workflows/[id] - Get a specific workflow
export async function GET(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;
    const { id } = params;

    // Get the workflow with its related data
    const workflow = await prisma.workflow.findUnique({
      where: {
        id,
        userId // Ensure the workflow belongs to the current user
      },
      include: {
        trigger: true,
        conditions: {
          orderBy: { order: 'asc' }
        },
        actions: {
          orderBy: { order: 'asc' }
        }
      }
    });

    if (!workflow) {
      return NextResponse.json({ error: 'Workflow not found' }, { status: 404 });
    }

    return NextResponse.json(workflow);
  } catch (error) {
    console.error('Error fetching workflow:', error);
    return NextResponse.json({ error: 'Failed to fetch workflow' }, { status: 500 });
  }
}

// PUT /api/workflows/[id] - Update a workflow
export async function PUT(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;
    const { id } = params;
    const data = await request.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: 'Workflow name is required' }, { status: 400 });
    }

    if (!data.trigger) {
      return NextResponse.json({ error: 'A trigger is required' }, { status: 400 });
    }

    if (!data.actions || data.actions.length === 0) {
      return NextResponse.json({ error: 'At least one action is required' }, { status: 400 });
    }

    // Check if the workflow exists and belongs to the user
    const existingWorkflow = await prisma.workflow.findUnique({
      where: {
        id,
        userId
      },
      include: {
        trigger: true,
        conditions: true,
        actions: true
      }
    });

    if (!existingWorkflow) {
      return NextResponse.json({ error: 'Workflow not found' }, { status: 404 });
    }

    // Update the workflow in a transaction
    const updatedWorkflow = await prisma.$transaction(async (tx) => {
      // Update the main workflow
      const workflow = await tx.workflow.update({
        where: { id },
        data: {
          name: data.name,
          description: data.description || '',
          isActive: data.isActive !== undefined ? data.isActive : true,
        }
      });

      // Update or create the trigger
      if (data.trigger) {
        await tx.workflowTrigger.upsert({
          where: { workflowId: id },
          update: {
            triggerType: data.trigger.triggerType,
            config: data.trigger.config || {}
          },
          create: {
            workflowId: id,
            triggerType: data.trigger.triggerType,
            config: data.trigger.config || {}
          }
        });
      }

      // Delete existing conditions and create new ones
      await tx.workflowCondition.deleteMany({
        where: { workflowId: id }
      });

      if (data.conditions && data.conditions.length > 0) {
        await tx.workflowCondition.createMany({
          data: data.conditions.map((condition, index) => ({
            workflowId: id,
            field: condition.field,
            operator: condition.operator,
            value: condition.value,
            order: index + 1
          }))
        });
      }

      // Delete existing actions and create new ones
      await tx.workflowAction.deleteMany({
        where: { workflowId: id }
      });

      if (data.actions && data.actions.length > 0) {
        await tx.workflowAction.createMany({
          data: data.actions.map((action, index) => ({
            workflowId: id,
            actionType: action.actionType,
            config: action.config || {},
            order: index + 1
          }))
        });
      }

      // Return the updated workflow with its related data
      return tx.workflow.findUnique({
        where: { id },
        include: {
          trigger: true,
          conditions: {
            orderBy: { order: 'asc' }
          },
          actions: {
            orderBy: { order: 'asc' }
          }
        }
      });
    });

    // Log the workflow update
    await auditLogger.logWorkflowUpdate({
      userId,
      workflowId: id,
      oldValues: existingWorkflow,
      newValues: updatedWorkflow,
      request
    });

    return NextResponse.json(updatedWorkflow);
  } catch (error) {
    console.error('Error updating workflow:', error);
    return NextResponse.json({ error: 'Failed to update workflow' }, { status: 500 });
  }
}

// PATCH /api/workflows/[id] - Update specific fields of a workflow (e.g., isActive)
export async function PATCH(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;
    const { id } = params;
    const data = await request.json();

    // Check if the workflow exists and belongs to the user
    const existingWorkflow = await prisma.workflow.findUnique({
      where: {
        id,
        userId
      }
    });

    if (!existingWorkflow) {
      return NextResponse.json({ error: 'Workflow not found' }, { status: 404 });
    }

    // Update only the provided fields
    const updatedWorkflow = await prisma.workflow.update({
      where: { id },
      data,
      include: {
        trigger: true,
        conditions: {
          orderBy: { order: 'asc' }
        },
        actions: {
          orderBy: { order: 'asc' }
        }
      }
    });

    // Log the workflow update
    await auditLogger.logWorkflowUpdate({
      userId,
      workflowId: id,
      oldValues: existingWorkflow,
      newValues: updatedWorkflow,
      request
    });

    return NextResponse.json(updatedWorkflow);
  } catch (error) {
    console.error('Error updating workflow:', error);
    return NextResponse.json({ error: 'Failed to update workflow' }, { status: 500 });
  }
}

// DELETE /api/workflows/[id] - Delete a workflow
export async function DELETE(request, { params }) {
  try {
    const { user } = await getSignedInUser(request);

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;
    const { id } = params;

    // Check if the workflow exists and belongs to the user
    const existingWorkflow = await prisma.workflow.findUnique({
      where: {
        id,
        userId
      },
      include: {
        trigger: true,
        conditions: true,
        actions: true
      }
    });

    if (!existingWorkflow) {
      return NextResponse.json({ error: 'Workflow not found' }, { status: 404 });
    }

    // Log the workflow deletion
    await auditLogger.logWorkflowDelete({
      userId,
      workflowId: id,
      oldValues: existingWorkflow,
      request
    });

    // Delete the workflow (cascade delete will handle related records)
    await prisma.workflow.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting workflow:', error);
    return NextResponse.json({ error: 'Failed to delete workflow' }, { status: 500 });
  }
}
