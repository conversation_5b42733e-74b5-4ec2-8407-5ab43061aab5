'use client';

import { useState, useEffect } from 'react';

export default function CalendarTabSimple({ contactId }) {
  const [activities, setActivities] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Fetch data when component mounts or contactId changes
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Fetch activities
        const activitiesResponse = await fetch(`/api/contacts/${contactId}/activities`);
        if (!activitiesResponse.ok) throw new Error('Failed to fetch activities');
        const activitiesData = await activitiesResponse.json();
        setActivities(activitiesData.activities || []);
        
        // Fetch tasks
        const tasksResponse = await fetch(`/api/contacts/${contactId}/tasks`);
        if (!tasksResponse.ok) throw new Error('Failed to fetch tasks');
        const tasksData = await tasksResponse.json();
        setTasks(tasksData || []);
        
        // Fetch notes
        const notesResponse = await fetch(`/api/contacts/${contactId}/notes`);
        if (!notesResponse.ok) throw new Error('Failed to fetch notes');
        const notesData = await notesResponse.json();
        setNotes(notesData || []);
      } catch (err) {
        console.error('Error fetching calendar data:', err);
        setError('Failed to load calendar data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [contactId]);

  // Helper function to format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Get current month name
  const getCurrentMonthName = () => {
    return currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  // Navigate to previous month
  const prevMonth = () => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() - 1);
    setCurrentMonth(newMonth);
  };

  // Navigate to next month
  const nextMonth = () => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + 1);
    setCurrentMonth(newMonth);
  };

  // Navigate to current month
  const goToToday = () => {
    setCurrentMonth(new Date());
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md text-red-800 dark:text-red-200">
        <p>{error}</p>
        <button 
          onClick={() => window.location.reload()}
          className="mt-2 text-sm underline hover:text-red-600 dark:hover:text-red-300"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800 rounded-lg shadow">
      {/* Calendar Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          {getCurrentMonthName()}
        </h2>
        <div className="flex space-x-2">
          <button 
            onClick={prevMonth}
            className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
            aria-label="Previous month"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
          <button 
            onClick={goToToday}
            className="px-3 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm font-medium"
          >
            Today
          </button>
          <button 
            onClick={nextMonth}
            className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
            aria-label="Next month"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Temporary Calendar View (List-based) */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="text-center mb-4 text-gray-500 dark:text-gray-400">
          <p>Calendar view is loading. In the meantime, here's a list of your upcoming events:</p>
        </div>
        
        <div className="space-y-4">
          {/* Activities Section */}
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
              <span className="h-3 w-3 rounded-full bg-green-500 mr-2"></span>
              Activities
            </h3>
            {activities.length === 0 ? (
              <p className="text-sm text-gray-500 dark:text-gray-400 italic">No activities found</p>
            ) : (
              <div className="space-y-2">
                {activities.slice(0, 5).map(activity => (
                  <div 
                    key={activity.id} 
                    className="p-2 rounded-md bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800"
                  >
                    <div className="font-medium text-gray-800 dark:text-gray-200">{activity.title}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">{formatDate(activity.date)}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* Tasks Section */}
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
              <span className="h-3 w-3 rounded-full bg-purple-500 mr-2"></span>
              Tasks
            </h3>
            {tasks.length === 0 ? (
              <p className="text-sm text-gray-500 dark:text-gray-400 italic">No tasks found</p>
            ) : (
              <div className="space-y-2">
                {tasks.slice(0, 5).map(task => (
                  <div 
                    key={task.id} 
                    className={`p-2 rounded-md ${task.completed ? 'bg-gray-50 dark:bg-gray-700/50' : 'bg-purple-50 dark:bg-purple-900/20'} border ${task.completed ? 'border-gray-200 dark:border-gray-700' : 'border-purple-100 dark:border-purple-800'}`}
                  >
                    <div className="font-medium text-gray-800 dark:text-gray-200">{task.title}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Due: {formatDate(task.dueDate)}</div>
                    {task.completed && (
                      <div className="text-xs text-green-600 dark:text-green-400 mt-1">Completed</div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* Notes Section */}
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
              <span className="h-3 w-3 rounded-full bg-amber-500 mr-2"></span>
              Notes
            </h3>
            {notes.length === 0 ? (
              <p className="text-sm text-gray-500 dark:text-gray-400 italic">No notes found</p>
            ) : (
              <div className="space-y-2">
                {notes.slice(0, 5).map(note => (
                  <div 
                    key={note.id} 
                    className="p-2 rounded-md bg-amber-50 dark:bg-amber-900/20 border border-amber-100 dark:border-amber-800"
                  >
                    <div className="font-medium text-gray-800 dark:text-gray-200">{note.title}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Created: {formatDate(note.createdAt)}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Legend */}
      <div className="p-3 border-t border-gray-200 dark:border-gray-700 flex flex-wrap gap-3 mt-auto">
        <div className="flex items-center">
          <span className="h-3 w-3 rounded-full bg-green-500 mr-1"></span>
          <span className="text-xs text-gray-600 dark:text-gray-400">Activity</span>
        </div>
        <div className="flex items-center">
          <span className="h-3 w-3 rounded-full bg-purple-500 mr-1"></span>
          <span className="text-xs text-gray-600 dark:text-gray-400">Task</span>
        </div>
        <div className="flex items-center">
          <span className="h-3 w-3 rounded-full bg-gray-400 mr-1"></span>
          <span className="text-xs text-gray-600 dark:text-gray-400">Completed Task</span>
        </div>
        <div className="flex items-center">
          <span className="h-3 w-3 rounded-full bg-amber-500 mr-1"></span>
          <span className="text-xs text-gray-600 dark:text-gray-400">Note</span>
        </div>
      </div>
    </div>
  );
}
