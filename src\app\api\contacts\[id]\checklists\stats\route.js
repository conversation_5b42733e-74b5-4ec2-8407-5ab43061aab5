import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

// GET /api/contacts/[id]/checklists/stats - Get checklist stats for a contact
export async function GET(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const { id: contactId } = await params;
    
    // Get all checklists for the contact
    const checklists = await prisma.checklist.findMany({
      where: { contactId },
      include: {
        items: true
      }
    });
    
    // Calculate stats
    const total = checklists.length;
    const completed = checklists.filter(c => c.status === 'completed').length;
    const inProgress = checklists.filter(c => c.status === 'in_progress').length;
    
    // Calculate item stats
    let totalItems = 0;
    let completedItems = 0;
    
    checklists.forEach(checklist => {
      if (checklist.items && Array.isArray(checklist.items)) {
        totalItems += checklist.items.length;
        completedItems += checklist.items.filter(item => item.completed).length;
      }
    });
    
    return NextResponse.json({
      total,
      completed,
      inProgress,
      items: {
        total: totalItems,
        completed: completedItems
      }
    });
  } catch (error) {
    console.error('Failed to fetch checklist stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch checklist stats: ' + error.message },
      { status: 500 }
    );
  }
}
