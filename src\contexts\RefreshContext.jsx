'use client';

import { createContext, useContext, useState } from 'react';

// Create the context
const RefreshContext = createContext(null);

// Create a provider component
export function RefreshProvider({ children }) {
  // Track the last refresh time for each data type
  const [refreshTimestamps, setRefreshTimestamps] = useState({
    tasks: Date.now(),
    notes: Date.now(),
    activities: Date.now(),
    groups: Date.now(),
    communication: Date.now(),
    engagement: Date.now(),
    milestones: Date.now(),
  });

  // Function to trigger a refresh for specific data types
  const triggerRefresh = (dataTypes) => {
    const now = Date.now();
    const updates = {};
    
    // If dataTypes is a string, convert it to an array
    const types = Array.isArray(dataTypes) ? dataTypes : [dataTypes];
    
    // Update timestamps for the specified data types
    types.forEach(type => {
      if (refreshTimestamps.hasOwnProperty(type)) {
        updates[type] = now;
      }
    });
    
    // Update the state if there are any changes
    if (Object.keys(updates).length > 0) {
      setRefreshTimestamps(prev => ({
        ...prev,
        ...updates
      }));
    }
  };

  return (
    <RefreshContext.Provider value={{ refreshTimestamps, triggerRefresh }}>
      {children}
    </RefreshContext.Provider>
  );
}

// Custom hook to use the refresh context
export function useRefresh() {
  const context = useContext(RefreshContext);
  if (!context) {
    throw new Error('useRefresh must be used within a RefreshProvider');
  }
  return context;
}
