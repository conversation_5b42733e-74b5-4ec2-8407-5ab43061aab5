import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';
import { getSignedInUser } from '@/lib/auth';
import { auditLogger } from '@/lib/services/auditLogger';

// Get a single pipeline stage
export async function GET(request, { params }) {
  try {
    const { id } = await params;
    const stage = await prisma.pipelineStage.findUnique({
      where: { id }
    });
    
    if (!stage) {
      return NextResponse.json(
        { error: 'Pipeline stage not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(stage);
  } catch (error) {
    console.error('Failed to fetch pipeline stage:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pipeline stage' },
      { status: 500 }
    );
  }
}

// Update a pipeline stage
export async function PATCH(request, { params }) {
  try {
    const body = await request.json();
    const { name, order } = body;

    const { id } = await params;

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Get the existing stage for audit log
    const existingStage = await prisma.pipelineStage.findUnique({
      where: { id }
    });

    if (!existingStage) {
      return NextResponse.json(
        { error: 'Pipeline stage not found' },
        { status: 404 }
      );
    }

    const updatedStage = await prisma.pipelineStage.update({
      where: { id },
      data: {
        ...(name !== undefined && { name }),
        ...(order !== undefined && { order }),
        updatedAt: new Date()
      }
    });

    // Log the pipeline stage update
    await auditLogger.logPipelineStageUpdate({
      userId: user?.id,
      stageId: id,
      oldValues: existingStage,
      newValues: updatedStage,
      request
    });

    return NextResponse.json(updatedStage);
  } catch (error) {
    console.error('Failed to update pipeline stage:', error);
    return NextResponse.json(
      { error: 'Failed to update pipeline stage: ' + error.message },
      { status: 500 }
    );
  }
}

// Delete a pipeline stage
export async function DELETE(request, { params }) {
  try {
    // Check if there are any contacts in this stage
    const { id } = await params;
    const contactsInStage = await prisma.contact.count({
      where: { pipelineStage: id }
    });
    
    if (contactsInStage > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete stage with contacts',
          contactCount: contactsInStage
        },
        { status: 400 }
      );
    }
    
    // Delete the stage
    await prisma.pipelineStage.delete({
      where: { id }
    });
    
    // Reorder remaining stages
    const remainingStages = await prisma.pipelineStage.findMany({
      orderBy: { order: 'asc' }
    });
    
    await prisma.$transaction(
      remainingStages.map((stage, index) => 
        prisma.pipelineStage.update({
          where: { id: stage.id },
          data: { order: index + 1 }
        })
      )
    );
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete pipeline stage:', error);
    return NextResponse.json(
      { error: 'Failed to delete pipeline stage: ' + error.message },
      { status: 500 }
    );
  }
}
