import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { deleteSession } from "@/lib/auth";

export async function DELETE() {
  const cookieStore = await cookies();
  const sessionID = cookieStore.get("sessionID")?.value;

  if (sessionID) {
    // Remove the session from the database if there is a session
    await deleteSession(sessionID);
  }

  // Delete the session cookie
  const response = NextResponse.json({ message: "Logged out successfully" });
  response.cookies.delete("sessionID");
  return response;
}
