.landing {
  height: 100vh;
}

.flexSplit {
  display: flex;
  height: 100%;
}

.left {
  background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
    url("/brand-assets/miami-login.jpg");
  background-size: cover;
  background-position: center;
  flex: 1;
  display: flex;
  color: white;
}

.copyContainer {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8px);
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.copyContainer .copy {
  text-align: center;
  max-width: 500px;
}

.copyContainer .copy h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.copyContainer .copy p {
  font-size: 1.5rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: white;
  border-left: 3px solid var(--color-primary);
}

.loginBox {
  width: 100%;
  max-width: 400px;
  padding: 2.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  text-align: center;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.logoContainer {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.loginBox h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.75rem;
}

.loginBox p {
  color: #666;
  margin-bottom: 2rem;
  font-size: 1rem;
}

.loginButtonContainer {
  margin-bottom: 1.5rem;
}

.loginButtonContainer :global(button) {
  width: 100%;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  transition: transform 0.2s ease;
}

.loginButtonContainer :global(button:hover) {
  transform: translateY(-1px);
}

.securityNote {
  color: #888;
  font-size: 0.875rem;
}

/* Dark mode styles */
:global(.dark) .right {
  background: #1a1a1a;
}

:global(.dark) .loginBox {
  background: #242424;
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

:global(.dark) .loginBox h2 {
  color: #fff;
}

:global(.dark) .loginBox p {
  color: #999;
}

:global(.dark) .securityNote {
  color: #777;
}

/* Responsive styles */
@media (max-width: 768px) {
  .flexSplit {
    flex-direction: column;
  }

  .left {
    flex: 0 0 40%;
  }

  .right {
    flex: 0 0 60%;
    border-left: none;
    border-top: 3px solid var(--color-primary);
    padding: 2rem 1rem;
  }

  .loginBox {
    padding: 2rem 1.5rem;
  }

  .copyContainer .copy h1 {
    font-size: 2.5rem;
  }

  .copyContainer .copy p {
    font-size: 1.25rem;
  }
}

/* Animation for content */
.loginBox {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
