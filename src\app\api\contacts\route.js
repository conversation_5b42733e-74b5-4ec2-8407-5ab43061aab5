import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma/client";
import { getSignedInUser, hasPermission } from "@/lib/auth";
import { READ_CONTACTS, WRITE_CONTACTS } from "@/constants/permissions";
import { withPermission } from "@/middleware/permissions";
import { auditLogger } from "@/lib/services/auditLogger";

export async function GET(request) {
  try {
    // Check permission using middleware
    const permissionCheck = await withPermission(READ_CONTACTS)(request);
    console.log("the permission check is:", permissionCheck);
    // if (permissionCheck) {
    //   return permissionCheck; // Return error response if permission check fails
    // }

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Debug log
    console.log("Current user:", user);
    console.log("User role:", user.role);
    console.log("Is admin?", user.role === "admin");

    const { searchParams } = new URL(request.url);
    const dormant = searchParams.get("dormant") === "true";
    const noEmails = searchParams.get("noEmails") === "true";
    const threshold = parseInt(searchParams.get("threshold")) || 30;
    let whereClause = {};
    // if noEmails is true, we only want contacts with no emails sent, or lastContactDate is null
    if (noEmails) {
      whereClause.lastContactDate = null;
    }

    const thresholdDate = new Date();
    thresholdDate.setDate(thresholdDate.getDate() - threshold);

    // Only filter by user ID if not an admin
    if (user.role !== "admin") {
      whereClause.userId = user.id;
    }

    if (dormant) {
      whereClause.lastContactDate = {
        lt: thresholdDate,
      };
    }

    if (noEmails) {
      whereClause.lastContactDate = null;
    }

    // Debug log
    console.log("Where clause:", JSON.stringify(whereClause));

    const contacts = await prisma.contact.findMany({
      where: whereClause,
      orderBy: {
        lastName: "asc",
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        company: true,
        type: true,
        pipelineStage: true,
        lastContactDate: true,
        createdAt: true,
        updatedAt: true,
        tags: true,
      },
    });

    // Debug log
    console.log("Found contacts:", contacts.length);

    return NextResponse.json(contacts);
  } catch (error) {
    console.error("Failed to fetch contacts:", error);
    return NextResponse.json(
      { error: "Failed to fetch contacts" },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    // Check permission using middleware
    const permissionCheck = await withPermission(WRITE_CONTACTS)(request);
    if (permissionCheck) {
      return permissionCheck; // Return error response if permission check fails
    }

    const body = await request.json();

    // Validate required fields
    if (!body.firstName || !body.lastName) {
      return NextResponse.json(
        { error: "First name and last name are required" },
        { status: 400 }
      );
    }

    // Get the current user
    const { user } = await getSignedInUser(request);

    // Use transaction to create contact and initial lifecycle metrics
    const result = await prisma.$transaction(async (tx) => {
      const contact = await tx.contact.create({
        data: {
          ...body,
          userId: user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Create initial lifecycle metrics
      await tx.contactLifecycleMetrics.create({
        data: {
          contactId: contact.id,
          firstContactDate: contact.createdAt,
          totalActivities: 0,
          totalStageChanges: 0,
          engagementScore: 0,
          activityFrequency: 0,
          isDormant: false,
          isHighValue: false,
          riskLevel: 'low',
          conversionProbability: 0.5,
          leadSource: contact.leadSource,
          lastCalculated: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      return contact;
    });

    // Log the contact creation
    await auditLogger.logContactCreate({
      userId: user.id,
      contact: result,
      request
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("Failed to create contact:", error);
    return NextResponse.json(
      { error: "Failed to create contact" },
      { status: 500 }
    );
  }
}
