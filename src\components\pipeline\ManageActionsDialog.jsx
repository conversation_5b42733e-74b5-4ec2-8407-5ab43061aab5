'use client';

import * as Dialog from '@radix-ui/react-dialog';
import { useState, useEffect } from 'react';
import StageActionForm from './StageActionForm';
import { getActionTypeById } from '@/lib/constants/stageActionTypes';

export default function ManageActionsDialog({ stage, isOpen, onClose, onActionsChange }) {
  const [stageActions, setStageActions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isActionFormOpen, setIsActionFormOpen] = useState(false);
  const [currentAction, setCurrentAction] = useState(null);

  // Fetch actions for this stage when the dialog opens
  useEffect(() => {
    if (isOpen && stage?.id) {
      fetchStageActions();
    }
  }, [isOpen, stage?.id]);

  const fetchStageActions = async () => {
    if (!stage?.id) return;

    setIsLoading(true);
    setError('');
    
    try {
      const response = await fetch(`/api/pipeline/stages/${stage.id}/actions`);
      if (response.ok) {
        const actions = await response.json();
        setStageActions(actions);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch actions');
      }
    } catch (error) {
      console.error('Failed to fetch stage actions:', error);
      setError('Failed to fetch actions. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddAction = () => {
    setCurrentAction(null);
    setIsActionFormOpen(true);
  };

  const handleEditAction = (action) => {
    setCurrentAction(action);
    setIsActionFormOpen(true);
  };

  const handleDeleteAction = async (actionId) => {
    if (!window.confirm('Are you sure you want to delete this action?')) {
      return;
    }

    setIsLoading(true);
    setError('');
    
    try {
      const response = await fetch(`/api/pipeline/actions/${actionId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        // Remove the action from the state
        const updatedActions = stageActions.filter(a => a.id !== actionId);
        setStageActions(updatedActions);
        
        // Notify parent component
        if (onActionsChange) {
          onActionsChange(updatedActions);
        }
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to delete action');
      }
    } catch (error) {
      console.error('Failed to delete action:', error);
      setError('Failed to delete action. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleActionSaved = () => {
    // Refresh the actions
    fetchStageActions();
    
    // Close the action form
    setIsActionFormOpen(false);
    
    // Notify parent component
    if (onActionsChange) {
      onActionsChange();
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-xl max-h-[90vh] overflow-y-auto">
          <Dialog.Title className="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            Manage Automated Actions for {stage?.name}
          </Dialog.Title>

          <div className="mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              These actions will be triggered automatically when a contact enters this stage.
            </p>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-200 rounded-md">
              {error}
            </div>
          )}

          <div className="mb-4">
            <button
              onClick={handleAddAction}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
            >
              Add New Action
            </button>
          </div>

          {isLoading ? (
            <div className="py-8 text-center text-gray-500 dark:text-gray-400">
              <div className="inline-block animate-spin h-6 w-6 border-2 border-gray-300 dark:border-gray-600 border-t-primary rounded-full mb-2"></div>
              <p>Loading actions...</p>
            </div>
          ) : stageActions.length === 0 ? (
            <div className="py-8 text-center text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
              <p>No automated actions configured for this stage.</p>
              <p className="text-sm mt-1">Click "Add New Action" to create one.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {stageActions.map(action => {
                const actionType = getActionTypeById(action.actionType);
                return (
                  <div 
                    key={action.id} 
                    className="p-4 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex items-center">
                        <span className="text-xl mr-2">{actionType.icon}</span>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">{actionType.label}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{actionType.description}</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditAction(action)}
                          className="p-1.5 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 rounded"
                          title="Edit"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleDeleteAction(action.id)}
                          className="p-1.5 text-red-600 hover:text-red-700 dark:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                          title="Delete"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    
                    <div className="mt-3 pl-8">
                      {action.actionType === 'activity' && (
                        <div className="text-sm text-gray-700 dark:text-gray-300">
                          <p><span className="font-medium">Type:</span> {action.actionDetails.type}</p>
                          {action.actionDetails.description && (
                            <p><span className="font-medium">Description:</span> {action.actionDetails.description}</p>
                          )}
                        </div>
                      )}
                      
                      {action.actionType === 'task' && (
                        <div className="text-sm text-gray-700 dark:text-gray-300">
                          <p><span className="font-medium">Title:</span> {action.actionDetails.title}</p>
                          {action.actionDetails.description && (
                            <p><span className="font-medium">Description:</span> {action.actionDetails.description}</p>
                          )}
                          <p><span className="font-medium">Priority:</span> {action.actionDetails.priority}</p>
                        </div>
                      )}
                      
                      {action.actionType === 'note' && (
                        <div className="text-sm text-gray-700 dark:text-gray-300">
                          <p><span className="font-medium">Title:</span> {action.actionDetails.title}</p>
                          {action.actionDetails.content && (
                            <p><span className="font-medium">Content:</span> {action.actionDetails.content}</p>
                          )}
                        </div>
                      )}
                      
                      {action.actionType === 'email' && (
                        <div className="text-sm text-gray-700 dark:text-gray-300">
                          <p><span className="font-medium">Subject:</span> {action.actionDetails.subject}</p>
                          {action.actionDetails.body && (
                            <p><span className="font-medium">Body:</span> {action.actionDetails.body.substring(0, 100)}{action.actionDetails.body.length > 100 ? '...' : ''}</p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          <div className="flex justify-end mt-6">
            <Dialog.Close asChild>
              <button className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                Close
              </button>
            </Dialog.Close>
          </div>

          {/* Stage Action Form */}
          <StageActionForm
            stageId={stage?.id}
            stageName={stage?.name}
            action={currentAction}
            isOpen={isActionFormOpen}
            onClose={() => setIsActionFormOpen(false)}
            onSuccess={handleActionSaved}
          />
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
