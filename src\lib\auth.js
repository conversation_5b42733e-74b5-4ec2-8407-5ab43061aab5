import { v4 as uuidv4 } from "uuid";
import { NextResponse } from "next/server";

// This file provides authentication options for the application
// It's used by the API routes to verify user authentication

// Auth options for API routes
export const authOptions = {
  // In a real app, you would configure session management, callbacks, etc.
  // For now, we'll provide a minimal implementation
  providers: [],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async session({ session, token }) {
      // Add user info to the session
      if (token && token.sub) {
        session.user = {
          id: token.sub,
          email: token.email || "",
          name: token.name || "",
        };
      }
      return session;
    },
  },
};

// Helper function to check if a user is authenticated
// This is a simplified version - in a real app, you would use proper session validation
export const isAuthenticated = async (req) => {
  // For development purposes, we'll assume the user is authenticated
  // In production, you would validate the session/token
  return true;
};

// Helper function to check if a user has a specific permission
export const hasPermission = (user, permission) => {
  if (!user) return false;

  // Admin role has all permissions
  if (user.role === "admin") return true;

  // Check if the user has the specific permission
  return user.permissions && user.permissions.includes(permission);
};

// Helper function to check if a user has a specific role
export const hasRole = (user, role) => {
  if (!user) return false;
  return user.role === role;
};

export async function createSession(userData) {
  // Generate a sessionID
  const sessionID = uuidv4();
  // Set the expiration date to 14 days from now
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 14);

  // Extract access token and other data
  const { userId, accessToken, ...otherData } = userData;

  // Create session data object for additional data
  const sessionData = JSON.stringify(otherData);

  // Import prisma client
  const { prisma } = await import("@/lib/prisma/client");

  // Create the session in the database
  await prisma.session.create({
    data: {
      sessionID,
      user: {
        connect: { id: userId },
      },
      accessToken,  // Store the Microsoft access token
      data: sessionData,  // Store other data as JSON string
      expiresAt,
      createdAt: new Date(),
    },
  });

  console.log(`Created session with ID ${sessionID} and access token: ${accessToken ? 'present' : 'not present'}`);

  // Return the sessionID so it can be set in a httpOnly cookie
  return sessionID;
}

export async function createUser(userData) {
  const { prisma } = await import("@/lib/prisma/client");

  // check if a user already exists with userData.azureId
  const existingUser = await prisma.user.findUnique({
    where: { azureId: userData.azureId },
  });

  if (existingUser) {
    console.log(
      "User already exists in mongodb, skipping user creation:",
      existingUser
    );
    return existingUser;
  }

  console.log("User does not exist in mongodb, creating user:", userData);
  const user = await prisma.user.create({
    data: userData,
  });

  return user;
}

export const deleteSession = async (sessionID) => {
  const { prisma } = await import("@/lib/prisma/client");

  await prisma.session.delete({
    where: { sessionID },
  });
};

export const getSignedInUser = async (req) => {
  // Check to see if there is a valid session
  const session = await getSession(req);

  if (!session) {
    return NextResponse.redirect(new URL("/", req.url));
    // return null;
  }

  return session;
};

export const getSession = async (req) => {
  try {
    console.log("IN THE GET SESSION FUNCTION");
    const { prisma } = await import("@/lib/prisma/client");
    const sessionID = req.cookies.get("sessionID")?.value;

    if (!sessionID) {
      console.log("GET SESSION: No sessionID in cookies");
      return null;
    }

    console.log(`GET SESSION: Looking up session with ID: ${sessionID.substring(0, 8)}...`);

    const session = await prisma.session.findUnique({
      where: { sessionID, expiresAt: { gt: new Date() } },
      include: { user: true },
    });

    if (!session) {
      console.log("GET SESSION: No valid session found in database");
      return null;
    }

    console.log(`GET SESSION: Found session for user: ${session.user?.email}`);
    console.log(`GET SESSION: Access token present: ${!!session.accessToken}`);

    return session;
  } catch (error) {
    console.error("Error in getSession:", error);
    return null;
  }
};

export const needsAuth = async (errorData) => {
  if (errorData.error === "Unauthorized") {
    return true;
  }
  return false;
};

export default authOptions;
