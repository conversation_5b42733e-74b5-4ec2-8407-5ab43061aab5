'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import * as Dialog from '@radix-ui/react-dialog';
import * as LucideIcons from 'lucide-react';
// Keep the import for backward compatibility
import { ACTIVITY_TYPES } from '@/lib/constants/activityTypes';

// Helper function to format dates for input fields with timezone handling
const formatDateForInput = (dateString) => {
  if (!dateString) return new Date().toISOString().split('T')[0];

  // Create a date object and adjust for timezone
  const date = new Date(dateString);
  const utcDate = new Date(date.getTime() + date.getTimezoneOffset() * 60000);
  return utcDate.toISOString().split('T')[0];
};

export default function ActivityForm({
  contactId,
  activity = null,
  isOpen,
  onClose,
}) {
  const [formData, setFormData] = useState({
    type: 'call',
    description: '',
    date: formatDateForInput(new Date()),
    // Additional fields for tasks
    title: '',
    priority: 'medium',
    // Additional fields for notes
    content: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [activityTypes, setActivityTypes] = useState([]);
  const [isLoadingTypes, setIsLoadingTypes] = useState(true);
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);

  // Fetch activity types from API
  useEffect(() => {
    const fetchActivityTypes = async () => {
      try {
        const response = await fetch('/api/activity-types');
        if (response.ok) {
          const types = await response.json();
          setActivityTypes(types);
        } else {
          // Fallback to hardcoded types
          setActivityTypes(ACTIVITY_TYPES.map(type => ({
            id: type.id,
            name: type.label,
            icon: type.icon,
            color: type.color || '#3b82f6'
          })));
        }
      } catch (error) {
        console.error('Error fetching activity types:', error);
        // Fallback to hardcoded types
        setActivityTypes(ACTIVITY_TYPES.map(type => ({
          id: type.id,
          name: type.label,
          icon: type.icon,
          color: type.color || '#3b82f6'
        })));
      } finally {
        setIsLoadingTypes(false);
      }
    };

    fetchActivityTypes();
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if click is outside the dropdown
      if (!event.target.closest('.activity-type-dropdown')) {
        setIsTypeDropdownOpen(false);
      }
    };

    if (isTypeDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isTypeDropdownOpen]);

  // If editing an existing activity, populate the form
  useEffect(() => {
    if (activity) {
      // Base activity fields
      const newFormData = {
        type: activity.type,
        description: activity.description || '',
        date: formatDateForInput(activity.date),
        title: '',
        priority: 'medium',
        content: ''
      };

      // If it's a task or note, add the specific fields
      if (activity.originalType === 'task' && activity.originalData) {
        newFormData.title = activity.originalData.title || '';
        newFormData.priority = activity.originalData.priority || 'medium';
      } else if (activity.originalType === 'note' && activity.originalData) {
        newFormData.title = activity.originalData.title || '';
        newFormData.content = activity.originalData.content || '';
      }

      setFormData(newFormData);
    } else {
      // Reset form for new activity
      setFormData({
        type: 'call',
        description: '',
        date: formatDateForInput(new Date()),
        title: '',
        priority: 'medium',
        content: ''
      });
    }
  }, [activity, isOpen]);

  // Handle activity type change
  const handleTypeChange = (type) => {
    setFormData({ ...formData, type });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      // Handle different activity types
      if (formData.type === 'task') {
        // Create a task record first
        const taskData = {
          title: formData.title || 'Untitled Task',
          description: formData.description,
          dueDate: formData.date,
          priority: formData.priority || 'medium'
        };

        const taskResponse = await fetch(`/api/contacts/${contactId}/tasks`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(taskData)
        });

        if (!taskResponse.ok) {
          const errorData = await taskResponse.json();
          throw new Error(errorData.error || 'Failed to save task');
        }

        const savedTask = await taskResponse.json();

        // Now create an activity record for this task
        const activityData = {
          type: 'task',
          description: formData.description || `Task: ${formData.title}`,
          date: formData.date
        };

        const activityResponse = await fetch(`/api/contacts/${contactId}/activities`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(activityData)
        });

        if (!activityResponse.ok) {
          console.error('Failed to create activity record for task');
        }

        // Return the combined data
        toast.success('Task saved!');

        onClose();
      }
      else if (formData.type === 'note') {
        // Create a note record first
        const noteData = {
          title: formData.title || 'Untitled Note',
          content: formData.content || formData.description
        };

        const noteResponse = await fetch(`/api/contacts/${contactId}/notes`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(noteData)
        });

        if (!noteResponse.ok) {
          const errorData = await noteResponse.json();
          throw new Error(errorData.error || 'Failed to save note');
        }

        const savedNote = await noteResponse.json();

        // Now create an activity record for this note
        const activityData = {
          type: 'note',
          description: formData.description || formData.content,
          date: formData.date
        };

        const activityResponse = await fetch(`/api/contacts/${contactId}/activities`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(activityData)
        });

        if (!activityResponse.ok) {
          console.error('Failed to create activity record for note');
        }

        // Return the combined data
        toast.success('Note saved!');

        onClose();
      }
      else {
        // Regular activity
        const url = `/api/contacts/${contactId}/activities`;
        const method = activity ? 'PATCH' : 'POST';
        const body = activity
          ? { ...formData, id: activity.id }
          : formData;

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to save activity');
        }

        const savedActivity = await response.json();
        toast.success(activity ? 'Activity updated!' : 'Activity saved!');

        onClose();
      }
    } catch (error) {
      console.error('Error saving activity:', error);
      setError(error.message || 'An error occurred while saving the activity');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-[100]" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md z-[110]">
          <Dialog.Title className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            {activity ? 'Edit Activity' : 'Add New Activity'}
          </Dialog.Title>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Activity Type
              </label>
              {isLoadingTypes ? (
                <div className="w-full h-10 bg-gray-100 dark:bg-gray-700 rounded-md animate-pulse"></div>
              ) : (
                <div className="relative activity-type-dropdown">
                  {/* Custom dropdown button */}
                  <button
                    type="button"
                    onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                    className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 pl-3 pr-10 py-2 text-sm text-gray-900 dark:text-white text-left focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <div className="flex items-center">
                      {(() => {
                        if (!formData.type) {
                          return <span className="text-gray-500">Select activity type</span>;
                        }

                        // First try exact ID match
                        let selectedType = activityTypes.find(t => t.id === formData.type);

                        // If not found by ID, try matching by name (case-insensitive)
                        if (!selectedType) {
                          selectedType = activityTypes.find(t => t.name.toLowerCase() === formData.type?.toLowerCase());
                        }

                        if (selectedType) {
                          const IconComponent = selectedType.icon && LucideIcons[selectedType.icon];
                          return (
                            <div className="flex items-center">
                              <div
                                className="w-5 h-5 rounded-full flex items-center justify-center mr-3"
                                style={{ backgroundColor: selectedType.color }}
                              >
                                {IconComponent ? (
                                  <IconComponent className="h-3 w-3 text-white" />
                                ) : (
                                  <span className="text-xs text-white font-bold">
                                    {selectedType.name.charAt(0)}
                                  </span>
                                )}
                              </div>
                              <span>{selectedType.name}</span>
                            </div>
                          );
                        }
                        return <span>Select activity type</span>;
                      })()}
                    </div>
                  </button>

                  {/* Dropdown arrow */}
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <LucideIcons.ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>

                  {/* Custom dropdown menu */}
                  {isTypeDropdownOpen && (
                    <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-700 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                      {activityTypes.map((type) => {
                        const IconComponent = type.icon && LucideIcons[type.icon];
                        const isSelected = formData.type === type.id ||
                          (formData.type && type.name.toLowerCase() === formData.type.toLowerCase());

                        return (
                          <div
                            key={type.id}
                            onClick={() => {
                              handleTypeChange(type.id);
                              setIsTypeDropdownOpen(false);
                            }}
                            className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100 dark:hover:bg-gray-600"
                          >
                            <div className="flex items-center">
                              <div
                                className="w-5 h-5 rounded-full flex items-center justify-center mr-3"
                                style={{ backgroundColor: type.color }}
                              >
                                {IconComponent ? (
                                  <IconComponent className="h-3 w-3 text-white" />
                                ) : (
                                  <span className="text-xs text-white font-bold">
                                    {type.name.charAt(0)}
                                  </span>
                                )}
                              </div>
                              <span className="font-normal block truncate text-gray-900 dark:text-white">
                                {type.name}
                              </span>
                            </div>
                            {isSelected && (
                              <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                                <svg className="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </span>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Date
              </label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>

            {/* Show title field for tasks and notes */}
            {(formData.type === 'task' || formData.type === 'note') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Title
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                />
              </div>
            )}

            {/* Show priority field for tasks */}
            {formData.type === 'task' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Priority
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>
            )}

            {/* Show content field for notes, description for others */}
            {formData.type === 'note' ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Content
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  rows={4}
                  required
                />
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  rows={4}
                  required={formData.type !== 'task'} // Not required for tasks as they have title
                />
              </div>
            )}

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover disabled:opacity-50"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : activity ? 'Update' : 'Add'}
              </button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
