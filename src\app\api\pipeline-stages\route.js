import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

export async function GET() {
  try {
    const pipelineStages = await prisma.pipelineStage.findMany({
      orderBy: {
        order: 'asc'
      }
    });
    return NextResponse.json(pipelineStages);
  } catch (error) {
    console.error('Failed to fetch pipeline stages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pipeline stages' },
      { status: 500 }
    );
  }
}