// Adapted from ChecklistForm.jsx for Tasklist/TasklistItem CRUD
'use client';

import { useState, useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { X, Plus, Trash2 } from 'lucide-react';

export default function TasklistForm({
  contactId,
  tasklist = null,
  isOpen,
  onClose,
  onSuccess
}) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    status: 'in_progress',
    items: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (tasklist) {
      setFormData({
        title: tasklist.title,
        description: tasklist.description || '',
        status: tasklist.status,
        items: tasklist.items || []
      });
    } else {
      setFormData({
        title: '',
        description: '',
        status: 'in_progress',
        items: [{ title: '', description: '', completed: false, priority: 'medium', dueDate: '' }]
      });
    }
  }, [tasklist, isOpen]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');
    const validItems = formData.items.filter(item => item.title.trim() !== '');
    if (!formData.title.trim()) {
      setError('Title is required');
      setIsSubmitting(false);
      return;
    }
    if (validItems.length === 0) {
      setError('At least one task item is required');
      setIsSubmitting(false);
      return;
    }
    try {
      let url, method, body;
      if (tasklist) {
        url = `/api/tasklists/${tasklist.id}`;
        method = 'PATCH';
        body = {
          title: formData.title,
          description: formData.description,
          status: formData.status
        };
        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(body)
        });
        if (!response.ok) throw new Error('Failed to update tasklist');
        let updatedTasklist = await response.json();
        // Update items
        const existingItemsResponse = await fetch(`/api/tasklists/${tasklist.id}/items`);
        const existingItems = await existingItemsResponse.json();
        const existingItemIds = new Set(existingItems.map(item => item.id));
        const formItemIds = new Set(validItems.filter(item => item.id).map(item => item.id));

        // Update or create items
        for (const item of validItems) {
          if (item.id) {
            await fetch(`/api/tasklist-items/${item.id}`, {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                title: item.title,
                description: item.description,
                completed: item.completed,
                priority: item.priority,
                dueDate: item.dueDate
              })
            });
          } else {
            await fetch(`/api/tasklists/${tasklist.id}/items`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                title: item.title,
                description: item.description,
                completed: item.completed,
                priority: item.priority,
                dueDate: item.dueDate
              })
            });
          }
        }

        // Only delete items that were removed by the user (present in existing, not in form)
        for (const itemId of existingItemIds) {
          if (!formItemIds.has(itemId)) {
            await fetch(`/api/tasklist-items/${itemId}`, { method: 'DELETE' });
          }
        }
        const finalResponse = await fetch(`/api/tasklists/${tasklist.id}`);
        updatedTasklist = await finalResponse.json();
        onSuccess(updatedTasklist);
      } else {
        url = `/api/contacts/${contactId}/tasklists`;
        method = 'POST';
        body = {
          title: formData.title,
          description: formData.description,
          status: formData.status,
          items: validItems
        };
        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(body)
        });
        if (!response.ok) throw new Error('Failed to create tasklist');
        const newTasklist = await response.json();
        onSuccess(newTasklist);
      }
      onClose();
    } catch (error) {
      setError(error.message || 'Failed to save tasklist. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addItem = () => {
    setFormData({
      ...formData,
      items: [
        ...formData.items,
        { title: '', description: '', completed: false, priority: 'medium', dueDate: '' }
      ]
    });
  };

  const updateItem = (index, field, value) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };
    setFormData({ ...formData, items: updatedItems });
  };

  const removeItem = (index) => {
    if (formData.items.length <= 1) return;
    const updatedItems = formData.items.filter((_, i) => i !== index);
    setFormData({ ...formData, items: updatedItems });
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-[100]" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[98vh] min-h-[650px] overflow-y-auto z-[110]">
          <div className="flex justify-between items-center mb-4">
            <Dialog.Title className="text-xl font-semibold text-gray-900 dark:text-white">
              {tasklist ? 'Edit Tasklist' : 'Add New Tasklist'}
            </Dialog.Title>
            <Dialog.Close className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <X className="h-5 w-5" />
            </Dialog.Close>
          </div>
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
              {error}
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="flex flex-col gap-2 mb-2">
              <div className="flex flex-col md:flex-row md:items-center gap-2">
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Title *"
                  className="flex-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 py-1 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                />
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                  className="w-40 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 py-1 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="archived">Archived</option>
                </select>
              </div>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Description"
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 py-1 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                rows={1}
              />
            </div>
            <hr className="my-2 border-gray-300 dark:border-gray-600" />
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Tasklist Items *
                </label>
                <button
                  type="button"
                  onClick={addItem}
                  className="flex items-center text-sm text-primary hover:text-primary-hover"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Item
                </button>
              </div>
              <div className="space-y-2 max-h-72 overflow-y-auto p-0.5">
                {formData.items.map((item, index) => (
                  <div key={index} className="flex flex-col gap-1 p-2 border border-gray-200 dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-700/50">
                    <div className="flex items-center gap-2">
                      <input
                        type="text"
                        value={item.title}
                        onChange={(e) => updateItem(index, 'title', e.target.value)}
                        placeholder="Title *"
                        className="flex-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 py-1 text-xs text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                      <input
                        type="date"
                        value={item.dueDate || ''}
                        onChange={(e) => updateItem(index, 'dueDate', e.target.value)}
                        className="w-32 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 py-1 text-xs text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="text"
                        value={item.description || ''}
                        onChange={(e) => updateItem(index, 'description', e.target.value)}
                        placeholder="Description"
                        className="flex-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 py-1 text-xs text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                      <select
                        value={item.priority}
                        onChange={(e) => updateItem(index, 'priority', e.target.value)}
                        className="w-24 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 py-1 text-xs text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                      </select>
                      <label className="flex items-center gap-1 text-xs text-gray-700 dark:text-gray-300">
                        <input
                          type="checkbox"
                          checked={item.completed}
                          onChange={(e) => updateItem(index, 'completed', e.target.checked)}
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                        />
                        Done
                      </label>
                      <button
                        type="button"
                        onClick={() => removeItem(index)}
                        className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-0.5 ml-1"
                        title="Remove item"
                        disabled={formData.items.length <= 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover disabled:opacity-50"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : tasklist ? 'Update' : 'Add'}
              </button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
