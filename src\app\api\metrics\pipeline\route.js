import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma/client';

export async function GET() {
  try {
    const pipelineStats = await prisma.contact.groupBy({
      by: ['pipelineStage'],
      _count: true,
    });

    // Include pipeline stages even if they have no contacts
    const allStages = await prisma.pipelineStage.findMany({
      orderBy: {
        order: 'asc',
      },
    });

    const formattedData = allStages.map(stage => ({
      stage: stage.name,
      count: pipelineStats.find(stat => stat.pipelineStage === stage.id)?._count || 0,
    }));

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Failed to fetch pipeline metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pipeline metrics' },
      { status: 500 }
    );
  }
}