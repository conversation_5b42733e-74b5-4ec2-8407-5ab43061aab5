import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

// GET /api/checklists/[id]/items - Get all items for a checklist
export async function GET(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const checklistId = params.id;
    
    const items = await prisma.checklistItem.findMany({
      where: { checklistId },
      orderBy: { order: 'asc' }
    });
    
    return NextResponse.json(items);
  } catch (error) {
    console.error('Failed to fetch checklist items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch checklist items: ' + error.message },
      { status: 500 }
    );
  }
}

// POST /api/checklists/[id]/items - Add a new item to a checklist
export async function POST(request, context) {
  try {
    // Await the params to fix the Next.js warning
    const { params } = context;
    const checklistId = params.id;
    
    const body = await request.json();
    
    // Validate required fields
    if (!body.text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }
    
    // Get the highest order value to add the new item at the end
    const highestOrderItem = await prisma.checklistItem.findFirst({
      where: { checklistId },
      orderBy: { order: 'desc' }
    });
    
    const newOrder = highestOrderItem ? highestOrderItem.order + 1 : 0;
    
    // Create the checklist item
    const item = await prisma.checklistItem.create({
      data: {
        text: body.text,
        description: body.description,
        completed: body.completed || false,
        completedAt: body.completed ? new Date() : null,
        order: newOrder,
        checklistId
      }
    });
    
    // Check if all items are completed and update the checklist status if needed
    const allItems = await prisma.checklistItem.findMany({
      where: { checklistId }
    });
    
    const allCompleted = allItems.every(item => item.completed);
    
    if (allCompleted) {
      await prisma.checklist.update({
        where: { id: checklistId },
        data: { status: 'completed' }
      });
    }
    
    return NextResponse.json(item);
  } catch (error) {
    console.error('Failed to create checklist item:', error);
    return NextResponse.json(
      { error: 'Failed to create checklist item: ' + error.message },
      { status: 500 }
    );
  }
}
