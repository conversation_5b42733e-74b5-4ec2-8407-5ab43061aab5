
import { useEffect, useState } from 'react';

// Fetches Contact tags from the admin-managed Tag table
export default function useContactTags() {
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    fetch('/api/contacts/tags')
      .then(res => res.ok ? res.json() : { tags: [] })
      .then(data => setTags(data.tags || []))
      .catch(() => setTags([]))
      .finally(() => setLoading(false));
  }, []);

  return { tags, loading };
}
