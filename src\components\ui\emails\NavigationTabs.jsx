"use client";
import { useRouter, useSearchParams } from "next/navigation";

export default function NavigationTabs({ activeTab, setActiveTab }) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleTabChange = (tab) => {
    setActiveTab(tab);

    // Update URL without full page reload
    const params = new URLSearchParams(searchParams);
    params.set("tab", tab);
    router.push(`/emails?${params.toString()}`, { scroll: false });
  };

  return (
    <div className="border-b border-gray-200 dark:border-gray-700">
      <nav className="-mb-px flex space-x-8">
        <button
          className={`py-4 px-1 border-b-2 font-medium text-sm ${
            activeTab === "analytics"
              ? "border-primary text-primary"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
          }`}
          onClick={() => handleTabChange("analytics")}
        >
          Analytics
        </button>
        <button
          className={`py-4 px-1 border-b-2 font-medium text-sm ${
            activeTab === "templates"
              ? "border-primary text-primary"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
          }`}
          onClick={() => handleTabChange("templates")}
        >
          Email Templates
        </button>
        <button
          className={`py-4 px-1 border-b-2 font-medium text-sm ${
            activeTab === "lists"
              ? "border-primary text-primary"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
          }`}
          onClick={() => handleTabChange("lists")}
        >
          Distribution Lists
        </button>
        <button
          className={`py-4 px-1 border-b-2 font-medium text-sm ${
            activeTab === "campaigns"
              ? "border-primary text-primary"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
          }`}
          onClick={() => handleTabChange("campaigns")}
        >
          Campaigns
        </button>
      </nav>
    </div>
  );
}
