"use client";
import { toast } from "react-hot-toast";

export default function ToastTestPage() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginTop: 40 }}>
      <h1 className="text-2xl font-bold mb-4">Toast Test Page</h1>
      <button
        className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-hover"
        onClick={() => toast.success("This is a test toast!")}
      >
        Show Toast
      </button>
    </div>
  );
}
