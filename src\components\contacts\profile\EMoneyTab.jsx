'use client';

import { useState, useEffect } from 'react';
import { Info, ExternalLink, RefreshCw } from 'lucide-react';
import Tooltip from '@/components/ui/Tooltip';

export default function EMoneyTab({ contactId }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [accounts, setAccounts] = useState([]);
  const [lastSynced, setLastSynced] = useState(null);

  // Mock data for demonstration
  const mockAccounts = [
    {
      id: '1',
      accountNumber: '*********',
      accountName: 'Individual Investment Account',
      accountType: 'Brokerage',
      balance: 125750.42,
      lastUpdated: new Date().toISOString()
    },
    {
      id: '2',
      accountNumber: '*********',
      accountName: 'Retirement Account',
      accountType: 'IRA',
      balance: 342890.15,
      lastUpdated: new Date().toISOString()
    },
    {
      id: '3',
      accountNumber: '*********',
      accountName: 'Joint Investment Account',
      accountType: 'Brokerage',
      balance: 78450.33,
      lastUpdated: new Date().toISOString()
    }
  ];

  // Fetch accounts data
  const fetchAccounts = async () => {
    setLoading(true);
    setError('');

    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`/api/contacts/${contactId}/emoney-accounts`);
      // if (!response.ok) throw new Error('Failed to fetch eMoney accounts');
      // const data = await response.json();

      // Using mock data for demonstration
      setTimeout(() => {
        setAccounts(mockAccounts);
        setLastSynced(new Date().toISOString());
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to fetch eMoney accounts:', error);
      setError('Failed to load eMoney accounts. Please try again.');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAccounts();
  }, [contactId]);

  const handleRefresh = () => {
    fetchAccounts();
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Calculate total balance
  const totalBalance = accounts.reduce((sum, account) => sum + account.balance, 0);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow h-full overflow-auto">
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">eMoney Accounts</h3>
            <Tooltip
              content={
                <div className="w-64">
                  <p className="font-medium mb-1">eMoney Integration</p>
                  <p className="text-xs mb-1">Financial accounts synced from eMoney.</p>
                  <p className="text-xs italic font-medium text-amber-500">Currently showing mock data for demonstration purposes.</p>
                </div>
              }
              position="bottom"
            >
              <div className="ml-2 text-gray-500 dark:text-gray-400 cursor-help">
                <Info className="h-4 w-4" />
              </div>
            </Tooltip>
            <div className="ml-2 px-2 py-0.5 text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 rounded-full">
              Mock Data
            </div>
          </div>
          <button
            onClick={handleRefresh}
            className="flex items-center px-3 py-1.5 text-sm bg-primary text-white rounded-lg hover:bg-primary-hover disabled:opacity-50"
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </button>
        </div>

        {error && (
          <div className="p-3 mb-4 bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-md">
            {error}
          </div>
        )}

        {lastSynced && (
          <div className="text-xs text-gray-500 dark:text-gray-400 mb-4">
            Last synced: {formatDate(lastSynced)}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <>
            {/* Total Balance Card */}
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800/30 rounded-lg">
              <div className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-1">Total Balance</div>
              <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                {formatCurrency(totalBalance)}
              </div>
              <div className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                Across {accounts.length} account{accounts.length !== 1 ? 's' : ''}
              </div>
            </div>

            {/* Accounts List */}
            <div className="space-y-4">
              {accounts.map((account) => (
                <div
                  key={account.id}
                  className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow duration-200"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="text-base font-medium text-gray-900 dark:text-white">
                        {account.accountName}
                      </h4>
                      <div className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        Account #: {account.accountNumber.replace(/(\d{4})(\d{4})(\d+)/, '$1-$2-$3')}
                      </div>
                      <div className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                        Type: {account.accountType}
                      </div>
                      <div className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                        Last Updated: {formatDate(account.lastUpdated)}
                      </div>
                      <div className="mt-1 text-xs italic text-amber-500 dark:text-amber-400">
                        Mock data for demonstration
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-gray-900 dark:text-white">
                        {formatCurrency(account.balance)}
                      </div>
                      <a
                        href="#"
                        className="inline-flex items-center mt-2 text-xs text-primary hover:text-primary-hover"
                        onClick={(e) => {
                          e.preventDefault();
                          alert('This would open the account details in eMoney');
                        }}
                      >
                        View in eMoney <ExternalLink className="h-3 w-3 ml-1" />
                      </a>
                    </div>
                  </div>
                </div>
              ))}

              {accounts.length === 0 && !loading && (
                <div className="p-6 text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                  </div>
                  <h3 className="text-base font-medium text-gray-900 dark:text-white mb-1">No eMoney Accounts</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    This contact doesn't have any linked eMoney accounts yet.
                  </p>
                  <p className="text-xs italic text-amber-500 dark:text-amber-400 mb-4">
                    (Mock data for demonstration purposes)
                  </p>
                  <button
                    className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover"
                    onClick={() => alert('This would open the eMoney account linking flow')}
                  >
                    Link eMoney Account
                  </button>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
