function CheckIcon({ className }) {
  return (
    <svg className={className} viewBox="0 0 20 20" fill="currentColor">
      <path
        fillRule="evenodd"
        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export default function Stepper({ steps, currentStep, getStepStatus }) {
  return (
    <div className="flex items-center w-full">
      {steps.map((step, index) => {
        const status = getStepStatus(index);

        return (
          <div
            key={step.id}
            className="flex items-center flex-1 last:flex-none"
          >
            <div className="relative flex flex-col items-center group">
              <div
                className={`
                  w-8 h-8 
                  rounded-full 
                  flex items-center justify-center 
                  transition-colors
                  ${
                    status === "completed"
                      ? "bg-primary text-white"
                      : status === "current"
                      ? "border-2 border-primary text-primary"
                      : status === "disabled"
                      ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                      : "border-2 border-gray-700 text-gray-500"
                  }
                `}
              >
                {status === "completed" ? (
                  <CheckIcon className="w-5 h-5" />
                ) : (
                  <span className="text-sm">{index + 1}</span>
                )}
              </div>
              <span
                className={`
                  absolute -bottom-6 
                  text-sm 
                  whitespace-nowrap
                  ${
                    status === "disabled"
                      ? "text-gray-500"
                      : status === "current"
                      ? "text-primary font-medium"
                      : "text-gray-500"
                  }
                `}
              >
                {step.label}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div
                className={`
                  h-[2px] w-full mx-2
                  ${status === "completed" ? "bg-primary" : "bg-gray-700"}
                `}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}
