import { getSignedInUser, hasPermission } from "@/lib/auth";
import { NextResponse } from "next/server";

/**
 * Middleware to check if the current user has the required permission
 * @param {string} requiredPermission - The permission to check for
 * @returns {Function} - Middleware function
 */
export function withPermission(requiredPermission) {
  return async function (req) {
    try {
      const user = await getSignedInUser(req);

      console.log("WAS THERE A USER: ", user);
      if (!user) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      console.log("THERE WAS!");

      if (!hasPermission(user, requiredPermission)) {
        console.log("UH OH! SOMEONE DIDNT HAS PERMISSION");
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }

      // User has the required permission, continue
      return null;
    } catch (error) {
      console.error("Error in permission middleware:", error);
      return NextResponse.json(
        { error: "Internal Server Error" },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware to check if the current user has any of the required permissions
 * @param {string[]} requiredPermissions - Array of permissions to check for
 * @returns {Function} - Middleware function
 */
export function withAnyPermission(requiredPermissions) {
  return async function (req) {
    try {
      const user = await getSignedInUser(req);

      if (!user) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Admin role has all permissions
      if (user.role === "admin") {
        return null;
      }

      // Check if the user has any of the required permissions
      const hasAnyPermission = requiredPermissions.some((permission) =>
        hasPermission(user, permission)
      );

      if (!hasAnyPermission) {
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }

      // User has at least one of the required permissions, continue
      return null;
    } catch (error) {
      console.error("Error in permission middleware:", error);
      return NextResponse.json(
        { error: "Internal Server Error" },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware to check if the current user has all of the required permissions
 * @param {string[]} requiredPermissions - Array of permissions to check for
 * @returns {Function} - Middleware function
 */
export function withAllPermissions(requiredPermissions) {
  return async function (req) {
    try {
      const user = await getSignedInUser(req);

      if (!user) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Admin role has all permissions
      if (user.role === "admin") {
        return null;
      }

      // Check if the user has all of the required permissions
      const hasAllPermissions = requiredPermissions.every((permission) =>
        hasPermission(user, permission)
      );

      if (!hasAllPermissions) {
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }

      // User has all of the required permissions, continue
      return null;
    } catch (error) {
      console.error("Error in permission middleware:", error);
      return NextResponse.json(
        { error: "Internal Server Error" },
        { status: 500 }
      );
    }
  };
}
