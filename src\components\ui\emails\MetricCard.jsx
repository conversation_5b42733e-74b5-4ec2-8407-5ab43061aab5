import { ArrowUpIcon, ArrowDownIcon } from "lucide-react";

export default function MetricCard({
  title,
  value,
  trend,
  icon: Icon,
  trendReversed = false,
}) {
  const getTrendClassName = (value) => {
    if (value > 0) {
      return trendReversed
        ? "text-red-600 dark:text-red-400"
        : "text-green-600 dark:text-green-400";
    } else if (value < 0) {
      return trendReversed
        ? "text-green-600 dark:text-green-400"
        : "text-red-600 dark:text-red-400";
    }
    return "text-gray-600 dark:text-gray-400";
  };

  const formatTrendValue = (value) => {
    if (!value || isNaN(value)) return "0%";
    return `${Math.abs(value).toFixed(1)}% vs last week`;
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
        {Icon && <Icon className="h-5 w-5" />}
        <span className="text-sm font-medium">{title}</span>
      </div>
      <div className="flex items-end gap-2">
        <span className="text-2xl font-semibold">{value}</span>
        {trend !== 0 && (
          <div
            className={`self-center flex items-center gap-1 text-sm ${getTrendClassName(
              trend
            )}`}
          >
            {trend > 0 ? (
              <ArrowUpIcon className="h-4 w-4" />
            ) : (
              <ArrowDownIcon className="h-4 w-4" />
            )}
            <span>{formatTrendValue(trend)}</span>
          </div>
        )}
      </div>
    </div>
  );
}
