"use client";

import { useState, useEffect } from "react";
import { 
  RefreshCw, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Activity, 
  Target,
  AlertTriangle,
  Calendar,
  Filter,
  Download,
  Eye
} from "lucide-react";
import * as Tabs from "@radix-ui/react-tabs";
import ExecutiveSummaryCards from "./overview/ExecutiveSummaryCards";
import PipelineWaterfallChart from "./overview/PipelineWaterfallChart";
import ContactHealthMatrix from "./overview/ContactHealthMatrix";
import StagePerformanceTable from "./overview/StagePerformanceTable";
import ActivityTrendsChart from "./overview/ActivityTrendsChart";
import RiskAnalysisTable from "./overview/RiskAnalysisTable";

export default function Overview() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeframe, setTimeframe] = useState('30'); // days
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  // Filters
  const [filters, setFilters] = useState({
    startDate: '',
    endDate: '',
    customTimeframe: false
  });

  useEffect(() => {
    fetchOverviewData();
  }, [timeframe, filters]);

  const fetchOverviewData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        timeframe: filters.customTimeframe ? '' : timeframe,
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate })
      });

      const response = await fetch(`/api/admin/overview?${params}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch overview data");
      }

      const overviewData = await response.json();
      setData(overviewData);
    } catch (error) {
      console.error("Error fetching overview data:", error);
      setError(error.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchOverviewData();
  };

  const handleTimeframeChange = (newTimeframe) => {
    setTimeframe(newTimeframe);
    setFilters(prev => ({ ...prev, customTimeframe: false }));
  };

  const handleCustomDateRange = (startDate, endDate) => {
    setFilters(prev => ({
      ...prev,
      startDate,
      endDate,
      customTimeframe: true
    }));
  };

  const exportData = () => {
    if (!data) return;
    
    const exportData = {
      generatedAt: new Date().toISOString(),
      timeframe: filters.customTimeframe ? `${filters.startDate} to ${filters.endDate}` : `${timeframe} days`,
      ...data
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `overview-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading && !data) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading analytics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Overview & Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Comprehensive insights into your CRM performance and contact engagement
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Timeframe Selector */}
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <select
              value={filters.customTimeframe ? 'custom' : timeframe}
              onChange={(e) => {
                if (e.target.value === 'custom') {
                  // Show custom date picker (implement as needed)
                } else {
                  handleTimeframeChange(e.target.value);
                }
              }}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="60">Last 60 days</option>
              <option value="90">Last 90 days</option>
              <option value="custom">Custom range</option>
            </select>
          </div>

          {/* Export Button */}
          <button
            onClick={exportData}
            disabled={!data}
            className="flex items-center space-x-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
          >
            <Download className="h-4 w-4" />
            <span>Export</span>
          </button>

          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center space-x-2 px-3 py-1 bg-primary text-white rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
            <p className="text-red-800 dark:text-red-200">{error}</p>
          </div>
        </div>
      )}

      {data && (
        <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
          {/* Tab Navigation */}
          <Tabs.List className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
            <Tabs.Trigger
              value="overview"
              className="flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-white text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
            >
              Overview
            </Tabs.Trigger>
            <Tabs.Trigger
              value="pipeline"
              className="flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-white text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
            >
              Pipeline Analysis
            </Tabs.Trigger>
            <Tabs.Trigger
              value="contacts"
              className="flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-white text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
            >
              Contact Health
            </Tabs.Trigger>
            <Tabs.Trigger
              value="risk"
              className="flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-white text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
            >
              Risk Analysis
            </Tabs.Trigger>
          </Tabs.List>

          {/* Tab Content */}
          <div className="mt-6">
            {/* Overview Tab */}
            <Tabs.Content value="overview" className="space-y-6">
              {/* Executive Summary Cards */}
              <ExecutiveSummaryCards data={data.executiveSummary} />
              
              {/* Charts Row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <PipelineWaterfallChart data={data.pipelineWaterfall} />
                <ContactHealthMatrix data={data.contactHealth} />
              </div>

              {/* Activity Trends */}
              <ActivityTrendsChart data={data.activityTrends} />
            </Tabs.Content>

            {/* Pipeline Analysis Tab */}
            <Tabs.Content value="pipeline" className="space-y-6">
              <StagePerformanceTable data={data.stagePerformance} />
              <PipelineWaterfallChart data={data.pipelineWaterfall} detailed={true} />
            </Tabs.Content>

            {/* Contact Health Tab */}
            <Tabs.Content value="contacts" className="space-y-6">
              <ContactHealthMatrix data={data.contactHealth} detailed={true} />
              <ActivityTrendsChart data={data.activityTrends} />
            </Tabs.Content>

            {/* Risk Analysis Tab */}
            <Tabs.Content value="risk" className="space-y-6">
              <RiskAnalysisTable data={data.riskAnalysis} />
            </Tabs.Content>
          </div>
        </Tabs.Root>
      )}

      {/* Loading overlay for refresh */}
      {refreshing && data && (
        <div className="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-xl">
            <div className="flex items-center space-x-3">
              <RefreshCw className="h-5 w-5 animate-spin text-primary" />
              <span className="text-gray-900 dark:text-white">Refreshing data...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
